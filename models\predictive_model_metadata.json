{"model_name": "predictive_model", "model_type": "RandomForestClassifier", "best_params": {"max_depth": 5, "n_estimators": 50}, "cv_score": 0.6481636562671046, "test_metrics": {"accuracy": 0.75, "precision": 0.75, "recall": 0.75, "f1_score": 0.7480818414322251, "roc_auc": 0.8030303030303031}, "feature_names": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "category_A_cat2", "category_A_cat3", "category_B_type2"], "target_column": "target", "n_features": 8, "training_data_shape": [200, 8], "random_state": 42, "top_features": [{"feature": "feature_0", "importance": 0.285013838400177}, {"feature": "feature_2", "importance": 0.1963436932310097}, {"feature": "feature_4", "importance": 0.1527485885840723}, {"feature": "feature_3", "importance": 0.15240005639647344}, {"feature": "feature_1", "importance": 0.13465400314457324}, {"feature": "category_A_cat2", "importance": 0.037664875619773934}, {"feature": "category_B_type2", "importance": 0.02256866820628349}, {"feature": "category_A_cat3", "importance": 0.018606276417636913}], "saved_at": "2025-07-24T22:11:25.959344", "model_path": "predictive_model.pkl"}