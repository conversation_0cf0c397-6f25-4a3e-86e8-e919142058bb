{"cells": [{"cell_type": "markdown", "id": "224aa5636ac7adc4", "metadata": {}, "source": ["# 4. Feature Engineering"]}, {"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:33.687179Z", "start_time": "2025-07-05T22:49:33.681627Z"}}, "outputs": [], "source": ["import os\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "68d93330", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:34.021005Z", "start_time": "2025-07-05T22:49:33.718459Z"}}, "outputs": [], "source": ["original_data = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "6fe255cd778f5323", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:34.294463Z", "start_time": "2025-07-05T22:49:34.061675Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "object", "type": "string"}, {"name": "scheduled_day", "rawType": "object", "type": "string"}, {"name": "appointment_day", "rawType": "object", "type": "string"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "object", "type": "string"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "object", "type": "string"}], "ref": "2f6a9aa5-7d59-4d8b-9410-a94ffa19e034", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27", "2016-04-29", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday"], ["1", "733688164476661", "5630279", "F", "2016-04-27", "2016-04-29", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["2", "3449833394123", "5630575", "F", "2016-04-27", "2016-04-29", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["3", "78124564369297", "5629123", "F", "2016-04-27", "2016-04-29", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["4", "734536231958495", "5630213", "F", "2016-04-27", "2016-04-29", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["5", "7542951368435", "5620163", "M", "2016-04-26", "2016-04-29", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday"], ["6", "566654781423437", "5634718", "F", "2016-04-28", "2016-04-29", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["7", "911394617215919", "5636249", "M", "2016-04-28", "2016-04-29", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["8", "99884723334928", "5633951", "F", "2016-04-28", "2016-04-29", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["9", "99948393975", "5620206", "F", "2016-04-26", "2016-04-29", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday"], ["10", "84574392942817", "5633121", "M", "2016-04-28", "2016-04-29", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["11", "14794966191172", "5633460", "F", "2016-04-28", "2016-04-29", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday"], ["12", "17135378245248", "5621836", "F", "2016-04-26", "2016-04-29", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday"], ["13", "622257462899397", "5626083", "F", "2016-04-27", "2016-04-29", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["14", "12154843752835", "5628338", "F", "2016-04-27", "2016-04-29", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["15", "863229818887631", "5616091", "M", "2016-04-25", "2016-04-29", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday"], ["16", "213753979425692", "5634142", "F", "2016-04-28", "2016-04-29", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["17", "5819369978796", "5624020", "M", "2016-04-26", "2016-04-29", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday"], ["18", "12154843752835", "5628345", "F", "2016-04-27", "2016-04-29", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["19", "342815551642", "5628068", "F", "2016-04-27", "2016-04-29", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["20", "311284853849", "5628907", "M", "2016-04-27", "2016-04-29", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["21", "7653516999712", "5616921", "F", "2016-04-25", "2016-04-29", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday"], ["22", "5873315843778", "5609446", "M", "2016-04-20", "2016-04-29", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday"], ["23", "996868412638744", "5635881", "F", "2016-04-28", "2016-04-29", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["24", "822432466381793", "5633339", "F", "2016-04-28", "2016-04-29", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday"], ["25", "25965426543339", "5632906", "F", "2016-04-28", "2016-04-29", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["26", "274164858852", "5635414", "F", "2016-04-28", "2016-04-29", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday"], ["27", "4982378572899", "5635842", "F", "2016-04-28", "2016-04-29", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday"], ["28", "137943696338", "5615608", "M", "2016-04-25", "2016-04-29", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday"], ["29", "589458459955", "5633116", "F", "2016-04-28", "2016-04-29", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday"], ["30", "8545415176986", "5618643", "F", "2016-04-26", "2016-04-29", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday"], ["31", "92235587471561", "5534656", "F", "2016-03-31", "2016-04-29", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday"], ["32", "182717227234941", "5534661", "F", "2016-03-31", "2016-04-29", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday"], ["33", "46946985511333", "5534635", "F", "2016-03-31", "2016-04-29", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday"], ["34", "798756986275976", "5534639", "F", "2016-03-31", "2016-04-29", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday"], ["35", "475118896369823", "5600005", "M", "2016-04-19", "2016-04-29", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday"], ["36", "9291167893717", "5628739", "M", "2016-04-27", "2016-04-29", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["37", "559463646617", "5626971", "F", "2016-04-27", "2016-04-29", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday"], ["38", "36477615234971", "5614045", "F", "2016-04-25", "2016-04-29", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday"], ["39", "236623344873175", "5628286", "M", "2016-04-27", "2016-04-29", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["40", "188517384712787", "5616082", "M", "2016-04-25", "2016-04-29", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday"], ["41", "271881817799985", "5628321", "M", "2016-04-27", "2016-04-29", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday"], ["42", "5434175738686", "5552915", "F", "2016-04-06", "2016-04-29", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday"], ["43", "793821379148", "5552917", "F", "2016-04-06", "2016-04-29", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday"], ["44", "67144894855774", "5552914", "M", "2016-04-06", "2016-04-29", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday"], ["45", "1846317738622", "5552936", "F", "2016-04-06", "2016-04-29", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday"], ["46", "45421316129453", "5552934", "F", "2016-04-06", "2016-04-29", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday"], ["47", "9672968175572", "5597628", "F", "2016-04-18", "2016-04-29", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday"], ["48", "148894173528", "5597632", "F", "2016-04-18", "2016-04-29", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday"], ["49", "6549277227425", "5597643", "M", "2016-04-18", "2016-04-29", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday"]], "shape": {"columns": 16, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 16 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA            0             1         0           0   \n", "1       23      GOIABEIRAS            0             0         0           0   \n", "2       39      GOIABEIRAS            0             0         0           0   \n", "3       19       CONQUISTA            0             0         0           0   \n", "4       30  NOVA PALESTINA            0             0         0           0   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ            0             0         0           0   \n", "71955   51     MARIA ORTIZ            0             0         0           0   \n", "71956   21     MARIA ORTIZ            0             0         0           0   \n", "71957   38     MARIA ORTIZ            0             0         0           0   \n", "71958   54     MARIA ORTIZ            0             0         0           0   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \n", "0             0         False    False          2                  Friday  \n", "1             0         False     True          2                  Friday  \n", "2             0         False     True          2                  Friday  \n", "3             0         False    False          2                  Friday  \n", "4             0         False    False          2                  Friday  \n", "...         ...           ...      ...        ...                     ...  \n", "71954         0          True    False         35                 Tuesday  \n", "71955         0          True    False         35                 Tuesday  \n", "71956         0          True    False         41                 Tuesday  \n", "71957         0          True    False         41                 Tuesday  \n", "71958         0          True    False         41                 Tuesday  \n", "\n", "[71959 rows x 16 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show_processed.csv\")\n", "df_fe"]}, {"cell_type": "code", "execution_count": 4, "id": "3d4d17c5", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:34.445685Z", "start_time": "2025-07-05T22:49:34.406638Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 16 columns):\n", " #   Column                   Non-Null Count  Dtype \n", "---  ------                   --------------  ----- \n", " 0   patient_id               71959 non-null  int64 \n", " 1   appointment_id           71959 non-null  int64 \n", " 2   gender                   71959 non-null  object\n", " 3   scheduled_day            71959 non-null  object\n", " 4   appointment_day          71959 non-null  object\n", " 5   age                      71959 non-null  int64 \n", " 6   neighbourhood            71959 non-null  object\n", " 7   scholarship              71959 non-null  int64 \n", " 8   hypertension             71959 non-null  int64 \n", " 9   diabetes                 71959 non-null  int64 \n", " 10  alcoholism               71959 non-null  int64 \n", " 11  handicap                 71959 non-null  int64 \n", " 12  sms_received             71959 non-null  bool  \n", " 13  no_show                  71959 non-null  bool  \n", " 14  lead_time                71959 non-null  int64 \n", " 15  appointment_day_of_week  71959 non-null  object\n", "dtypes: bool(2), int64(9), object(5)\n", "memory usage: 7.8+ MB\n"]}], "source": ["df_fe.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "8e5c2cdb", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:34.725291Z", "start_time": "2025-07-05T22:49:34.568690Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 16 columns):\n", " #   Column                   Non-Null Count  Dtype         \n", "---  ------                   --------------  -----         \n", " 0   patient_id               71959 non-null  int64         \n", " 1   appointment_id           71959 non-null  int64         \n", " 2   gender                   71959 non-null  category      \n", " 3   scheduled_day            71959 non-null  datetime64[ns]\n", " 4   appointment_day          71959 non-null  datetime64[ns]\n", " 5   age                      71959 non-null  int64         \n", " 6   neighbourhood            71959 non-null  category      \n", " 7   scholarship              71959 non-null  int64         \n", " 8   hypertension             71959 non-null  int64         \n", " 9   diabetes                 71959 non-null  int64         \n", " 10  alcoholism               71959 non-null  int64         \n", " 11  handicap                 71959 non-null  int64         \n", " 12  sms_received             71959 non-null  bool          \n", " 13  no_show                  71959 non-null  bool          \n", " 14  lead_time                71959 non-null  int64         \n", " 15  appointment_day_of_week  71959 non-null  category      \n", "dtypes: bool(2), category(3), datetime64[ns](2), int64(9)\n", "memory usage: 6.4 MB\n"]}], "source": ["# Convert `gender`, `appointment_day_of_week` to categorical type\n", "for col in ['gender', 'neighbourhood', 'appointment_day_of_week']:\n", "    df_fe[col] = df_fe[col].astype('category')\n", "\n", "# Convert `scheduled_day` and `appointment_day` to datetime\n", "for col in ['scheduled_day', 'appointment_day']:\n", "    df_fe[col] = pd.to_datetime(df_fe[col]).dt.date.astype('datetime64[ns]')\n", "\n", "# Convert `scholarship`, `hypertension`, `diabetes`, `alcoholism`, `handicap` to boolean type\n", "for col in ['scholarship', 'hypertension', 'diabetes', 'alcoholism', 'handicap']:\n", "    df_fe[col] = df_fe[col].astype('int64')\n", "    \n", "df_fe.info()"]}, {"cell_type": "code", "execution_count": 6, "id": "1c76fbc271e543ab", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:34.839944Z", "start_time": "2025-07-05T22:49:34.828481Z"}}, "outputs": [{"data": {"text/plain": ["Index(['patient_id', 'appointment_id', 'gender', 'scheduled_day',\n", "       'appointment_day', 'age', 'neighbourhood', 'scholarship',\n", "       'hypertension', 'diabetes', 'alcoholism', 'handicap', 'sms_received',\n", "       'no_show', 'lead_time', 'appointment_day_of_week'],\n", "      dtype='object')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe.columns"]}, {"cell_type": "markdown", "id": "5182891c", "metadata": {}, "source": ["### Create new features"]}, {"cell_type": "markdown", "id": "0d35da3a", "metadata": {}, "source": ["1. Extract the hour of the day when the appointment was scheduled"]}, {"cell_type": "code", "execution_count": 7, "id": "e81b50c5", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:35.333076Z", "start_time": "2025-07-05T22:49:34.965555Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}], "ref": "1c7feef4-2591-481a-a26a-db0975c2e21b", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17"]], "shape": {"columns": 17, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 17 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA            0             1         0           0   \n", "1       23      GOIABEIRAS            0             0         0           0   \n", "2       39      GOIABEIRAS            0             0         0           0   \n", "3       19       CONQUISTA            0             0         0           0   \n", "4       30  NOVA PALESTINA            0             0         0           0   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ            0             0         0           0   \n", "71955   51     MARIA ORTIZ            0             0         0           0   \n", "71956   21     MARIA ORTIZ            0             0         0           0   \n", "71957   38     MARIA ORTIZ            0             0         0           0   \n", "71958   54     MARIA ORTIZ            0             0         0           0   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0             0         False    False          2                  Friday   \n", "1             0         False     True          2                  Friday   \n", "2             0         False     True          2                  Friday   \n", "3             0         False    False          2                  Friday   \n", "4             0         False    False          2                  Friday   \n", "...         ...           ...      ...        ...                     ...   \n", "71954         0          True    False         35                 Tuesday   \n", "71955         0          True    False         35                 Tuesday   \n", "71956         0          True    False         41                 Tuesday   \n", "71957         0          True    False         41                 Tuesday   \n", "71958         0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours  \n", "0                   18  \n", "1                   16  \n", "2                   16  \n", "3                   17  \n", "4                   16  \n", "...                ...  \n", "71954               13  \n", "71955               16  \n", "71956                7  \n", "71957                7  \n", "71958                7  \n", "\n", "[71959 rows x 17 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['scheduled_hours'] = pd.to_datetime(original_data['ScheduledDay']).dt.hour.astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "e800a365", "metadata": {}, "source": ["2. Create Age Groups"]}, {"cell_type": "code", "execution_count": 8, "id": "5aca5616", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:35.485456Z", "start_time": "2025-07-05T22:49:35.444920Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}], "ref": "48a28e32-91dc-40a5-b7a4-3243c6205936", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35"]], "shape": {"columns": 18, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 18 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA            0             1         0           0   \n", "1       23      GOIABEIRAS            0             0         0           0   \n", "2       39      GOIABEIRAS            0             0         0           0   \n", "3       19       CONQUISTA            0             0         0           0   \n", "4       30  NOVA PALESTINA            0             0         0           0   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ            0             0         0           0   \n", "71955   51     MARIA ORTIZ            0             0         0           0   \n", "71956   21     MARIA ORTIZ            0             0         0           0   \n", "71957   38     MARIA ORTIZ            0             0         0           0   \n", "71958   54     MARIA ORTIZ            0             0         0           0   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0             0         False    False          2                  Friday   \n", "1             0         False     True          2                  Friday   \n", "2             0         False     True          2                  Friday   \n", "3             0         False    False          2                  Friday   \n", "4             0         False    False          2                  Friday   \n", "...         ...           ...      ...        ...                     ...   \n", "71954         0          True    False         35                 Tuesday   \n", "71955         0          True    False         35                 Tuesday   \n", "71956         0          True    False         41                 Tuesday   \n", "71957         0          True    False         41                 Tuesday   \n", "71958         0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  \n", "0                   18       66+  \n", "1                   16     19-35  \n", "2                   16     36-60  \n", "3                   17     19-35  \n", "4                   16     19-35  \n", "...                ...       ...  \n", "71954               13     51-65  \n", "71955               16     51-65  \n", "71956                7     19-35  \n", "71957                7     36-60  \n", "71958                7     51-65  \n", "\n", "[71959 rows x 18 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['age_group'] = pd.cut(df_fe['age'], bins=[0, 18, 35, 50, 65, 100], labels=['0-18', '19-35', '36-60', '51-65', '66+'])\n", "df_fe"]}, {"cell_type": "markdown", "id": "4d394cde", "metadata": {}, "source": ["3. Create a new column for the total number of chronic conditions"]}, {"cell_type": "code", "execution_count": 9, "id": "6c3d9771", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:35.660149Z", "start_time": "2025-07-05T22:49:35.653338Z"}}, "outputs": [], "source": ["# df_fe['hypertension'] = df_fe['hypertension'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['hypertension'].astype('int64')\n", "\n", "# df_fe['diabetes'] = df_fe['diabetes'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['alcoholism'] = df_fe['alcoholism'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['handicap'] = df_fe['handicap'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['total_chronic_conditions'] = df_fe[['hypertension', 'diabetes', 'alcoholism', 'handicap']].sum(axis=1)\n", "# df_fe"]}, {"cell_type": "markdown", "id": "649474b9", "metadata": {}, "source": ["4. Create a new column to indicate if the appointment is on a weekend"]}, {"cell_type": "code", "execution_count": 10, "id": "95cf8f8c", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:36.032461Z", "start_time": "2025-07-05T22:49:35.968174Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}], "ref": "ed39932b-6967-44ef-8d4b-13d94a0b5678", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+", "0"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35", "0"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60", "0"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35", "0"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35", "0"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35", "0"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65", "0"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18", "0"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60", "0"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60", "0"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35", "0"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35", "0"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18", "0"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18", "0"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60", "0"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60", "0"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18", "0"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60", "0"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60", "0"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+", "0"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65", "0"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+", "0"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+", "0"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35", "0"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65", "0"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65", "0"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35", "0"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35", "0"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18", "0"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18", "0"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18", "0"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null, "0"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+", "0"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65", "0"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65", "0"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35", "0"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+", "0"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65", "0"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65", "0"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35", "0"]], "shape": {"columns": 19, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 19 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA            0             1         0           0   \n", "1       23      GOIABEIRAS            0             0         0           0   \n", "2       39      GOIABEIRAS            0             0         0           0   \n", "3       19       CONQUISTA            0             0         0           0   \n", "4       30  NOVA PALESTINA            0             0         0           0   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ            0             0         0           0   \n", "71955   51     MARIA ORTIZ            0             0         0           0   \n", "71956   21     MARIA ORTIZ            0             0         0           0   \n", "71957   38     MARIA ORTIZ            0             0         0           0   \n", "71958   54     MARIA ORTIZ            0             0         0           0   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0             0         False    False          2                  Friday   \n", "1             0         False     True          2                  Friday   \n", "2             0         False     True          2                  Friday   \n", "3             0         False    False          2                  Friday   \n", "4             0         False    False          2                  Friday   \n", "...         ...           ...      ...        ...                     ...   \n", "71954         0          True    False         35                 Tuesday   \n", "71955         0          True    False         35                 Tuesday   \n", "71956         0          True    False         41                 Tuesday   \n", "71957         0          True    False         41                 Tuesday   \n", "71958         0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend  \n", "0                   18       66+           0  \n", "1                   16     19-35           0  \n", "2                   16     36-60           0  \n", "3                   17     19-35           0  \n", "4                   16     19-35           0  \n", "...                ...       ...         ...  \n", "71954               13     51-65           0  \n", "71955               16     51-65           0  \n", "71956                7     19-35           0  \n", "71957                7     36-60           0  \n", "71958                7     51-65           0  \n", "\n", "[71959 rows x 19 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['is_weekend'] = pd.to_datetime(df_fe['appointment_day']).dt.dayofweek.isin([5, 6]).astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "a0309b21", "metadata": {}, "source": ["5. Create a new feature for SMS Reminders"]}, {"cell_type": "code", "execution_count": 11, "id": "9c1844fd", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:36.296222Z", "start_time": "2025-07-05T22:49:36.257741Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "received_sms", "rawType": "object", "type": "string"}], "ref": "2433908c-5fe9-4043-9e2e-b509ac889202", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+", "0", "No"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35", "0", "No"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60", "0", "No"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35", "0", "No"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35", "0", "No"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35", "0", "Yes"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65", "0", "No"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18", "0", "Yes"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60", "0", "No"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60", "0", "No"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35", "0", "Yes"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35", "0", "No"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18", "0", "No"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18", "0", "Yes"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60", "0", "No"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60", "0", "Yes"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18", "0", "No"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60", "0", "No"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60", "0", "Yes"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+", "0", "Yes"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65", "0", "No"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+", "0", "No"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+", "0", "No"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35", "0", "No"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65", "0", "Yes"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65", "0", "Yes"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35", "0", "Yes"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35", "0", "Yes"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18", "0", "Yes"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18", "0", "No"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18", "0", "Yes"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null, "0", "Yes"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+", "0", "Yes"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65", "0", "Yes"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65", "0", "No"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35", "0", "Yes"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+", "0", "Yes"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65", "0", "Yes"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65", "0", "No"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35", "0", "No"]], "shape": {"columns": 20, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 20 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA            0             1         0           0   \n", "1       23      GOIABEIRAS            0             0         0           0   \n", "2       39      GOIABEIRAS            0             0         0           0   \n", "3       19       CONQUISTA            0             0         0           0   \n", "4       30  NOVA PALESTINA            0             0         0           0   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ            0             0         0           0   \n", "71955   51     MARIA ORTIZ            0             0         0           0   \n", "71956   21     MARIA ORTIZ            0             0         0           0   \n", "71957   38     MARIA ORTIZ            0             0         0           0   \n", "71958   54     MARIA ORTIZ            0             0         0           0   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0             0         False    False          2                  Friday   \n", "1             0         False     True          2                  Friday   \n", "2             0         False     True          2                  Friday   \n", "3             0         False    False          2                  Friday   \n", "4             0         False    False          2                  Friday   \n", "...         ...           ...      ...        ...                     ...   \n", "71954         0          True    False         35                 Tuesday   \n", "71955         0          True    False         35                 Tuesday   \n", "71956         0          True    False         41                 Tuesday   \n", "71957         0          True    False         41                 Tuesday   \n", "71958         0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend received_sms  \n", "0                   18       66+           0           No  \n", "1                   16     19-35           0           No  \n", "2                   16     36-60           0           No  \n", "3                   17     19-35           0           No  \n", "4                   16     19-35           0           No  \n", "...                ...       ...         ...          ...  \n", "71954               13     51-65           0          Yes  \n", "71955               16     51-65           0          Yes  \n", "71956                7     19-35           0          Yes  \n", "71957                7     36-60           0          Yes  \n", "71958                7     51-65           0          Yes  \n", "\n", "[71959 rows x 20 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['received_sms'] = df_fe['sms_received'].map({False: 'No', True: 'Yes'})\n", "df_fe"]}, {"cell_type": "markdown", "id": "a550732d", "metadata": {}, "source": ["6. Create a feature for the number of day until the next appointment."]}, {"cell_type": "code", "execution_count": 12, "id": "92068b1d", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:36.563429Z", "start_time": "2025-07-05T22:49:36.470918Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "received_sms", "rawType": "object", "type": "string"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}], "ref": "9c2e37f9-4a23-4996-b277-b0768a8872e9", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+", "0", "No", "33"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35", "0", "No", "0"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60", "0", "No", "20"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35", "0", "No", "0"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35", "0", "No", "0"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35", "0", "Yes", "0"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65", "0", "No", "0"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18", "0", "Yes", "0"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60", "0", "No", "0"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60", "0", "No", "0"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35", "0", "Yes", "0"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35", "0", "No", "32"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18", "0", "No", "0"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18", "0", "Yes", "0"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60", "0", "No", "31"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60", "0", "Yes", "0"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18", "0", "No", "6"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60", "0", "No", "0"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60", "0", "Yes", "0"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+", "0", "Yes", "14"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65", "0", "No", "33"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+", "0", "No", "40"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "0"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+", "0", "No", "0"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35", "0", "No", "0"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65", "0", "Yes", "13"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "32"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65", "0", "Yes", "0"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35", "0", "Yes", "38"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "0"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35", "0", "Yes", "38"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "6"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18", "0", "Yes", "0"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18", "0", "No", "10"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18", "0", "Yes", "0"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null, "0", "Yes", "32"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+", "0", "Yes", "32"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65", "0", "Yes", "0"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65", "0", "No", "0"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35", "0", "Yes", "20"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+", "0", "Yes", "35"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65", "0", "Yes", "0"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65", "0", "No", "26"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35", "0", "No", "0"]], "shape": {"columns": 21, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 21 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  \\\n", "0       76       REPÚBLICA            0             1         0  ...   \n", "1       23      GOIABEIRAS            0             0         0  ...   \n", "2       39      GOIABEIRAS            0             0         0  ...   \n", "3       19       CONQUISTA            0             0         0  ...   \n", "4       30  NOVA PALESTINA            0             0         0  ...   \n", "...    ...             ...          ...           ...       ...  ...   \n", "71954   56     MARIA ORTIZ            0             0         0  ...   \n", "71955   51     MARIA ORTIZ            0             0         0  ...   \n", "71956   21     MARIA ORTIZ            0             0         0  ...   \n", "71957   38     MARIA ORTIZ            0             0         0  ...   \n", "71958   54     MARIA ORTIZ            0             0         0  ...   \n", "\n", "       handicap  sms_received  no_show  lead_time  appointment_day_of_week  \\\n", "0             0         False    False          2                   Friday   \n", "1             0         False     True          2                   Friday   \n", "2             0         False     True          2                   Friday   \n", "3             0         False    False          2                   Friday   \n", "4             0         False    False          2                   Friday   \n", "...         ...           ...      ...        ...                      ...   \n", "71954         0          True    False         35                  Tuesday   \n", "71955         0          True    False         35                  Tuesday   \n", "71956         0          True    False         41                  Tuesday   \n", "71957         0          True    False         41                  Tuesday   \n", "71958         0          True    False         41                  Tuesday   \n", "\n", "      scheduled_hours  age_group is_weekend  received_sms  \\\n", "0                  18        66+          0            No   \n", "1                  16      19-35          0            No   \n", "2                  16      36-60          0            No   \n", "3                  17      19-35          0            No   \n", "4                  16      19-35          0            No   \n", "...               ...        ...        ...           ...   \n", "71954              13      51-65          0           Yes   \n", "71955              16      51-65          0           Yes   \n", "71956               7      19-35          0           Yes   \n", "71957               7      36-60          0           Yes   \n", "71958               7      51-65          0           Yes   \n", "\n", "      days_until_next_appointment  \n", "0                              33  \n", "1                               0  \n", "2                              20  \n", "3                               0  \n", "4                               0  \n", "...                           ...  \n", "71954                           0  \n", "71955                           0  \n", "71956                           0  \n", "71957                           0  \n", "71958                           0  \n", "\n", "[71959 rows x 21 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['days_until_next_appointment'] = (df_fe.groupby('patient_id')['appointment_day'].shift(-1) - df_fe['appointment_day']).dt.days.fillna(0).astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "cec9c2ef", "metadata": {}, "source": ["7. Create a feature for the number of previous no-show by the patient"]}, {"cell_type": "code", "execution_count": 13, "id": "5f6b5371", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:36.892926Z", "start_time": "2025-07-05T22:49:36.788166Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "received_sms", "rawType": "object", "type": "string"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}], "ref": "889adf12-1c2c-400c-9ac0-38a725196512", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+", "0", "No", "33", "0"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35", "0", "No", "0", "0"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60", "0", "No", "20", "0"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35", "0", "No", "0", "0"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35", "0", "No", "0", "0"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35", "0", "Yes", "0", "0"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0", "0"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0", "0"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65", "0", "No", "0", "0"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18", "0", "Yes", "0", "0"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60", "0", "No", "0", "0"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60", "0", "No", "0", "0"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35", "0", "Yes", "0", "0"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35", "0", "No", "32", "0"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18", "0", "No", "0", "0"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18", "0", "Yes", "0", "0"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60", "0", "No", "31", "0"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60", "0", "Yes", "0", "0"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18", "0", "No", "6", "1"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60", "0", "No", "0", "0"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0", "0"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60", "0", "Yes", "0", "0"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+", "0", "Yes", "14", "0"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65", "0", "No", "33", "0"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+", "0", "No", "40", "0"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "0", "0"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+", "0", "No", "0", "0"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35", "0", "No", "0", "0"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65", "0", "Yes", "13", "0"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "32", "0"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65", "0", "Yes", "0", "0"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35", "0", "Yes", "38", "0"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "0", "0"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35", "0", "Yes", "38", "0"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "6", "0"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18", "0", "Yes", "0", "0"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18", "0", "No", "10", "0"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0", "0"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18", "0", "Yes", "0", "0"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0", "0"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null, "0", "Yes", "32", "0"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0", "0"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+", "0", "Yes", "32", "0"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65", "0", "Yes", "0", "0"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65", "0", "No", "0", "0"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35", "0", "Yes", "20", "0"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+", "0", "Yes", "35", "0"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65", "0", "Yes", "0", "0"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65", "0", "No", "26", "0"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35", "0", "No", "0", "0"]], "shape": {"columns": 22, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "      <th>previous_no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 22 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  \\\n", "0       76       REPÚBLICA            0             1         0  ...   \n", "1       23      GOIABEIRAS            0             0         0  ...   \n", "2       39      GOIABEIRAS            0             0         0  ...   \n", "3       19       CONQUISTA            0             0         0  ...   \n", "4       30  NOVA PALESTINA            0             0         0  ...   \n", "...    ...             ...          ...           ...       ...  ...   \n", "71954   56     MARIA ORTIZ            0             0         0  ...   \n", "71955   51     MARIA ORTIZ            0             0         0  ...   \n", "71956   21     MARIA ORTIZ            0             0         0  ...   \n", "71957   38     MARIA ORTIZ            0             0         0  ...   \n", "71958   54     MARIA ORTIZ            0             0         0  ...   \n", "\n", "       sms_received  no_show  lead_time  appointment_day_of_week  \\\n", "0             False    False          2                   Friday   \n", "1             False     True          2                   Friday   \n", "2             False     True          2                   Friday   \n", "3             False    False          2                   Friday   \n", "4             False    False          2                   Friday   \n", "...             ...      ...        ...                      ...   \n", "71954          True    False         35                  Tuesday   \n", "71955          True    False         35                  Tuesday   \n", "71956          True    False         41                  Tuesday   \n", "71957          True    False         41                  Tuesday   \n", "71958          True    False         41                  Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend received_sms  \\\n", "0                   18       66+           0           No   \n", "1                   16     19-35           0           No   \n", "2                   16     36-60           0           No   \n", "3                   17     19-35           0           No   \n", "4                   16     19-35           0           No   \n", "...                ...       ...         ...          ...   \n", "71954               13     51-65           0          Yes   \n", "71955               16     51-65           0          Yes   \n", "71956                7     19-35           0          Yes   \n", "71957                7     36-60           0          Yes   \n", "71958                7     51-65           0          Yes   \n", "\n", "       days_until_next_appointment previous_no_show  \n", "0                               33                0  \n", "1                                0                0  \n", "2                               20                0  \n", "3                                0                0  \n", "4                                0                0  \n", "...                            ...              ...  \n", "71954                            0                1  \n", "71955                            0                1  \n", "71956                            0                0  \n", "71957                            0                0  \n", "71958                            0                0  \n", "\n", "[71959 rows x 22 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['previous_no_show'] = df_fe.groupby('patient_id')['no_show'].cumsum() - df_fe['no_show']\n", "df_fe"]}, {"cell_type": "markdown", "id": "fc837eca", "metadata": {}, "source": ["8. Create a feature for the total number of appointments per patient"]}, {"cell_type": "code", "execution_count": 14, "id": "2e2a2695", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:37.320735Z", "start_time": "2025-07-05T22:49:37.223505Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "appointment_day_of_week", "rawType": "category", "type": "unknown"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "age_group", "rawType": "category", "type": "unknown"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "received_sms", "rawType": "object", "type": "string"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}, {"name": "total_appointments", "rawType": "int64", "type": "integer"}], "ref": "4306109f-f042-47fb-8573-dee2da575499", "rows": [["0", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "Friday", "18", "66+", "0", "No", "33", "0", "2"], ["1", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "19-35", "0", "No", "0", "0", "1"], ["2", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "16", "36-60", "0", "No", "20", "0", "2"], ["3", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "17", "19-35", "0", "No", "0", "0", "1"], ["4", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "16", "19-35", "0", "No", "0", "0", "1"], ["5", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "8", "19-35", "0", "Yes", "0", "0", "1"], ["6", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0", "0", "1"], ["7", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "19-35", "0", "No", "0", "0", "1"], ["8", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "51-65", "0", "No", "0", "0", "1"], ["9", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "Friday", "12", "0-18", "0", "Yes", "0", "0", "1"], ["10", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "14", "36-60", "0", "No", "0", "0", "1"], ["11", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "Friday", "8", "36-60", "0", "No", "0", "0", "1"], ["12", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "Friday", "11", "19-35", "0", "Yes", "0", "0", "1"], ["13", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "14", "19-35", "0", "No", "32", "0", "2"], ["14", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "10", "0-18", "0", "No", "0", "0", "6"], ["15", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "Friday", "8", "0-18", "0", "Yes", "0", "0", "1"], ["16", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "8", "36-60", "0", "No", "31", "0", "2"], ["17", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "Friday", "9", "36-60", "0", "Yes", "0", "0", "1"], ["18", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "0-18", "0", "No", "6", "1", "6"], ["19", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "10", "36-60", "0", "No", "0", "0", "1"], ["20", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0", "0", "1"], ["21", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "10", "36-60", "0", "Yes", "0", "0", "1"], ["22", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "Friday", "13", "66+", "0", "Yes", "14", "0", "2"], ["23", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "51-65", "0", "No", "33", "0", "2"], ["24", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "Friday", "14", "66+", "0", "No", "40", "0", "2"], ["25", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "0", "0", "1"], ["26", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "Friday", "14", "66+", "0", "No", "0", "0", "1"], ["27", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "Friday", "10", "19-35", "0", "No", "0", "0", "1"], ["28", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "Friday", "15", "51-65", "0", "Yes", "13", "0", "3"], ["29", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "Friday", "15", "36-60", "0", "No", "32", "0", "7"], ["30", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "Friday", "10", "51-65", "0", "Yes", "0", "0", "1"], ["31", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "12", "19-35", "0", "Yes", "38", "0", "2"], ["32", "182717227234941", "5534661", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "0", "0", "1"], ["33", "46946985511333", "5534635", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "15", "19-35", "0", "Yes", "38", "0", "2"], ["34", "798756986275976", "5534639", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "Friday", "7", "19-35", "0", "Yes", "6", "0", "5"], ["35", "475118896369823", "5600005", "M", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "Friday", "7", "0-18", "0", "Yes", "0", "0", "1"], ["36", "9291167893717", "5628739", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "Friday", "7", "0-18", "0", "No", "10", "0", "7"], ["37", "559463646617", "5626971", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "Friday", "7", "0-18", "0", "No", "0", "0", "1"], ["38", "36477615234971", "5614045", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "Friday", "15", "0-18", "0", "Yes", "0", "0", "1"], ["39", "236623344873175", "5628286", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0", "0", "1"], ["40", "188517384712787", "5616082", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "Friday", "14", null, "0", "Yes", "32", "0", "2"], ["41", "271881817799985", "5628321", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "Friday", "9", null, "0", "No", "0", "0", "1"], ["42", "5434175738686", "5552915", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "Friday", "8", "66+", "0", "Yes", "32", "0", "2"], ["43", "793821379148", "5552917", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "51-65", "0", "Yes", "0", "0", "1"], ["44", "67144894855774", "5552914", "M", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "Friday", "13", "51-65", "0", "No", "0", "0", "1"], ["45", "1846317738622", "5552936", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "Friday", "14", "19-35", "0", "Yes", "20", "0", "3"], ["46", "45421316129453", "5552934", "F", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "Friday", "12", "66+", "0", "Yes", "35", "0", "2"], ["47", "9672968175572", "5597628", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "Friday", "8", "51-65", "0", "Yes", "0", "0", "1"], ["48", "148894173528", "5597632", "F", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "Friday", "7", "51-65", "0", "No", "26", "0", "2"], ["49", "6549277227425", "5597643", "M", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "Friday", "17", "19-35", "0", "No", "0", "0", "1"]], "shape": {"columns": 23, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "      <th>previous_no_show</th>\n", "      <th>total_appointments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 23 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  no_show  \\\n", "0       76       REPÚBLICA            0             1         0  ...    False   \n", "1       23      GOIABEIRAS            0             0         0  ...     True   \n", "2       39      GOIABEIRAS            0             0         0  ...     True   \n", "3       19       CONQUISTA            0             0         0  ...    False   \n", "4       30  NOVA PALESTINA            0             0         0  ...    False   \n", "...    ...             ...          ...           ...       ...  ...      ...   \n", "71954   56     MARIA ORTIZ            0             0         0  ...    False   \n", "71955   51     MARIA ORTIZ            0             0         0  ...    False   \n", "71956   21     MARIA ORTIZ            0             0         0  ...    False   \n", "71957   38     MARIA ORTIZ            0             0         0  ...    False   \n", "71958   54     MARIA ORTIZ            0             0         0  ...    False   \n", "\n", "       lead_time  appointment_day_of_week  scheduled_hours  age_group  \\\n", "0              2                   Friday               18        66+   \n", "1              2                   Friday               16      19-35   \n", "2              2                   Friday               16      36-60   \n", "3              2                   Friday               17      19-35   \n", "4              2                   Friday               16      19-35   \n", "...          ...                      ...              ...        ...   \n", "71954         35                  Tuesday               13      51-65   \n", "71955         35                  Tuesday               16      51-65   \n", "71956         41                  Tuesday                7      19-35   \n", "71957         41                  Tuesday                7      36-60   \n", "71958         41                  Tuesday                7      51-65   \n", "\n", "      is_weekend  received_sms days_until_next_appointment  previous_no_show  \\\n", "0              0            No                          33                 0   \n", "1              0            No                           0                 0   \n", "2              0            No                          20                 0   \n", "3              0            No                           0                 0   \n", "4              0            No                           0                 0   \n", "...          ...           ...                         ...               ...   \n", "71954          0           Yes                           0                 1   \n", "71955          0           Yes                           0                 1   \n", "71956          0           Yes                           0                 0   \n", "71957          0           Yes                           0                 0   \n", "71958          0           Yes                           0                 0   \n", "\n", "      total_appointments  \n", "0                      2  \n", "1                      1  \n", "2                      2  \n", "3                      1  \n", "4                      1  \n", "...                  ...  \n", "71954                  2  \n", "71955                  3  \n", "71956                  1  \n", "71957                  2  \n", "71958                  2  \n", "\n", "[71959 rows x 23 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['total_appointments'] = df_fe.groupby('patient_id')['appointment_id'].transform('count').astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "751a59b3", "metadata": {}, "source": ["9. Encode categorical variables as needed for modeling"]}, {"cell_type": "code", "execution_count": 15, "id": "3ce35d4f", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:37.635040Z", "start_time": "2025-07-05T22:49:37.532400Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}, {"name": "total_appointments", "rawType": "int64", "type": "integer"}, {"name": "gender_M", "rawType": "bool", "type": "boolean"}, {"name": "age_group_19-35", "rawType": "bool", "type": "boolean"}, {"name": "age_group_36-60", "rawType": "bool", "type": "boolean"}, {"name": "age_group_51-65", "rawType": "bool", "type": "boolean"}, {"name": "age_group_66+", "rawType": "bool", "type": "boolean"}, {"name": "received_sms_Yes", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Monday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Saturday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Thursday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Tuesday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Wednesday", "rawType": "bool", "type": "boolean"}], "ref": "b9151999-1277-41ba-813a-f993c6764266", "rows": [["0", "95985133231274", "5626772", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False", "2", "18", "0", "33", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"], ["1", "733688164476661", "5630279", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["2", "3449833394123", "5630575", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "20", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["3", "78124564369297", "5629123", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "17", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["4", "734536231958495", "5630213", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["5", "7542951368435", "5620163", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "3", "8", "0", "0", "0", "1", "True", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["6", "566654781423437", "5634718", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["7", "911394617215919", "5636249", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["8", "99884723334928", "5633951", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False"], ["9", "99948393975", "5620206", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False", "3", "12", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["10", "84574392942817", "5633121", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "1", "14", "0", "0", "0", "1", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["11", "14794966191172", "5633460", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True", "1", "8", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["12", "17135378245248", "5621836", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False", "3", "11", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["13", "622257462899397", "5626083", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "14", "0", "32", "0", "2", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["14", "12154843752835", "5628338", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True", "2", "10", "0", "0", "0", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["15", "863229818887631", "5616091", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True", "4", "8", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["16", "213753979425692", "5634142", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "31", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["17", "5819369978796", "5624020", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False", "3", "9", "0", "0", "0", "1", "True", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False"], ["18", "12154843752835", "5628345", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "6", "1", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["19", "342815551642", "5628068", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["20", "311284853849", "5628907", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["21", "7653516999712", "5616921", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False", "4", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False"], ["22", "5873315843778", "5609446", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False", "9", "13", "0", "14", "0", "2", "True", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False"], ["23", "996868412638744", "5635881", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "33", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False"], ["24", "822432466381793", "5633339", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False", "1", "14", "0", "40", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"], ["25", "25965426543339", "5632906", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["26", "274164858852", "5635414", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True", "1", "14", "0", "0", "0", "1", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False"], ["27", "4982378572899", "5635842", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["28", "137943696338", "5615608", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False", "4", "15", "0", "13", "0", "3", "True", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False"], ["29", "589458459955", "5633116", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False", "1", "15", "0", "32", "0", "7", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False"], ["30", "8545415176986", "5618643", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True", "3", "10", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False"], ["31", "92235587471561", "5534656", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "12", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["32", "182717227234941", "5534661", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "19", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True", "29", "7", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["33", "46946985511333", "5534635", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "15", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["34", "798756986275976", "5534639", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "23", "GRANDE VITÓRIA", "1", "0", "0", "0", "0", "True", "True", "29", "7", "0", "6", "0", "5", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["35", "475118896369823", "5600005", "2016-04-19 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True", "10", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["36", "9291167893717", "5628739", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "8", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False", "2", "7", "0", "10", "0", "7", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["37", "559463646617", "5626971", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "2", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["38", "36477615234971", "5614045", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "3", "CONQUISTA", "1", "0", "0", "0", "0", "True", "False", "4", "15", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["39", "236623344873175", "5628286", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "SÃO BENEDITO", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["40", "188517384712787", "5616082", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "0", "ILHA DAS CAIEIRAS", "0", "0", "0", "0", "0", "True", "False", "4", "14", "0", "32", "0", "2", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["41", "271881817799985", "5628321", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "0", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["42", "5434175738686", "5552915", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "69", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "True", "False", "23", "8", "0", "32", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False"], ["43", "793821379148", "5552917", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "58", "SANTO ANDRÉ", "0", "0", "0", "0", "0", "True", "False", "23", "14", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False"], ["44", "67144894855774", "5552914", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "62", "SOLON BORGES", "0", "0", "0", "0", "0", "False", "False", "23", "13", "0", "0", "0", "1", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False"], ["45", "1846317738622", "5552936", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "30", "BONFIM", "1", "0", "0", "0", "0", "True", "False", "23", "14", "0", "20", "0", "3", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["46", "45421316129453", "5552934", "2016-04-06 00:00:00", "2016-04-29 00:00:00", "68", "REPÚBLICA", "0", "1", "1", "0", "0", "True", "False", "23", "12", "0", "35", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False"], ["47", "9672968175572", "5597628", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "64", "MARIA ORTIZ", "0", "0", "0", "0", "0", "True", "False", "11", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False"], ["48", "148894173528", "5597632", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "60", "JABOUR", "0", "0", "0", "0", "0", "False", "False", "11", "7", "0", "26", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False"], ["49", "6549277227425", "5597643", "2016-04-18 00:00:00", "2016-04-29 00:00:00", "28", "ANTÔNIO HONÓRIO", "0", "0", "0", "0", "0", "False", "True", "11", "17", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False"]], "shape": {"columns": 30, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>...</th>\n", "      <th>age_group_19-35</th>\n", "      <th>age_group_36-60</th>\n", "      <th>age_group_51-65</th>\n", "      <th>age_group_66+</th>\n", "      <th>received_sms_Yes</th>\n", "      <th>appointment_day_of_week_Monday</th>\n", "      <th>appointment_day_of_week_Saturday</th>\n", "      <th>appointment_day_of_week_Thursday</th>\n", "      <th>appointment_day_of_week_Tuesday</th>\n", "      <th>appointment_day_of_week_Wednesday</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 30 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id scheduled_day appointment_day  age  \\\n", "0       95985133231274         5626772    2016-04-27      2016-04-29   76   \n", "1      733688164476661         5630279    2016-04-27      2016-04-29   23   \n", "2        3449833394123         5630575    2016-04-27      2016-04-29   39   \n", "3       78124564369297         5629123    2016-04-27      2016-04-29   19   \n", "4      734536231958495         5630213    2016-04-27      2016-04-29   30   \n", "...                ...             ...           ...             ...  ...   \n", "71954    2572134369293         5651768    2016-05-03      2016-06-07   56   \n", "71955    3596266328735         5650093    2016-05-03      2016-06-07   51   \n", "71956   15576631729893         5630692    2016-04-27      2016-06-07   21   \n", "71957   92134931435557         5630323    2016-04-27      2016-06-07   38   \n", "71958  377511518121127         5629448    2016-04-27      2016-06-07   54   \n", "\n", "        neighbourhood  scholarship  hypertension  diabetes  alcoholism  ...  \\\n", "0           REPÚBLICA            0             1         0           0  ...   \n", "1          GOIABEIRAS            0             0         0           0  ...   \n", "2          GOIABEIRAS            0             0         0           0  ...   \n", "3           CONQUISTA            0             0         0           0  ...   \n", "4      NOVA PALESTINA            0             0         0           0  ...   \n", "...               ...          ...           ...       ...         ...  ...   \n", "71954     MARIA ORTIZ            0             0         0           0  ...   \n", "71955     MARIA ORTIZ            0             0         0           0  ...   \n", "71956     MARIA ORTIZ            0             0         0           0  ...   \n", "71957     MARIA ORTIZ            0             0         0           0  ...   \n", "71958     MARIA ORTIZ            0             0         0           0  ...   \n", "\n", "       age_group_19-35  age_group_36-60  age_group_51-65  age_group_66+  \\\n", "0                False            False            False           True   \n", "1                 True            False            False          False   \n", "2                False             True            False          False   \n", "3                 True            False            False          False   \n", "4                 True            False            False          False   \n", "...                ...              ...              ...            ...   \n", "71954            False            False             True          False   \n", "71955            False            False             True          False   \n", "71956             True            False            False          False   \n", "71957            False             True            False          False   \n", "71958            False            False             True          False   \n", "\n", "       received_sms_Yes  appointment_day_of_week_Monday  \\\n", "0                 False                           False   \n", "1                 False                           False   \n", "2                 False                           False   \n", "3                 False                           False   \n", "4                 False                           False   \n", "...                 ...                             ...   \n", "71954              True                           False   \n", "71955              True                           False   \n", "71956              True                           False   \n", "71957              True                           False   \n", "71958              True                           False   \n", "\n", "       appointment_day_of_week_Saturday  appointment_day_of_week_Thursday  \\\n", "0                                 False                             False   \n", "1                                 False                             False   \n", "2                                 False                             False   \n", "3                                 False                             False   \n", "4                                 False                             False   \n", "...                                 ...                               ...   \n", "71954                             False                             False   \n", "71955                             False                             False   \n", "71956                             False                             False   \n", "71957                             False                             False   \n", "71958                             False                             False   \n", "\n", "       appointment_day_of_week_Tuesday  appointment_day_of_week_Wednesday  \n", "0                                False                              False  \n", "1                                False                              False  \n", "2                                False                              False  \n", "3                                False                              False  \n", "4                                False                              False  \n", "...                                ...                                ...  \n", "71954                             True                              False  \n", "71955                             True                              False  \n", "71956                             True                              False  \n", "71957                             True                              False  \n", "71958                             True                              False  \n", "\n", "[71959 rows x 30 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = pd.get_dummies(df_fe, columns=['gender','age_group','received_sms','appointment_day_of_week'], drop_first=True)\n", "df_fe"]}, {"cell_type": "code", "execution_count": 16, "id": "467d3fd95c6f5b13", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:37.847318Z", "start_time": "2025-07-05T22:49:37.804860Z"}}, "outputs": [], "source": ["top_neigh = df_fe['neighbourhood'].value_counts().nlargest(10).index\n", "df_fe['neighbourhood'] = df_fe['neighbourhood'].apply(lambda x: x if x in top_neigh else 'Other')\n", "df_fe = pd.get_dummies(df_fe, columns=['neighbourhood'], drop_first=True)"]}, {"cell_type": "markdown", "id": "3bf934cc", "metadata": {}, "source": ["10. Drop columns irrelevant columns"]}, {"cell_type": "code", "execution_count": 17, "id": "a4913f61", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:38.156144Z", "start_time": "2025-07-05T22:49:38.091674Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}, {"name": "total_appointments", "rawType": "int64", "type": "integer"}, {"name": "gender_M", "rawType": "bool", "type": "boolean"}, {"name": "age_group_19-35", "rawType": "bool", "type": "boolean"}, {"name": "age_group_36-60", "rawType": "bool", "type": "boolean"}, {"name": "age_group_51-65", "rawType": "bool", "type": "boolean"}, {"name": "age_group_66+", "rawType": "bool", "type": "boolean"}, {"name": "received_sms_Yes", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Monday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Saturday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Thursday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Tuesday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Wednesday", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CARATOÍRA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CENTRO", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_ITARARÉ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM CAMBURI", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM DA PENHA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JESUS DE NAZARETH", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_MARIA ORTIZ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_Other", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_RESISTÊNCIA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_TABUAZEIRO", "rawType": "bool", "type": "boolean"}], "ref": "1f92ffb2-82d0-47bc-a018-333c6f836182", "rows": [["0", "76", "0", "1", "0", "0", "0", "False", "False", "2", "18", "0", "33", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["1", "23", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["2", "39", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "20", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["3", "19", "0", "0", "0", "0", "0", "False", "False", "2", "17", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["4", "30", "0", "0", "0", "0", "0", "False", "False", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["5", "29", "0", "0", "0", "0", "0", "True", "True", "3", "8", "0", "0", "0", "1", "True", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["6", "22", "1", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["7", "28", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["8", "54", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["9", "15", "0", "0", "0", "0", "0", "True", "False", "3", "12", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["10", "50", "0", "0", "0", "0", "0", "False", "False", "1", "14", "0", "0", "0", "1", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["11", "40", "1", "0", "0", "0", "0", "False", "True", "1", "8", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["12", "30", "1", "0", "0", "0", "0", "True", "False", "3", "11", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["13", "30", "0", "0", "0", "0", "0", "False", "True", "2", "14", "0", "32", "0", "2", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["14", "4", "0", "0", "0", "0", "0", "False", "True", "2", "10", "0", "0", "0", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["15", "13", "0", "0", "0", "0", "0", "True", "True", "4", "8", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["16", "46", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "31", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["17", "46", "0", "1", "0", "0", "0", "True", "False", "3", "9", "0", "0", "0", "1", "True", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["18", "4", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "6", "1", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["19", "46", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["20", "12", "1", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["21", "38", "1", "0", "0", "0", "0", "True", "False", "4", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["22", "85", "0", "1", "0", "0", "0", "True", "False", "9", "13", "0", "14", "0", "2", "True", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["23", "55", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "33", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True"], ["24", "71", "0", "0", "1", "0", "0", "False", "False", "1", "14", "0", "40", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["25", "50", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["26", "78", "0", "1", "1", "0", "0", "False", "True", "1", "14", "0", "0", "0", "1", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["27", "31", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["28", "58", "0", "1", "0", "1", "0", "True", "False", "4", "15", "0", "13", "0", "3", "True", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["29", "39", "0", "1", "1", "0", "0", "False", "False", "1", "15", "0", "32", "0", "7", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["30", "58", "0", "0", "0", "0", "0", "True", "True", "3", "10", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["31", "27", "0", "0", "0", "0", "0", "True", "True", "29", "12", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["32", "19", "0", "0", "0", "0", "0", "True", "True", "29", "7", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["33", "23", "1", "0", "0", "0", "0", "True", "True", "29", "15", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["34", "23", "1", "0", "0", "0", "0", "True", "True", "29", "7", "0", "6", "0", "5", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["35", "12", "0", "0", "0", "0", "0", "True", "True", "10", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["36", "8", "1", "0", "0", "0", "0", "False", "False", "2", "7", "0", "10", "0", "7", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["37", "2", "0", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["38", "3", "1", "0", "0", "0", "0", "True", "False", "4", "15", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["39", "0", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["40", "0", "0", "0", "0", "0", "0", "True", "False", "4", "14", "0", "32", "0", "2", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["41", "0", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["42", "69", "0", "1", "0", "0", "0", "True", "False", "23", "8", "0", "32", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["43", "58", "0", "0", "0", "0", "0", "True", "False", "23", "14", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["44", "62", "0", "0", "0", "0", "0", "False", "False", "23", "13", "0", "0", "0", "1", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["45", "30", "1", "0", "0", "0", "0", "True", "False", "23", "14", "0", "20", "0", "3", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["46", "68", "0", "1", "1", "0", "0", "True", "False", "23", "12", "0", "35", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["47", "64", "0", "0", "0", "0", "0", "True", "False", "11", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False"], ["48", "60", "0", "0", "0", "0", "0", "False", "False", "11", "7", "0", "26", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["49", "28", "0", "0", "0", "0", "0", "False", "True", "11", "17", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"]], "shape": {"columns": 35, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>scheduled_hours</th>\n", "      <th>...</th>\n", "      <th>neighbourhood_CARATOÍRA</th>\n", "      <th>neighbourhood_CENTRO</th>\n", "      <th>neighbourhood_ITARARÉ</th>\n", "      <th>neighbourhood_JARDIM CAMBURI</th>\n", "      <th>neighbourhood_JARDIM DA PENHA</th>\n", "      <th>neighbourhood_JESUS DE NAZARETH</th>\n", "      <th>neighbourhood_MARIA ORTIZ</th>\n", "      <th>neighbourhood_Other</th>\n", "      <th>neighbourhood_RESISTÊNCIA</th>\n", "      <th>neighbourhood_TABUAZEIRO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>18</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>13</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>21</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>38</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 35 columns</p>\n", "</div>"], "text/plain": ["       age  scholarship  hypertension  diabetes  alcoholism  handicap  \\\n", "0       76            0             1         0           0         0   \n", "1       23            0             0         0           0         0   \n", "2       39            0             0         0           0         0   \n", "3       19            0             0         0           0         0   \n", "4       30            0             0         0           0         0   \n", "...    ...          ...           ...       ...         ...       ...   \n", "71954   56            0             0         0           0         0   \n", "71955   51            0             0         0           0         0   \n", "71956   21            0             0         0           0         0   \n", "71957   38            0             0         0           0         0   \n", "71958   54            0             0         0           0         0   \n", "\n", "       sms_received  no_show  lead_time  scheduled_hours  ...  \\\n", "0             False    False          2               18  ...   \n", "1             False     True          2               16  ...   \n", "2             False     True          2               16  ...   \n", "3             False    False          2               17  ...   \n", "4             False    False          2               16  ...   \n", "...             ...      ...        ...              ...  ...   \n", "71954          True    False         35               13  ...   \n", "71955          True    False         35               16  ...   \n", "71956          True    False         41                7  ...   \n", "71957          True    False         41                7  ...   \n", "71958          True    False         41                7  ...   \n", "\n", "       neighbourhood_CARATOÍRA  neighbourhood_CENTRO  neighbourhood_ITARARÉ  \\\n", "0                        False                 False                  False   \n", "1                        False                 False                  False   \n", "2                        False                 False                  False   \n", "3                        False                 False                  False   \n", "4                        False                 False                  False   \n", "...                        ...                   ...                    ...   \n", "71954                    False                 False                  False   \n", "71955                    False                 False                  False   \n", "71956                    False                 False                  False   \n", "71957                    False                 False                  False   \n", "71958                    False                 False                  False   \n", "\n", "       neighbourhood_JARDIM CAMBURI  neighbourhood_JARDIM DA PENHA  \\\n", "0                             False                          False   \n", "1                             False                          False   \n", "2                             False                          False   \n", "3                             False                          False   \n", "4                             False                          False   \n", "...                             ...                            ...   \n", "71954                         False                          False   \n", "71955                         False                          False   \n", "71956                         False                          False   \n", "71957                         False                          False   \n", "71958                         False                          False   \n", "\n", "       neighbourhood_JESUS DE NAZARETH  neighbourhood_MARIA ORTIZ  \\\n", "0                                False                      False   \n", "1                                False                      False   \n", "2                                False                      False   \n", "3                                False                      False   \n", "4                                False                      False   \n", "...                                ...                        ...   \n", "71954                            False                       True   \n", "71955                            False                       True   \n", "71956                            False                       True   \n", "71957                            False                       True   \n", "71958                            False                       True   \n", "\n", "       neighbourhood_Other  neighbourhood_RESISTÊNCIA  \\\n", "0                     True                      False   \n", "1                     True                      False   \n", "2                     True                      False   \n", "3                     True                      False   \n", "4                     True                      False   \n", "...                    ...                        ...   \n", "71954                False                      False   \n", "71955                False                      False   \n", "71956                False                      False   \n", "71957                False                      False   \n", "71958                False                      False   \n", "\n", "       neighbourhood_TABUAZEIRO  \n", "0                         False  \n", "1                         False  \n", "2                         False  \n", "3                         False  \n", "4                         False  \n", "...                         ...  \n", "71954                     False  \n", "71955                     False  \n", "71956                     False  \n", "71957                     False  \n", "71958                     False  \n", "\n", "[71959 rows x 35 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = df_fe.drop(columns=['appointment_id', 'patient_id', 'scheduled_day', 'appointment_day'], axis=1, inplace=False)\n", "df_fe"]}, {"cell_type": "code", "execution_count": 18, "id": "532d79cdafff1fd2", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:38.366765Z", "start_time": "2025-07-05T22:49:38.347189Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 35 columns):\n", " #   Column                             Non-Null Count  Dtype\n", "---  ------                             --------------  -----\n", " 0   age                                71959 non-null  int64\n", " 1   scholarship                        71959 non-null  int64\n", " 2   hypertension                       71959 non-null  int64\n", " 3   diabetes                           71959 non-null  int64\n", " 4   alcoholism                         71959 non-null  int64\n", " 5   handicap                           71959 non-null  int64\n", " 6   sms_received                       71959 non-null  bool \n", " 7   no_show                            71959 non-null  bool \n", " 8   lead_time                          71959 non-null  int64\n", " 9   scheduled_hours                    71959 non-null  int64\n", " 10  is_weekend                         71959 non-null  int64\n", " 11  days_until_next_appointment        71959 non-null  int64\n", " 12  previous_no_show                   71959 non-null  int64\n", " 13  total_appointments                 71959 non-null  int64\n", " 14  gender_M                           71959 non-null  bool \n", " 15  age_group_19-35                    71959 non-null  bool \n", " 16  age_group_36-60                    71959 non-null  bool \n", " 17  age_group_51-65                    71959 non-null  bool \n", " 18  age_group_66+                      71959 non-null  bool \n", " 19  received_sms_Yes                   71959 non-null  bool \n", " 20  appointment_day_of_week_Monday     71959 non-null  bool \n", " 21  appointment_day_of_week_Saturday   71959 non-null  bool \n", " 22  appointment_day_of_week_Thursday   71959 non-null  bool \n", " 23  appointment_day_of_week_Tuesday    71959 non-null  bool \n", " 24  appointment_day_of_week_Wednesday  71959 non-null  bool \n", " 25  neighbourhood_CARATOÍRA            71959 non-null  bool \n", " 26  neighbourhood_CENTRO               71959 non-null  bool \n", " 27  neighbourhood_ITARARÉ              71959 non-null  bool \n", " 28  neighbourhood_JARDIM CAMBURI       71959 non-null  bool \n", " 29  neighbourhood_JARDIM DA PENHA      71959 non-null  bool \n", " 30  neighbourhood_JESUS DE NAZARETH    71959 non-null  bool \n", " 31  neighbourhood_MARIA ORTIZ          71959 non-null  bool \n", " 32  neighbourhood_Other                71959 non-null  bool \n", " 33  neighbourhood_RESISTÊNCIA          71959 non-null  bool \n", " 34  neighbourhood_TABUAZEIRO           71959 non-null  bool \n", "dtypes: bool(23), int64(12)\n", "memory usage: 8.2 MB\n"]}], "source": ["df_fe.info()"]}, {"cell_type": "code", "execution_count": 19, "id": "3117e076b16be241", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:49:39.614471Z", "start_time": "2025-07-05T22:49:38.583363Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 35 columns):\n", " #   Column                             Non-Null Count  Dtype\n", "---  ------                             --------------  -----\n", " 0   age                                71959 non-null  int64\n", " 1   scholarship                        71959 non-null  int64\n", " 2   hypertension                       71959 non-null  int64\n", " 3   diabetes                           71959 non-null  int64\n", " 4   alcoholism                         71959 non-null  int64\n", " 5   handicap                           71959 non-null  int64\n", " 6   sms_received                       71959 non-null  bool \n", " 7   no_show                            71959 non-null  bool \n", " 8   lead_time                          71959 non-null  int64\n", " 9   scheduled_hours                    71959 non-null  int64\n", " 10  is_weekend                         71959 non-null  int64\n", " 11  days_until_next_appointment        71959 non-null  int64\n", " 12  previous_no_show                   71959 non-null  int64\n", " 13  total_appointments                 71959 non-null  int64\n", " 14  gender_M                           71959 non-null  bool \n", " 15  age_group_19-35                    71959 non-null  bool \n", " 16  age_group_36-60                    71959 non-null  bool \n", " 17  age_group_51-65                    71959 non-null  bool \n", " 18  age_group_66+                      71959 non-null  bool \n", " 19  received_sms_Yes                   71959 non-null  bool \n", " 20  appointment_day_of_week_Monday     71959 non-null  bool \n", " 21  appointment_day_of_week_Saturday   71959 non-null  bool \n", " 22  appointment_day_of_week_Thursday   71959 non-null  bool \n", " 23  appointment_day_of_week_Tuesday    71959 non-null  bool \n", " 24  appointment_day_of_week_Wednesday  71959 non-null  bool \n", " 25  neighbourhood_CARATOÍRA            71959 non-null  bool \n", " 26  neighbourhood_CENTRO               71959 non-null  bool \n", " 27  neighbourhood_ITARARÉ              71959 non-null  bool \n", " 28  neighbourhood_JARDIM CAMBURI       71959 non-null  bool \n", " 29  neighbourhood_JARDIM DA PENHA      71959 non-null  bool \n", " 30  neighbourhood_JESUS DE NAZARETH    71959 non-null  bool \n", " 31  neighbourhood_MARIA ORTIZ          71959 non-null  bool \n", " 32  neighbourhood_Other                71959 non-null  bool \n", " 33  neighbourhood_RESISTÊNCIA          71959 non-null  bool \n", " 34  neighbourhood_TABUAZEIRO           71959 non-null  bool \n", "dtypes: bool(23), int64(12)\n", "memory usage: 8.2 MB\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}, {"name": "total_appointments", "rawType": "int64", "type": "integer"}, {"name": "gender_M", "rawType": "bool", "type": "boolean"}, {"name": "age_group_19-35", "rawType": "bool", "type": "boolean"}, {"name": "age_group_36-60", "rawType": "bool", "type": "boolean"}, {"name": "age_group_51-65", "rawType": "bool", "type": "boolean"}, {"name": "age_group_66+", "rawType": "bool", "type": "boolean"}, {"name": "received_sms_Yes", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Monday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Saturday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Thursday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Tuesday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Wednesday", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CARATOÍRA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CENTRO", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_ITARARÉ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM CAMBURI", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM DA PENHA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JESUS DE NAZARETH", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_MARIA ORTIZ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_Other", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_RESISTÊNCIA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_TABUAZEIRO", "rawType": "bool", "type": "boolean"}], "ref": "a7d1723c-4de0-472d-ae31-154c987a928e", "rows": [["0", "76", "0", "1", "0", "0", "0", "False", "False", "2", "18", "0", "33", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["1", "23", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["2", "39", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "20", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["3", "19", "0", "0", "0", "0", "0", "False", "False", "2", "17", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["4", "30", "0", "0", "0", "0", "0", "False", "False", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["5", "29", "0", "0", "0", "0", "0", "True", "True", "3", "8", "0", "0", "0", "1", "True", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["6", "22", "1", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["7", "28", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["8", "54", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["9", "15", "0", "0", "0", "0", "0", "True", "False", "3", "12", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"]], "shape": {"columns": 35, "rows": 10}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>scheduled_hours</th>\n", "      <th>...</th>\n", "      <th>neighbourhood_CARATOÍRA</th>\n", "      <th>neighbourhood_CENTRO</th>\n", "      <th>neighbourhood_ITARARÉ</th>\n", "      <th>neighbourhood_JARDIM CAMBURI</th>\n", "      <th>neighbourhood_JARDIM DA PENHA</th>\n", "      <th>neighbourhood_JESUS DE NAZARETH</th>\n", "      <th>neighbourhood_MARIA ORTIZ</th>\n", "      <th>neighbourhood_Other</th>\n", "      <th>neighbourhood_RESISTÊNCIA</th>\n", "      <th>neighbourhood_TABUAZEIRO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>18</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>15</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>15</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>15</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 35 columns</p>\n", "</div>"], "text/plain": ["   age  scholarship  hypertension  diabetes  alcoholism  handicap  \\\n", "0   76            0             1         0           0         0   \n", "1   23            0             0         0           0         0   \n", "2   39            0             0         0           0         0   \n", "3   19            0             0         0           0         0   \n", "4   30            0             0         0           0         0   \n", "5   29            0             0         0           0         0   \n", "6   22            1             0         0           0         0   \n", "7   28            0             0         0           0         0   \n", "8   54            0             0         0           0         0   \n", "9   15            0             0         0           0         0   \n", "\n", "   sms_received  no_show  lead_time  scheduled_hours  ...  \\\n", "0         False    False          2               18  ...   \n", "1         False     True          2               16  ...   \n", "2         False     True          2               16  ...   \n", "3         False    False          2               17  ...   \n", "4         False    False          2               16  ...   \n", "5          True     True          3                8  ...   \n", "6         False    False          1               15  ...   \n", "7         False    False          1               15  ...   \n", "8         False    False          1                8  ...   \n", "9          True    False          3               12  ...   \n", "\n", "   neighbourhood_CARATOÍRA  neighbourhood_CENTRO  neighbourhood_ITARARÉ  \\\n", "0                    False                 False                  False   \n", "1                    False                 False                  False   \n", "2                    False                 False                  False   \n", "3                    False                 False                  False   \n", "4                    False                 False                  False   \n", "5                    False                 False                  False   \n", "6                    False                 False                  False   \n", "7                    False                 False                  False   \n", "8                    False                 False                  False   \n", "9                    False                 False                  False   \n", "\n", "   neighbourhood_JARDIM CAMBURI  neighbourhood_JARDIM DA PENHA  \\\n", "0                         False                          False   \n", "1                         False                          False   \n", "2                         False                          False   \n", "3                         False                          False   \n", "4                         False                          False   \n", "5                         False                          False   \n", "6                         False                          False   \n", "7                         False                          False   \n", "8                         False                          False   \n", "9                         False                          False   \n", "\n", "   neighbourhood_JESUS DE NAZARETH  neighbourhood_MARIA ORTIZ  \\\n", "0                            False                      False   \n", "1                            False                      False   \n", "2                            False                      False   \n", "3                            False                      False   \n", "4                            False                      False   \n", "5                            False                      False   \n", "6                            False                      False   \n", "7                            False                      False   \n", "8                            False                      False   \n", "9                            False                      False   \n", "\n", "   neighbourhood_Other  neighbourhood_RESISTÊNCIA  neighbourhood_TABUAZEIRO  \n", "0                 True                      False                     False  \n", "1                 True                      False                     False  \n", "2                 True                      False                     False  \n", "3                 True                      False                     False  \n", "4                 True                      False                     False  \n", "5                 True                      False                     False  \n", "6                 True                      False                     False  \n", "7                 True                      False                     False  \n", "8                 True                      False                     False  \n", "9                 True                      False                     False  \n", "\n", "[10 rows x 35 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["FILE_PATH = \"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show_features.csv\"\n", "\n", "if os.path.exists(FILE_PATH):\n", "    os.remove(FILE_PATH)\n", "\n", "df_fe.to_csv(FILE_PATH, index=False)\n", "\n", "show_df = pd.read_csv(FILE_PATH)\n", "show_df.info()\n", "show_df.head(10)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}