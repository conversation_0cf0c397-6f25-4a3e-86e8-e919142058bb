{"cells": [{"cell_type": "markdown", "id": "58f9d1575354c326", "metadata": {}, "source": ["<h1>2. Data Cleaning</h1>"]}, {"cell_type": "code", "execution_count": 1, "id": "f397eaa90b2253d1", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:53.297147Z", "start_time": "2025-07-05T22:12:53.293387Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "id": "5092c4ab9d45c6c7", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:53.884334Z", "start_time": "2025-07-05T22:12:53.639093Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PatientId", "rawType": "float64", "type": "float"}, {"name": "AppointmentID", "rawType": "int64", "type": "integer"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "ScheduledDay", "rawType": "object", "type": "string"}, {"name": "AppointmentDay", "rawType": "object", "type": "string"}, {"name": "Age", "rawType": "int64", "type": "integer"}, {"name": "Neighbourhood", "rawType": "object", "type": "string"}, {"name": "Scholarship", "rawType": "int64", "type": "integer"}, {"name": "Hipertension", "rawType": "int64", "type": "integer"}, {"name": "Diabetes", "rawType": "int64", "type": "integer"}, {"name": "Alcoholism", "rawType": "int64", "type": "integer"}, {"name": "Handcap", "rawType": "int64", "type": "integer"}, {"name": "SMS_received", "rawType": "int64", "type": "integer"}, {"name": "No-show", "rawType": "object", "type": "string"}], "ref": "4cd6dae4-d581-457a-9260-6a6f58c21a54", "rows": [["0", "29872499824296.0", "5642903", "F", "2016-04-29T18:38:08Z", "2016-04-29T00:00:00Z", "62", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "0", "No"], ["1", "558997776694438.0", "5642503", "M", "2016-04-29T16:08:27Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "0", "0", "0", "0", "0", "No"], ["2", "4262962299951.0", "5642549", "F", "2016-04-29T16:19:04Z", "2016-04-29T00:00:00Z", "62", "MATA DA PRAIA", "0", "0", "0", "0", "0", "0", "No"], ["3", "867951213174.0", "5642828", "F", "2016-04-29T17:29:31Z", "2016-04-29T00:00:00Z", "8", "PONTAL DE CAMBURI", "0", "0", "0", "0", "0", "0", "No"], ["4", "8841186448183.0", "5642494", "F", "2016-04-29T16:07:23Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "1", "1", "0", "0", "0", "No"], ["5", "95985133231274.0", "5626772", "F", "2016-04-27T08:36:51Z", "2016-04-29T00:00:00Z", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "0", "No"], ["6", "733688164476661.0", "5630279", "F", "2016-04-27T15:05:12Z", "2016-04-29T00:00:00Z", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["7", "3449833394123.0", "5630575", "F", "2016-04-27T15:39:58Z", "2016-04-29T00:00:00Z", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["8", "56394729949972.0", "5638447", "F", "2016-04-29T08:02:16Z", "2016-04-29T00:00:00Z", "21", "ANDORINHAS", "0", "0", "0", "0", "0", "0", "No"], ["9", "78124564369297.0", "5629123", "F", "2016-04-27T12:48:25Z", "2016-04-29T00:00:00Z", "19", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["10", "734536231958495.0", "5630213", "F", "2016-04-27T14:58:11Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["11", "7542951368435.0", "5620163", "M", "2016-04-26T08:44:12Z", "2016-04-29T00:00:00Z", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "1", "Yes"], ["12", "566654781423437.0", "5634718", "F", "2016-04-28T11:33:51Z", "2016-04-29T00:00:00Z", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "0", "No"], ["13", "911394617215919.0", "5636249", "M", "2016-04-28T14:52:07Z", "2016-04-29T00:00:00Z", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["14", "99884723334928.0", "5633951", "F", "2016-04-28T10:06:24Z", "2016-04-29T00:00:00Z", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["15", "99948393975.0", "5620206", "F", "2016-04-26T08:47:27Z", "2016-04-29T00:00:00Z", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "1", "No"], ["16", "84574392942817.0", "5633121", "M", "2016-04-28T08:51:47Z", "2016-04-29T00:00:00Z", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["17", "14794966191172.0", "5633460", "F", "2016-04-28T09:28:57Z", "2016-04-29T00:00:00Z", "40", "CONQUISTA", "1", "0", "0", "0", "0", "0", "Yes"], ["18", "17135378245248.0", "5621836", "F", "2016-04-26T10:54:18Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "1", "No"], ["19", "7223289184215.0", "5640433", "F", "2016-04-29T10:43:14Z", "2016-04-29T00:00:00Z", "46", "DA PENHA", "0", "0", "0", "0", "0", "0", "No"], ["20", "622257462899397.0", "5626083", "F", "2016-04-27T07:51:14Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "Yes"], ["21", "12154843752835.0", "5628338", "F", "2016-04-27T10:50:45Z", "2016-04-29T00:00:00Z", "4", "CONQUISTA", "0", "0", "0", "0", "0", "0", "Yes"], ["22", "863229818887631.0", "5616091", "M", "2016-04-25T13:29:16Z", "2016-04-29T00:00:00Z", "13", "CONQUISTA", "0", "0", "0", "0", "0", "1", "Yes"], ["23", "213753979425692.0", "5634142", "F", "2016-04-28T10:27:05Z", "2016-04-29T00:00:00Z", "46", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["24", "8734857996885.0", "5641780", "F", "2016-04-29T14:19:19Z", "2016-04-29T00:00:00Z", "65", "TABUAZEIRO", "0", "0", "0", "0", "0", "0", "No"], ["25", "5819369978796.0", "5624020", "M", "2016-04-26T15:04:17Z", "2016-04-29T00:00:00Z", "46", "CONQUISTA", "0", "1", "0", "0", "0", "1", "No"], ["26", "25787851512.0", "5641781", "F", "2016-04-29T14:19:42Z", "2016-04-29T00:00:00Z", "45", "BENTO FERREIRA", "0", "1", "0", "0", "0", "0", "No"], ["27", "12154843752835.0", "5628345", "F", "2016-04-27T10:51:45Z", "2016-04-29T00:00:00Z", "4", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["28", "5926171692527.0", "5642400", "M", "2016-04-29T15:48:02Z", "2016-04-29T00:00:00Z", "51", "SÃO PEDRO", "0", "0", "0", "0", "0", "0", "No"], ["29", "1225776163665.0", "5642186", "F", "2016-04-29T15:16:29Z", "2016-04-29T00:00:00Z", "32", "SANTA MARTHA", "0", "0", "0", "0", "0", "0", "No"], ["30", "342815551642.0", "5628068", "F", "2016-04-27T10:24:52Z", "2016-04-29T00:00:00Z", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["31", "311284853849.0", "5628907", "M", "2016-04-27T12:07:14Z", "2016-04-29T00:00:00Z", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "0", "Yes"], ["32", "52883562769472.0", "5637908", "M", "2016-04-29T07:30:27Z", "2016-04-29T00:00:00Z", "61", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["33", "7653516999712.0", "5616921", "F", "2016-04-25T15:01:04Z", "2016-04-29T00:00:00Z", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "1", "No"], ["34", "19999756532994.0", "5637963", "F", "2016-04-29T07:34:30Z", "2016-04-29T00:00:00Z", "79", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["35", "78162635161611.0", "5637968", "M", "2016-04-29T07:34:42Z", "2016-04-29T00:00:00Z", "18", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["36", "72984587621439.0", "5637975", "F", "2016-04-29T07:35:19Z", "2016-04-29T00:00:00Z", "63", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "0", "No"], ["37", "1578131861739.0", "5637986", "F", "2016-04-29T07:35:56Z", "2016-04-29T00:00:00Z", "64", "TABUAZEIRO", "1", "1", "1", "0", "0", "0", "No"], ["38", "5873315843778.0", "5609446", "M", "2016-04-20T15:54:18Z", "2016-04-29T00:00:00Z", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "1", "No"], ["39", "14556231793236.0", "5639644", "F", "2016-04-29T09:21:13Z", "2016-04-29T00:00:00Z", "59", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["40", "996868412638744.0", "5635881", "F", "2016-04-28T14:14:16Z", "2016-04-29T00:00:00Z", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "0", "No"], ["41", "822432466381793.0", "5633339", "F", "2016-04-28T09:20:36Z", "2016-04-29T00:00:00Z", "71", "MARUÍPE", "0", "0", "1", "0", "0", "0", "No"], ["42", "25965426543339.0", "5632906", "F", "2016-04-28T08:34:54Z", "2016-04-29T00:00:00Z", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["43", "71558956872246.0", "5641620", "F", "2016-04-29T14:02:16Z", "2016-04-29T00:00:00Z", "49", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["44", "274164858852.0", "5635414", "F", "2016-04-28T13:27:27Z", "2016-04-29T00:00:00Z", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "0", "Yes"], ["45", "4982378572899.0", "5635842", "F", "2016-04-28T14:11:24Z", "2016-04-29T00:00:00Z", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["46", "137943696338.0", "5615608", "M", "2016-04-25T12:44:36Z", "2016-04-29T00:00:00Z", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "1", "No"], ["47", "589458459955.0", "5633116", "F", "2016-04-28T08:51:26Z", "2016-04-29T00:00:00Z", "39", "MARUÍPE", "0", "1", "1", "0", "0", "0", "No"], ["48", "8545415176986.0", "5618643", "F", "2016-04-26T07:19:49Z", "2016-04-29T00:00:00Z", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "1", "Yes"], ["49", "92235587471561.0", "5534656", "F", "2016-03-31T17:11:17Z", "2016-04-29T00:00:00Z", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "1", "Yes"]], "shape": {"columns": 14, "rows": 110527}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2.572134e+12</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T09:15:35Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3.596266e+12</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T07:27:33Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>1.557663e+13</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T16:03:52Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>9.213493e+13</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:09:23Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>3.775115e+14</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T13:30:56Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"], "text/plain": ["           PatientId  AppointmentID Gender          ScheduledDay  \\\n", "0       2.987250e+13        5642903      F  2016-04-29T18:38:08Z   \n", "1       5.589978e+14        5642503      M  2016-04-29T16:08:27Z   \n", "2       4.262962e+12        5642549      F  2016-04-29T16:19:04Z   \n", "3       8.679512e+11        5642828      F  2016-04-29T17:29:31Z   \n", "4       8.841186e+12        5642494      F  2016-04-29T16:07:23Z   \n", "...              ...            ...    ...                   ...   \n", "110522  2.572134e+12        5651768      F  2016-05-03T09:15:35Z   \n", "110523  3.596266e+12        5650093      F  2016-05-03T07:27:33Z   \n", "110524  1.557663e+13        5630692      F  2016-04-27T16:03:52Z   \n", "110525  9.213493e+13        5630323      F  2016-04-27T15:09:23Z   \n", "110526  3.775115e+14        5629448      F  2016-04-27T13:30:56Z   \n", "\n", "              AppointmentDay  Age      Neighbourhood  Scholarship  \\\n", "0       2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0   \n", "1       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "2       2016-04-29T00:00:00Z   62      MATA DA PRAIA            0   \n", "3       2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0   \n", "4       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "...                      ...  ...                ...          ...   \n", "110522  2016-06-07T00:00:00Z   56        MARIA ORTIZ            0   \n", "110523  2016-06-07T00:00:00Z   51        MARIA ORTIZ            0   \n", "110524  2016-06-07T00:00:00Z   21        MARIA ORTIZ            0   \n", "110525  2016-06-07T00:00:00Z   38        MARIA ORTIZ            0   \n", "110526  2016-06-07T00:00:00Z   54        MARIA ORTIZ            0   \n", "\n", "        Hipertension  Diabetes  Alcoholism  Handcap  SMS_received No-show  \n", "0                  1         0           0        0             0      No  \n", "1                  0         0           0        0             0      No  \n", "2                  0         0           0        0             0      No  \n", "3                  0         0           0        0             0      No  \n", "4                  1         1           0        0             0      No  \n", "...              ...       ...         ...      ...           ...     ...  \n", "110522             0         0           0        0             1      No  \n", "110523             0         0           0        0             1      No  \n", "110524             0         0           0        0             1      No  \n", "110525             0         0           0        0             1      No  \n", "110526             0         0           0        0             1      No  \n", "\n", "[110527 rows x 14 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show.csv\")\n", "df"]}, {"cell_type": "code", "execution_count": 3, "id": "44dc80ce", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:54.007913Z", "start_time": "2025-07-05T22:12:53.980881Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "float64", "type": "float"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "object", "type": "string"}, {"name": "scheduled_day", "rawType": "object", "type": "string"}, {"name": "appointment_day", "rawType": "object", "type": "string"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "object", "type": "string"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "int64", "type": "integer"}, {"name": "no_show", "rawType": "object", "type": "string"}], "ref": "994b11fa-ba03-4edb-863e-d83bc91bd9b2", "rows": [["0", "29872499824296.0", "5642903", "F", "2016-04-29T18:38:08Z", "2016-04-29T00:00:00Z", "62", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "0", "No"], ["1", "558997776694438.0", "5642503", "M", "2016-04-29T16:08:27Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "0", "0", "0", "0", "0", "No"], ["2", "4262962299951.0", "5642549", "F", "2016-04-29T16:19:04Z", "2016-04-29T00:00:00Z", "62", "MATA DA PRAIA", "0", "0", "0", "0", "0", "0", "No"], ["3", "867951213174.0", "5642828", "F", "2016-04-29T17:29:31Z", "2016-04-29T00:00:00Z", "8", "PONTAL DE CAMBURI", "0", "0", "0", "0", "0", "0", "No"], ["4", "8841186448183.0", "5642494", "F", "2016-04-29T16:07:23Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "1", "1", "0", "0", "0", "No"], ["5", "95985133231274.0", "5626772", "F", "2016-04-27T08:36:51Z", "2016-04-29T00:00:00Z", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "0", "No"], ["6", "733688164476661.0", "5630279", "F", "2016-04-27T15:05:12Z", "2016-04-29T00:00:00Z", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["7", "3449833394123.0", "5630575", "F", "2016-04-27T15:39:58Z", "2016-04-29T00:00:00Z", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["8", "56394729949972.0", "5638447", "F", "2016-04-29T08:02:16Z", "2016-04-29T00:00:00Z", "21", "ANDORINHAS", "0", "0", "0", "0", "0", "0", "No"], ["9", "78124564369297.0", "5629123", "F", "2016-04-27T12:48:25Z", "2016-04-29T00:00:00Z", "19", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["10", "734536231958495.0", "5630213", "F", "2016-04-27T14:58:11Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["11", "7542951368435.0", "5620163", "M", "2016-04-26T08:44:12Z", "2016-04-29T00:00:00Z", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "1", "Yes"], ["12", "566654781423437.0", "5634718", "F", "2016-04-28T11:33:51Z", "2016-04-29T00:00:00Z", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "0", "No"], ["13", "911394617215919.0", "5636249", "M", "2016-04-28T14:52:07Z", "2016-04-29T00:00:00Z", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["14", "99884723334928.0", "5633951", "F", "2016-04-28T10:06:24Z", "2016-04-29T00:00:00Z", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["15", "99948393975.0", "5620206", "F", "2016-04-26T08:47:27Z", "2016-04-29T00:00:00Z", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "1", "No"], ["16", "84574392942817.0", "5633121", "M", "2016-04-28T08:51:47Z", "2016-04-29T00:00:00Z", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["17", "14794966191172.0", "5633460", "F", "2016-04-28T09:28:57Z", "2016-04-29T00:00:00Z", "40", "CONQUISTA", "1", "0", "0", "0", "0", "0", "Yes"], ["18", "17135378245248.0", "5621836", "F", "2016-04-26T10:54:18Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "1", "No"], ["19", "7223289184215.0", "5640433", "F", "2016-04-29T10:43:14Z", "2016-04-29T00:00:00Z", "46", "DA PENHA", "0", "0", "0", "0", "0", "0", "No"], ["20", "622257462899397.0", "5626083", "F", "2016-04-27T07:51:14Z", "2016-04-29T00:00:00Z", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "Yes"], ["21", "12154843752835.0", "5628338", "F", "2016-04-27T10:50:45Z", "2016-04-29T00:00:00Z", "4", "CONQUISTA", "0", "0", "0", "0", "0", "0", "Yes"], ["22", "863229818887631.0", "5616091", "M", "2016-04-25T13:29:16Z", "2016-04-29T00:00:00Z", "13", "CONQUISTA", "0", "0", "0", "0", "0", "1", "Yes"], ["23", "213753979425692.0", "5634142", "F", "2016-04-28T10:27:05Z", "2016-04-29T00:00:00Z", "46", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["24", "8734857996885.0", "5641780", "F", "2016-04-29T14:19:19Z", "2016-04-29T00:00:00Z", "65", "TABUAZEIRO", "0", "0", "0", "0", "0", "0", "No"], ["25", "5819369978796.0", "5624020", "M", "2016-04-26T15:04:17Z", "2016-04-29T00:00:00Z", "46", "CONQUISTA", "0", "1", "0", "0", "0", "1", "No"], ["26", "25787851512.0", "5641781", "F", "2016-04-29T14:19:42Z", "2016-04-29T00:00:00Z", "45", "BENTO FERREIRA", "0", "1", "0", "0", "0", "0", "No"], ["27", "12154843752835.0", "5628345", "F", "2016-04-27T10:51:45Z", "2016-04-29T00:00:00Z", "4", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"], ["28", "5926171692527.0", "5642400", "M", "2016-04-29T15:48:02Z", "2016-04-29T00:00:00Z", "51", "SÃO PEDRO", "0", "0", "0", "0", "0", "0", "No"], ["29", "1225776163665.0", "5642186", "F", "2016-04-29T15:16:29Z", "2016-04-29T00:00:00Z", "32", "SANTA MARTHA", "0", "0", "0", "0", "0", "0", "No"], ["30", "342815551642.0", "5628068", "F", "2016-04-27T10:24:52Z", "2016-04-29T00:00:00Z", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "0", "No"], ["31", "311284853849.0", "5628907", "M", "2016-04-27T12:07:14Z", "2016-04-29T00:00:00Z", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "0", "Yes"], ["32", "52883562769472.0", "5637908", "M", "2016-04-29T07:30:27Z", "2016-04-29T00:00:00Z", "61", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["33", "7653516999712.0", "5616921", "F", "2016-04-25T15:01:04Z", "2016-04-29T00:00:00Z", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "1", "No"], ["34", "19999756532994.0", "5637963", "F", "2016-04-29T07:34:30Z", "2016-04-29T00:00:00Z", "79", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["35", "78162635161611.0", "5637968", "M", "2016-04-29T07:34:42Z", "2016-04-29T00:00:00Z", "18", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["36", "72984587621439.0", "5637975", "F", "2016-04-29T07:35:19Z", "2016-04-29T00:00:00Z", "63", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "0", "No"], ["37", "1578131861739.0", "5637986", "F", "2016-04-29T07:35:56Z", "2016-04-29T00:00:00Z", "64", "TABUAZEIRO", "1", "1", "1", "0", "0", "0", "No"], ["38", "5873315843778.0", "5609446", "M", "2016-04-20T15:54:18Z", "2016-04-29T00:00:00Z", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "1", "No"], ["39", "14556231793236.0", "5639644", "F", "2016-04-29T09:21:13Z", "2016-04-29T00:00:00Z", "59", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["40", "996868412638744.0", "5635881", "F", "2016-04-28T14:14:16Z", "2016-04-29T00:00:00Z", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "0", "No"], ["41", "822432466381793.0", "5633339", "F", "2016-04-28T09:20:36Z", "2016-04-29T00:00:00Z", "71", "MARUÍPE", "0", "0", "1", "0", "0", "0", "No"], ["42", "25965426543339.0", "5632906", "F", "2016-04-28T08:34:54Z", "2016-04-29T00:00:00Z", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["43", "71558956872246.0", "5641620", "F", "2016-04-29T14:02:16Z", "2016-04-29T00:00:00Z", "49", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "0", "No"], ["44", "274164858852.0", "5635414", "F", "2016-04-28T13:27:27Z", "2016-04-29T00:00:00Z", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "0", "Yes"], ["45", "4982378572899.0", "5635842", "F", "2016-04-28T14:11:24Z", "2016-04-29T00:00:00Z", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "0", "No"], ["46", "137943696338.0", "5615608", "M", "2016-04-25T12:44:36Z", "2016-04-29T00:00:00Z", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "1", "No"], ["47", "589458459955.0", "5633116", "F", "2016-04-28T08:51:26Z", "2016-04-29T00:00:00Z", "39", "MARUÍPE", "0", "1", "1", "0", "0", "0", "No"], ["48", "8545415176986.0", "5618643", "F", "2016-04-26T07:19:49Z", "2016-04-29T00:00:00Z", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "1", "Yes"], ["49", "92235587471561.0", "5534656", "F", "2016-03-31T17:11:17Z", "2016-04-29T00:00:00Z", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "1", "Yes"]], "shape": {"columns": 14, "rows": 110527}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2.572134e+12</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T09:15:35Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3.596266e+12</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03T07:27:33Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>1.557663e+13</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T16:03:52Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>9.213493e+13</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:09:23Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>3.775115e+14</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T13:30:56Z</td>\n", "      <td>2016-06-07T00:00:00Z</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"], "text/plain": ["          patient_id  appointment_id gender         scheduled_day  \\\n", "0       2.987250e+13         5642903      F  2016-04-29T18:38:08Z   \n", "1       5.589978e+14         5642503      M  2016-04-29T16:08:27Z   \n", "2       4.262962e+12         5642549      F  2016-04-29T16:19:04Z   \n", "3       8.679512e+11         5642828      F  2016-04-29T17:29:31Z   \n", "4       8.841186e+12         5642494      F  2016-04-29T16:07:23Z   \n", "...              ...             ...    ...                   ...   \n", "110522  2.572134e+12         5651768      F  2016-05-03T09:15:35Z   \n", "110523  3.596266e+12         5650093      F  2016-05-03T07:27:33Z   \n", "110524  1.557663e+13         5630692      F  2016-04-27T16:03:52Z   \n", "110525  9.213493e+13         5630323      F  2016-04-27T15:09:23Z   \n", "110526  3.775115e+14         5629448      F  2016-04-27T13:30:56Z   \n", "\n", "             appointment_day  age      neighbourhood  scholarship  \\\n", "0       2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0   \n", "1       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "2       2016-04-29T00:00:00Z   62      MATA DA PRAIA            0   \n", "3       2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0   \n", "4       2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0   \n", "...                      ...  ...                ...          ...   \n", "110522  2016-06-07T00:00:00Z   56        MARIA ORTIZ            0   \n", "110523  2016-06-07T00:00:00Z   51        MARIA ORTIZ            0   \n", "110524  2016-06-07T00:00:00Z   21        MARIA ORTIZ            0   \n", "110525  2016-06-07T00:00:00Z   38        MARIA ORTIZ            0   \n", "110526  2016-06-07T00:00:00Z   54        MARIA ORTIZ            0   \n", "\n", "        hypertension  diabetes  alcoholism  handicap  sms_received no_show  \n", "0                  1         0           0         0             0      No  \n", "1                  0         0           0         0             0      No  \n", "2                  0         0           0         0             0      No  \n", "3                  0         0           0         0             0      No  \n", "4                  1         1           0         0             0      No  \n", "...              ...       ...         ...       ...           ...     ...  \n", "110522             0         0           0         0             1      No  \n", "110523             0         0           0         0             1      No  \n", "110524             0         0           0         0             1      No  \n", "110525             0         0           0         0             1      No  \n", "110526             0         0           0         0             1      No  \n", "\n", "[110527 rows x 14 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_df = df.copy(deep=True)\n", "\n", "# Convert to small-caps\n", "temp_df.columns = [col.strip().lower().replace(\"-\", \"\").replace(\" \", \"_\") for col in temp_df.columns]\n", "\n", "# Correct `typo`\n", "temp_df.rename(columns={\n", "    'patientid': 'patient_id',\n", "    'appointmentid': 'appointment_id',\n", "    'scheduledday': 'scheduled_day',\n", "    'appointmentday': 'appointment_day',\n", "    'noshow': 'no_show',\n", "    'hipertension': 'hypertension',\n", "    'handcap': 'handicap',\n", "    'noshow': 'no_show'\n", "}, inplace=True)\n", "\n", "temp_df"]}, {"cell_type": "code", "execution_count": 4, "id": "be2615b1", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:54.544792Z", "start_time": "2025-07-05T22:12:54.195979Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_35836\\**********.py:22: FutureWarning: Downcasting behavior in `replace` is deprecated and will be removed in a future version. To retain the old behavior, explicitly call `result.infer_objects(copy=False)`. To opt-in to the future behavior, set `pd.set_option('future.no_silent_downcasting', True)`\n", "  temp_df['no_show']  = temp_df['no_show'].replace({'No': 0, 'Yes': 1}).infer_objects(copy=False)\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "patient_id", "rawType": "int64", "type": "integer"}, {"name": "appointment_id", "rawType": "int64", "type": "integer"}, {"name": "gender", "rawType": "category", "type": "unknown"}, {"name": "scheduled_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "appointment_day", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "neighbourhood", "rawType": "category", "type": "unknown"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "category", "type": "unknown"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}], "ref": "6bb90828-1979-4b67-b2a8-aecb85732434", "rows": [["0", "29872499824296", "5642903", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "62", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "False", "False"], ["1", "558997776694438", "5642503", "M", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "56", "JARDIM DA PENHA", "0", "0", "0", "0", "0", "False", "False"], ["2", "4262962299951", "5642549", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "62", "MATA DA PRAIA", "0", "0", "0", "0", "0", "False", "False"], ["3", "867951213174", "5642828", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "8", "PONTAL DE CAMBURI", "0", "0", "0", "0", "0", "False", "False"], ["4", "8841186448183", "5642494", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "56", "JARDIM DA PENHA", "0", "1", "1", "0", "0", "False", "False"], ["5", "95985133231274", "5626772", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "False", "False"], ["6", "733688164476661", "5630279", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True"], ["7", "3449833394123", "5630575", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "False", "True"], ["8", "56394729949972", "5638447", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "21", "ANDORINHAS", "0", "0", "0", "0", "0", "False", "False"], ["9", "78124564369297", "5629123", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "19", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False"], ["10", "734536231958495", "5630213", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False"], ["11", "7542951368435", "5620163", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "29", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "True"], ["12", "566654781423437", "5634718", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "22", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "False"], ["13", "911394617215919", "5636249", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "28", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False"], ["14", "99884723334928", "5633951", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "54", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False"], ["15", "99948393975", "5620206", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "15", "NOVA PALESTINA", "0", "0", "0", "0", "0", "True", "False"], ["16", "84574392942817", "5633121", "M", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False"], ["17", "14794966191172", "5633460", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "40", "CONQUISTA", "1", "0", "0", "0", "0", "False", "True"], ["18", "17135378245248", "5621836", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "1", "0", "0", "0", "0", "True", "False"], ["19", "7223289184215", "5640433", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "46", "DA PENHA", "0", "0", "0", "0", "0", "False", "False"], ["20", "622257462899397", "5626083", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "30", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "True"], ["21", "12154843752835", "5628338", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "True"], ["22", "863229818887631", "5616091", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "13", "CONQUISTA", "0", "0", "0", "0", "0", "True", "True"], ["23", "213753979425692", "5634142", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False"], ["24", "8734857996885", "5641780", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "65", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False"], ["25", "5819369978796", "5624020", "M", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "46", "CONQUISTA", "0", "1", "0", "0", "0", "True", "False"], ["26", "25787851512", "5641781", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "45", "BENTO FERREIRA", "0", "1", "0", "0", "0", "False", "False"], ["27", "12154843752835", "5628345", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "4", "CONQUISTA", "0", "0", "0", "0", "0", "False", "False"], ["28", "5926171692527", "5642400", "M", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "51", "SÃO PEDRO", "0", "0", "0", "0", "0", "False", "False"], ["29", "1225776163665", "5642186", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "32", "SANTA MARTHA", "0", "0", "0", "0", "0", "False", "False"], ["30", "342815551642", "5628068", "F", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "46", "NOVA PALESTINA", "0", "0", "0", "0", "0", "False", "False"], ["31", "311284853849", "5628907", "M", "2016-04-27 00:00:00", "2016-04-29 00:00:00", "12", "NOVA PALESTINA", "1", "0", "0", "0", "0", "False", "True"], ["32", "52883562769472", "5637908", "M", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "61", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "False", "False"], ["33", "7653516999712", "5616921", "F", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "38", "SÃO CRISTÓVÃO", "1", "0", "0", "0", "0", "True", "False"], ["34", "19999756532994", "5637963", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "79", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "False", "False"], ["35", "78162635161611", "5637968", "M", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "18", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False"], ["36", "72984587621439", "5637975", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "63", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "False"], ["37", "1578131861739", "5637986", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "64", "TABUAZEIRO", "1", "1", "1", "0", "0", "False", "False"], ["38", "5873315843778", "5609446", "M", "2016-04-20 00:00:00", "2016-04-29 00:00:00", "85", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "True", "False"], ["39", "14556231793236", "5639644", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "59", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False"], ["40", "996868412638744", "5635881", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "55", "TABUAZEIRO", "0", "0", "0", "0", "0", "False", "False"], ["41", "822432466381793", "5633339", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "71", "MARUÍPE", "0", "0", "1", "0", "0", "False", "False"], ["42", "25965426543339", "5632906", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "50", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False"], ["43", "71558956872246", "5641620", "F", "2016-04-29 00:00:00", "2016-04-29 00:00:00", "49", "SÃO CRISTÓVÃO", "0", "1", "0", "0", "0", "False", "False"], ["44", "274164858852", "5635414", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "78", "SÃO CRISTÓVÃO", "0", "1", "1", "0", "0", "False", "True"], ["45", "4982378572899", "5635842", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "31", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "False", "False"], ["46", "137943696338", "5615608", "M", "2016-04-25 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "1", "0", "1", "0", "True", "False"], ["47", "589458459955", "5633116", "F", "2016-04-28 00:00:00", "2016-04-29 00:00:00", "39", "MARUÍPE", "0", "1", "1", "0", "0", "False", "False"], ["48", "8545415176986", "5618643", "F", "2016-04-26 00:00:00", "2016-04-29 00:00:00", "58", "SÃO CRISTÓVÃO", "0", "0", "0", "0", "0", "True", "True"], ["49", "92235587471561", "5534656", "F", "2016-03-31 00:00:00", "2016-04-29 00:00:00", "27", "GRANDE VITÓRIA", "0", "0", "0", "0", "0", "True", "True"]], "shape": {"columns": 14, "rows": 110527}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>29872499824296</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>558997776694438</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4262962299951</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>867951213174</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8841186448183</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29</td>\n", "      <td>2016-04-29</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110522</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110523</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110524</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110525</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110526</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>110527 rows × 14 columns</p>\n", "</div>"], "text/plain": ["             patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0        29872499824296         5642903      F    2016-04-29      2016-04-29   \n", "1       558997776694438         5642503      M    2016-04-29      2016-04-29   \n", "2         4262962299951         5642549      F    2016-04-29      2016-04-29   \n", "3          867951213174         5642828      F    2016-04-29      2016-04-29   \n", "4         8841186448183         5642494      F    2016-04-29      2016-04-29   \n", "...                 ...             ...    ...           ...             ...   \n", "110522    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "110523    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "110524   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "110525   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "110526  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "        age      neighbourhood  scholarship  hypertension  diabetes  \\\n", "0        62    JARDIM DA PENHA            0             1         0   \n", "1        56    JARDIM DA PENHA            0             0         0   \n", "2        62      MATA DA PRAIA            0             0         0   \n", "3         8  PONTAL DE CAMBURI            0             0         0   \n", "4        56    JARDIM DA PENHA            0             1         1   \n", "...     ...                ...          ...           ...       ...   \n", "110522   56        MARIA ORTIZ            0             0         0   \n", "110523   51        MARIA ORTIZ            0             0         0   \n", "110524   21        MARIA ORTIZ            0             0         0   \n", "110525   38        MARIA ORTIZ            0             0         0   \n", "110526   54        MARIA ORTIZ            0             0         0   \n", "\n", "        alcoholism handicap  sms_received  no_show  \n", "0                0        0         False    False  \n", "1                0        0         False    False  \n", "2                0        0         False    False  \n", "3                0        0         False    False  \n", "4                0        0         False    False  \n", "...            ...      ...           ...      ...  \n", "110522           0        0          True    False  \n", "110523           0        0          True    False  \n", "110524           0        0          True    False  \n", "110525           0        0          True    False  \n", "110526           0        0          True    False  \n", "\n", "[110527 rows x 14 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Drop unnecessary columns\n", "# temp_df = temp_df.drop(temp_df.columns[0], axis=1)  # Drop the first unnamed column\n", "\n", "# Convert label data types\n", "temp_df['patient_id'] = temp_df['patient_id'].astype('int64')\n", "temp_df['appointment_id'] = temp_df['appointment_id'].astype('int64')\n", "\n", "# Convert `gender`, `neighbourhood`, `handicap`, to categorical type\n", "for col in ['gender', 'neighbourhood', 'handicap']:\n", "    temp_df[col] = temp_df[col].astype('category')\n", "    \n", "# Convert `scheduled_day` and `appointment_day` to datetime\n", "for col in ['scheduled_day', 'appointment_day']:\n", "    temp_df[col] = pd.to_datetime(temp_df[col]).dt.date.astype('datetime64[ns]')\n", "\n", "# Convert `scholarship`, `hypertension`, `diabetes`, `alcoholism`, `sms_received` to boolean type\n", "for col in ['scholarship', 'hypertension', 'diabetes', 'alcoholism']:\n", "    temp_df[col] = temp_df[col].astype('int64')\n", "    \n", "# Convert `no_show` to boolean type\n", "temp_df['sms_received'] = temp_df['sms_received'].astype('bool')\n", "temp_df['no_show']  = temp_df['no_show'].replace({'No': 0, 'Yes': 1}).infer_objects(copy=False)\n", "temp_df['no_show'] = temp_df['no_show'].astype('bool')\n", "\n", "# Change `age` -1 value to 0\n", "temp_df.replace({'age':{-1: 0}}, inplace=True)\n", "\n", "temp_df"]}, {"cell_type": "code", "execution_count": 5, "id": "f3ed0cd2", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:54.771837Z", "start_time": "2025-07-05T22:12:54.752815Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 110527 entries, 0 to 110526\n", "Data columns (total 14 columns):\n", " #   Column           Non-Null Count   Dtype         \n", "---  ------           --------------   -----         \n", " 0   patient_id       110527 non-null  int64         \n", " 1   appointment_id   110527 non-null  int64         \n", " 2   gender           110527 non-null  category      \n", " 3   scheduled_day    110527 non-null  datetime64[ns]\n", " 4   appointment_day  110527 non-null  datetime64[ns]\n", " 5   age              110527 non-null  int64         \n", " 6   neighbourhood    110527 non-null  category      \n", " 7   scholarship      110527 non-null  int64         \n", " 8   hypertension     110527 non-null  int64         \n", " 9   diabetes         110527 non-null  int64         \n", " 10  alcoholism       110527 non-null  int64         \n", " 11  handicap         110527 non-null  category      \n", " 12  sms_received     110527 non-null  bool          \n", " 13  no_show          110527 non-null  bool          \n", "dtypes: bool(2), category(3), datetime64[ns](2), int64(7)\n", "memory usage: 8.1 MB\n"]}], "source": ["# verify the changes\n", "temp_df.info()"]}, {"cell_type": "code", "execution_count": 6, "id": "5e25fa59", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:55.063499Z", "start_time": "2025-07-05T22:12:54.979287Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "patient_id", "rawType": "float64", "type": "float"}, {"name": "appointment_id", "rawType": "float64", "type": "float"}, {"name": "gender", "rawType": "object", "type": "unknown"}, {"name": "scheduled_day", "rawType": "object", "type": "unknown"}, {"name": "appointment_day", "rawType": "object", "type": "unknown"}, {"name": "age", "rawType": "float64", "type": "float"}, {"name": "neighbourhood", "rawType": "object", "type": "unknown"}, {"name": "scholarship", "rawType": "float64", "type": "float"}, {"name": "hypertension", "rawType": "float64", "type": "float"}, {"name": "diabetes", "rawType": "float64", "type": "float"}, {"name": "alcoholism", "rawType": "float64", "type": "float"}, {"name": "handicap", "rawType": "float64", "type": "float"}, {"name": "sms_received", "rawType": "object", "type": "unknown"}, {"name": "no_show", "rawType": "object", "type": "unknown"}], "ref": "cccc874a-a432-4736-86d4-1e9fb9e4970a", "rows": [["count", "110527.0", "110527.0", "110527", "110527", "110527", "110527.0", "110527", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527", "110527"], ["unique", null, null, "2", null, null, null, "81", null, null, null, null, "5.0", "2", "2"], ["top", null, null, "F", null, null, null, "JARDIM CAMBURI", null, null, null, null, "0.0", "False", "False"], ["freq", null, null, "71840", null, null, null, "7717", null, null, null, null, "108286.0", "75045", "88208"], ["mean", "147496265710394.06", "5675305.123426855", null, "2016-05-08 20:33:18.179630080", "2016-05-19 00:57:50.008233472", "37.08888325929411", null, "0.09826558216544373", "0.1972459218109602", "0.07186479321794674", "0.030399811810688793", null, null, null], ["min", "39217.0", "5030230.0", null, "2015-11-10 00:00:00", "2016-04-29 00:00:00", "0.0", null, "0.0", "0.0", "0.0", "0.0", null, null, null], ["25%", "4172614444192.0", "5640285.5", null, "2016-04-29 00:00:00", "2016-05-09 00:00:00", "18.0", null, "0.0", "0.0", "0.0", "0.0", null, null, null], ["50%", "31731838713978.0", "5680573.0", null, "2016-05-10 00:00:00", "2016-05-18 00:00:00", "37.0", null, "0.0", "0.0", "0.0", "0.0", null, null, null], ["75%", "94391720898175.0", "5725523.5", null, "2016-05-20 00:00:00", "2016-05-31 00:00:00", "55.0", null, "0.0", "0.0", "0.0", "0.0", null, null, null], ["max", "999981631772427.0", "5790484.0", null, "2016-06-08 00:00:00", "2016-06-08 00:00:00", "115.0", null, "1.0", "1.0", "1.0", "1.0", null, null, null], ["std", "256094920291738.88", "71295.75153966916", null, null, null, "23.110190247631852", null, "0.29767475410942307", "0.39792134994753325", "0.2582650735076741", "0.17168555541436223", null, null, null]], "shape": {"columns": 14, "rows": 11}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.0</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.0</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>F</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>JARDIM CAMBURI</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>71840</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7717</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>108286.0</td>\n", "      <td>75045</td>\n", "      <td>88208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-08 20:33:18.179630080</td>\n", "      <td>2016-05-19 00:57:50.008233472</td>\n", "      <td>37.088883</td>\n", "      <td>NaN</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921700e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>NaN</td>\n", "      <td>2015-11-10 00:00:00</td>\n", "      <td>2016-04-29 00:00:00</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-04-29 00:00:00</td>\n", "      <td>2016-05-09 00:00:00</td>\n", "      <td>18.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-10 00:00:00</td>\n", "      <td>2016-05-18 00:00:00</td>\n", "      <td>37.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-05-20 00:00:00</td>\n", "      <td>2016-05-31 00:00:00</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>NaN</td>\n", "      <td>2016-06-08 00:00:00</td>\n", "      <td>2016-06-08 00:00:00</td>\n", "      <td>115.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>23.110190</td>\n", "      <td>NaN</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          patient_id  appointment_id  gender                  scheduled_day  \\\n", "count   1.105270e+05    1.105270e+05  110527                         110527   \n", "unique           NaN             NaN       2                            NaN   \n", "top              NaN             NaN       F                            NaN   \n", "freq             NaN             NaN   71840                            NaN   \n", "mean    1.474963e+14    5.675305e+06     NaN  2016-05-08 20:33:18.179630080   \n", "min     3.921700e+04    5.030230e+06     NaN            2015-11-10 00:00:00   \n", "25%     4.172614e+12    5.640286e+06     NaN            2016-04-29 00:00:00   \n", "50%     3.173184e+13    5.680573e+06     NaN            2016-05-10 00:00:00   \n", "75%     9.439172e+13    5.725524e+06     NaN            2016-05-20 00:00:00   \n", "max     9.999816e+14    5.790484e+06     NaN            2016-06-08 00:00:00   \n", "std     2.560949e+14    7.129575e+04     NaN                            NaN   \n", "\n", "                      appointment_day            age   neighbourhood  \\\n", "count                          110527  110527.000000          110527   \n", "unique                            NaN            NaN              81   \n", "top                               NaN            NaN  JARDIM CAMBURI   \n", "freq                              NaN            NaN            7717   \n", "mean    2016-05-19 00:57:50.008233472      37.088883             NaN   \n", "min               2016-04-29 00:00:00       0.000000             NaN   \n", "25%               2016-05-09 00:00:00      18.000000             NaN   \n", "50%               2016-05-18 00:00:00      37.000000             NaN   \n", "75%               2016-05-31 00:00:00      55.000000             NaN   \n", "max               2016-06-08 00:00:00     115.000000             NaN   \n", "std                               NaN      23.110190             NaN   \n", "\n", "          scholarship   hypertension       diabetes     alcoholism  handicap  \\\n", "count   110527.000000  110527.000000  110527.000000  110527.000000  110527.0   \n", "unique            NaN            NaN            NaN            NaN       5.0   \n", "top               NaN            NaN            NaN            NaN       0.0   \n", "freq              NaN            NaN            NaN            NaN  108286.0   \n", "mean         0.098266       0.197246       0.071865       0.030400       NaN   \n", "min          0.000000       0.000000       0.000000       0.000000       NaN   \n", "25%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "50%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "75%          0.000000       0.000000       0.000000       0.000000       NaN   \n", "max          1.000000       1.000000       1.000000       1.000000       NaN   \n", "std          0.297675       0.397921       0.258265       0.171686       NaN   \n", "\n", "       sms_received no_show  \n", "count        110527  110527  \n", "unique            2       2  \n", "top           False   False  \n", "freq          75045   88208  \n", "mean            NaN     NaN  \n", "min             <PERSON><PERSON>  \n", "25%             NaN     NaN  \n", "50%             NaN     NaN  \n", "75%             NaN     NaN  \n", "max             NaN     NaN  \n", "std             NaN     NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_df.describe(include='all')"]}, {"cell_type": "code", "execution_count": 7, "id": "f32835bdaff67679", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:55.796198Z", "start_time": "2025-07-05T22:12:55.290015Z"}}, "outputs": [], "source": ["FILE_PATH = r\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show_cleaned.csv\"\n", "\n", "# Delete existing file if it exists\n", "if os.path.exists(FILE_PATH):\n", "    os.remove(FILE_PATH)\n", "\n", "# Save the cleaned DataFrame to a CSV file\n", "temp_df.to_csv(FILE_PATH, index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}