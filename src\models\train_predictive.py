import sys, os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import pandas as pd
import logging
from sklearn.impute import SimpleImputer
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier

from common.utils import utils
from data import load_data as ld
from globals.basic_settings import app_settings

class PredictiveModelTrainer:
    """
    A class for training predictive models with proper data preprocessing and validation.
    """
    
    def __init__(self, data: pd.DataFrame, target_column: str, test_size: float = 0.2, random_state: int = 42):
        """
        Initialize the predictive model trainer.
        
        Args:
            data: The dataset for training
            target_column: Name of the target variable column
            test_size: Proportion of data to use for testing
            random_state: Random seed for reproducibility
        """
        self.data = data.copy(deep=True)
        self.target_column = target_column
        self.test_size = test_size
        self.random_state = random_state
        self.model = None
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_names = None
        
        # Validate inputs
        if target_column not in data.columns:
            raise ValueError(f"Target column '{target_column}' not found in data")
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def preprocess_data(self):
        """
        Comprehensive data preprocessing with feature selection and encoding.
        """
        # Define columns to exclude (consistent with causal model)
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or col in ['patient_id', 'appointment_id', self.target_column]
        ]

        # Separate features and target
        X = self.data.drop(exclude_columns, axis=1)
        y = self.data[self.target_column]
        
        self.logger.info(f"Original features: {X.shape[1]}")
        
        # Handle categorical variables
        X_encoded = pd.get_dummies(X, drop_first=True)
        self.feature_names = X_encoded.columns.tolist()
        
        self.logger.info(f"Features after encoding: {X_encoded.shape[1]}")
        
        return X_encoded, y
        
    def split_data(self):
        """
        Split data with stratification and proper preprocessing pipeline.
        """
        X, y = self.preprocess_data()
        
        # Stratified split to maintain class distribution
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, 
            test_size=self.test_size, 
            random_state=self.random_state, 
            stratify=y
        )
        
        # Fit imputer on training data only (prevent data leakage)
        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train), 
            columns=X_train.columns,
            index=X_train.index
        )
        
        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test), 
            columns=X_test.columns,
            index=X_test.index
        )
        
        self.logger.info(f"Training set: {X_train_imputed.shape}, Test set: {X_test_imputed.shape}")
        
        return X_train_imputed, X_test_imputed, y_train, y_test
    
    def train(self, model_type: str = 'random_forest', custom_params: dict = None):
        """
        Train model with comprehensive hyperparameter tuning and validation.
        
        Args:
            model_type: Type of model to train ('random_forest', etc.)
            custom_params: Custom parameter grid for tuning
        """
        X_train, X_test, y_train, y_test = self.split_data()

        # Check class distribution
        class_distribution = y_train.value_counts(normalize=True)
        self.logger.info(f"Class distribution in training set: {class_distribution.to_dict()}")

        if model_type != 'random_forest':
            raise ValueError(f"Model type '{model_type}' is not supported.")
            
        base_model = RandomForestClassifier(
            random_state=self.random_state,
            class_weight='balanced'  # Handle class imbalance
        )

        param_grid = custom_params or {
            'n_estimators': [100, 200, 300],
            'max_depth': [None, 10, 20, 30],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2']
        }
        
        # Use StratifiedKFold for cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        
        # Grid search with ROC AUC as primary metric
        grid_search = GridSearchCV(
            base_model, 
            param_grid, 
            scoring='roc_auc', 
            cv=cv, 
            verbose=1, 
            n_jobs=-1,
            return_train_score=True
        )
        
        self.logger.info("Starting model training with hyperparameter tuning...")
        grid_search.fit(X_train, y_train)
        
        self.model = grid_search.best_estimator_
        self.logger.info(f"Best parameters found: {grid_search.best_params_}")
        self.logger.info(f"Best cross-validation score: {grid_search.best_score_:.4f}")
        
        # Evaluate on test set
        y_pred = self.model.predict(X_test)
        y_proba = self.model.predict_proba(X_test)[:, 1]
        
        # Comprehensive evaluation using utils
        metrics = utils.evaluate_model(y_test, y_pred, y_proba)
        self.logger.info(f"Test set evaluation metrics: {metrics}")
        
        # Additional detailed evaluation
        self._detailed_evaluation(X_test, y_test, y_pred, y_proba)
        
        # Save model with metadata
        self._save_model_with_metadata(grid_search)
        
        return self.model
    
    def _detailed_evaluation(self, X_test, y_test, y_pred, y_proba):
        """
        Provide detailed model evaluation including feature importance.
        """
        # Classification report
        print("\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        # Confusion matrix
        print("\nConfusion Matrix:")
        print(confusion_matrix(y_test, y_pred))
        
        # Feature importance for tree-based models
        if hasattr(self.model, 'feature_importances_'):
            feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\nTop 10 Most Important Features:")
            print(feature_importance.head(10))
            
    def _save_model_with_metadata(self, grid_search):
        """
        Save model with comprehensive metadata.
        """
        try:
            model_metadata = {
                'best_params': grid_search.best_params_,
                'best_score': grid_search.best_score_,
                'feature_names': self.feature_names,
                'target_column': self.target_column,
                'model_type': type(self.model).__name__
            }
            
            # Use the utils class save_model method
            app_base_path = app_settings.get("app_base_path")
            model_path = os.path.join(app_base_path, "models/predictive_model.pkl")
            
            # Create models directory if it doesn't exist
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            
            utils.save_model(self.model, model_path)
            self.logger.info(f"Predictive model saved successfully to {model_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving model: {e}")
        
    def predict(self, new_data: pd.DataFrame):
        """
        Make predictions on new data using the trained model.
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")
        
        # Apply same preprocessing as training
        processed_data = self._preprocess_new_data(new_data)
        return self.model.predict(processed_data)
    
    def predict_proba(self, new_data: pd.DataFrame):
        """
        Get prediction probabilities for new data.
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")
        
        processed_data = self._preprocess_new_data(new_data)
        return self.model.predict_proba(processed_data)
    
    def _preprocess_new_data(self, new_data: pd.DataFrame) -> pd.DataFrame:
        """Apply same preprocessing pipeline to new data."""
        return pd.DataFrame(
            self.imputer.transform(new_data),
            columns=new_data.columns,
            index=new_data.index,
        )
    
    def get_model_summary(self):
        """
        Get a summary of the trained model.
        """
        if self.model is None:
            return {"status": "No model trained yet."}
        
        return {
            'model_type': type(self.model).__name__,
            'n_features': len(self.feature_names) if self.feature_names else 0,
            'target_column': self.target_column,
            'feature_names': self.feature_names
        }

def main():
    """Example usage of the PredictiveModelTrainer."""
    try:
        # Load feature engineered data
        data = ld.load_app_feature_data()
        
        # Initialize predictive model trainer
        trainer = PredictiveModelTrainer(
            data=data,
            target_column='no_show'
        )
        
        # Train the model
        model = trainer.train(model_type='random_forest')
        
        print("Predictive model training completed successfully!")
        
    except Exception as e:
        print(f"An error occurred: {e}")
        logging.error(f"An error occurred: {e}", exc_info=True)

if __name__ == "__main__":
    main()