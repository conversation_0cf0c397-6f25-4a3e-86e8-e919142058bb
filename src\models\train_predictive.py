import sys
import os
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union

import numpy as np
import pandas as pd
from sklearn.impute import SimpleImputer
from sklearn.metrics import (
    classification_report, confusion_matrix, roc_auc_score,
    accuracy_score, precision_score, recall_score, f1_score
)
from sklearn.model_selection import train_test_split, StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.ensemble import GradientBoostingClassifier

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.utils import save_model, load_model
from data import load_data as ld
from globals.basic_settings import config

class PredictiveModelTrainer:
    """
    A comprehensive class for training predictive models with proper data preprocessing,
    validation, and model management.

    Features:
    - Multiple model types support
    - Automated hyperparameter tuning
    - Comprehensive evaluation metrics
    - Model persistence with metadata
    - Robust error handling and logging
    """

    # Supported model types and their configurations
    SUPPORTED_MODELS = {
        'random_forest': {
            'class': RandomForestClassifier,
            'default_params': {
                'n_estimators': [100, 200, 300],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4],
                'max_features': ['sqrt', 'log2']
            }
        },
        'logistic_regression': {
            'class': LogisticRegression,
            'default_params': {
                'C': [0.1, 1.0, 10.0],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga'],
                'max_iter': [1000]
            }
        },
        'gradient_boosting': {
            'class': GradientBoostingClassifier,
            'default_params': {
                'n_estimators': [100, 200],
                'learning_rate': [0.05, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'subsample': [0.8, 1.0]
            }
        },
        'svm': {
            'class': SVC,
            'default_params': {
                'C': [0.1, 1.0, 10.0],
                'kernel': ['rbf', 'linear'],
                'gamma': ['scale', 'auto']
            }
        }
    }

    def __init__(self,
                 data: pd.DataFrame,
                 target_column: str,
                 test_size: float = 0.2,
                 random_state: int = 42,
                 model_name: str = "predictive_model"):
        """
        Initialize the predictive model trainer.

        Args:
            data: The dataset for training
            target_column: Name of the target variable column
            test_size: Proportion of data to use for testing (0.0 to 1.0)
            random_state: Random seed for reproducibility
            model_name: Name for saving the model

        Raises:
            ValueError: If target_column not found or invalid parameters
        """
        # Input validation
        self._validate_inputs(data, target_column, test_size)

        # Core attributes
        self.data = data.copy(deep=True)
        self.target_column = target_column
        self.test_size = test_size
        self.random_state = random_state
        self.model_name = model_name

        # Model and preprocessing components
        self.model = None
        self.best_params = None
        self.cv_score = None
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_names = None

        # Evaluation metrics storage
        self.train_metrics = None
        self.test_metrics = None
        self.feature_importance = None

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.logger.info(f"PredictiveModelTrainer initialized for target '{target_column}'")
        self.logger.info(f"Dataset shape: {data.shape}")

    def _validate_inputs(self, data: pd.DataFrame, target_column: str, test_size: float):
        """Validate initialization inputs."""
        if not isinstance(data, pd.DataFrame):
            raise ValueError("Data must be a pandas DataFrame")

        if data.empty:
            raise ValueError("Data cannot be empty")

        if target_column not in data.columns:
            raise ValueError(f"Target column '{target_column}' not found in data")

        if not 0.0 < test_size < 1.0:
            raise ValueError("test_size must be between 0.0 and 1.0")

        # Check if target has at least 2 classes
        unique_targets = data[target_column].nunique()
        if unique_targets < 2:
            raise ValueError(f"Target column must have at least 2 unique values, found {unique_targets}")
        
    def preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Comprehensive data preprocessing with feature selection and encoding.

        Returns:
            Tuple of (features_dataframe, target_series)
        """
        # Define columns to exclude (consistent with causal model)
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or
               col in ['patient_id', 'appointment_id', self.target_column]
        ]

        # Separate features and target
        X = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
        y = self.data[self.target_column]

        self.logger.info(f"Original features: {X.shape[1]}")
        self.logger.info(f"Excluded columns: {len(exclude_columns)}")

        # Handle categorical variables with proper encoding
        categorical_columns = X.select_dtypes(include=['object', 'category']).columns
        if len(categorical_columns) > 0:
            self.logger.info(f"Encoding {len(categorical_columns)} categorical columns")
            X_encoded = pd.get_dummies(X, columns=categorical_columns, drop_first=True)
        else:
            X_encoded = X.copy()

        # Store feature names for later use
        self.feature_names = X_encoded.columns.tolist()

        self.logger.info(f"Features after encoding: {X_encoded.shape[1]}")

        # Check for any remaining non-numeric columns
        non_numeric = X_encoded.select_dtypes(exclude=[np.number]).columns
        if len(non_numeric) > 0:
            self.logger.warning(f"Non-numeric columns found: {list(non_numeric)}")

        return X_encoded, y

    def split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """
        Split data with stratification and proper preprocessing pipeline.

        Returns:
            Tuple of (X_train, X_test, y_train, y_test)
        """
        X, y = self.preprocess_data()

        # Check class distribution before splitting
        class_counts = y.value_counts()
        self.logger.info(f"Class distribution: {class_counts.to_dict()}")

        # Ensure minimum samples per class for stratification
        min_class_size = class_counts.min()
        if min_class_size < 2:
            self.logger.warning("Some classes have very few samples. Stratification may not work properly.")
            stratify = None
        else:
            stratify = y

        # Stratified split to maintain class distribution
        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=self.test_size,
            random_state=self.random_state,
            stratify=stratify
        )

        # Fit imputer on training data only (prevent data leakage)
        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )

        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test),
            columns=X_test.columns,
            index=X_test.index
        )

        # Log data quality information
        self.logger.info(f"Training set: {X_train_imputed.shape}, Test set: {X_test_imputed.shape}")
        self.logger.info(f"Missing values in training set: {X_train_imputed.isnull().sum().sum()}")
        self.logger.info(f"Missing values in test set: {X_test_imputed.isnull().sum().sum()}")

        return X_train_imputed, X_test_imputed, y_train, y_test
    
    def train(self,
              model_type: str = 'random_forest',
              custom_params: Optional[Dict] = None,
              scoring: str = 'roc_auc',
              cv_folds: int = 5) -> Any:
        """
        Train model with comprehensive hyperparameter tuning and validation.

        Args:
            model_type: Type of model to train (see SUPPORTED_MODELS)
            custom_params: Custom parameter grid for tuning
            scoring: Scoring metric for cross-validation
            cv_folds: Number of cross-validation folds

        Returns:
            Trained model

        Raises:
            ValueError: If model_type is not supported
        """
        # Validate model type
        if model_type not in self.SUPPORTED_MODELS:
            available_models = list(self.SUPPORTED_MODELS.keys())
            raise ValueError(f"Model type '{model_type}' not supported. Available: {available_models}")

        self.logger.info(f"Training {model_type} model...")

        # Split data
        X_train, X_test, y_train, y_test = self.split_data()

        # Check class distribution
        class_distribution = y_train.value_counts(normalize=True)
        self.logger.info(f"Class distribution in training set: {class_distribution.to_dict()}")

        # Check for class imbalance
        min_class_ratio = class_distribution.min()
        if min_class_ratio < 0.1:
            self.logger.warning(f"Severe class imbalance detected (min class: {min_class_ratio:.3f})")

        # Get model configuration
        model_config = self.SUPPORTED_MODELS[model_type]
        model_class = model_config['class']

        # Initialize base model with appropriate parameters
        base_model_params = {'random_state': self.random_state}

        # Add class balancing for applicable models
        if hasattr(model_class(), 'class_weight'):
            base_model_params['class_weight'] = 'balanced'

        # Special handling for SVM probability
        if model_type == 'svm':
            base_model_params['probability'] = True

        base_model = model_class(**base_model_params)

        # Get parameter grid
        param_grid = custom_params or model_config['default_params']

        # Use StratifiedKFold for cross-validation
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=self.random_state)

        # Grid search with specified scoring metric
        self.logger.info(f"Starting hyperparameter tuning with {scoring} scoring...")

        with warnings.catch_warnings():
            warnings.simplefilter("ignore")  # Suppress sklearn warnings during grid search

            grid_search = GridSearchCV(
                base_model,
                param_grid,
                scoring=scoring,
                cv=cv,
                verbose=3,
                n_jobs=-1,
                return_train_score=True,
                error_score='raise'  # Raise errors instead of returning NaN
            )

            try:
                grid_search.fit(X_train, y_train)
            except Exception as e:
                self.logger.error(f"Grid search failed: {e}")
                raise

        # Store results
        self.model = grid_search.best_estimator_
        self.best_params = grid_search.best_params_
        self.cv_score = grid_search.best_score_

        self.logger.info(f"Best parameters found: {self.best_params}")
        self.logger.info(f"Best cross-validation score: {self.cv_score:.4f}")

        # Evaluate on test set
        y_pred = self.model.predict(X_test)

        # Get probabilities if available
        if hasattr(self.model, 'predict_proba'):
            y_proba = self.model.predict_proba(X_test)[:, 1]
        else:
            y_proba = None
            self.logger.warning("Model does not support probability prediction")

        # Calculate comprehensive metrics
        self.test_metrics = self._calculate_metrics(y_test, y_pred, y_proba)
        self.logger.info(f"Test set evaluation metrics: {self.test_metrics}")

        # Additional detailed evaluation
        self._detailed_evaluation(X_test, y_test, y_pred, y_proba)

        # Save model with metadata
        self._save_model_with_metadata()

        return self.model

    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
        """
        Calculate comprehensive evaluation metrics.

        Args:
            y_true: True labels
            y_pred: Predicted labels
            y_proba: Predicted probabilities (optional)

        Returns:
            Dictionary of metrics
        """
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
        }

        # Add AUC if probabilities are available
        if y_proba is not None:
            try:
                metrics['roc_auc'] = roc_auc_score(y_true, y_proba)
            except ValueError as e:
                self.logger.warning(f"Could not calculate ROC AUC: {e}")
                metrics['roc_auc'] = None

        return metrics

    def _detailed_evaluation(self, X_test: pd.DataFrame, y_test: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray]):
        """
        Provide detailed model evaluation including feature importance.
        """
        self.logger.info("Generating detailed evaluation report...")

        # Classification report
        self.logger.info("\nClassification Report:")
        report = classification_report(y_test, y_pred, output_dict=True)
        for class_name, metrics in report.items():
            if isinstance(metrics, dict):
                self.logger.info(f"  {class_name}: {metrics}")

        # Confusion matrix
        cm = confusion_matrix(y_test, y_pred)
        self.logger.info(f"\nConfusion Matrix:\n{cm}")

        # Feature importance for applicable models
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance = pd.DataFrame({
                'feature': self.feature_names,
                'importance': self.model.feature_importances_
            }).sort_values('importance', ascending=False)

            self.logger.info("\nTop 10 Most Important Features:")
            for idx, row in self.feature_importance.head(10).iterrows():
                self.logger.info(f"  {row['feature']}: {row['importance']:.4f}")

        # Model-specific insights
        if hasattr(self.model, 'coef_'):
            # For linear models, show coefficient information
            n_features_to_show = min(10, len(self.feature_names))
            coef_abs = np.abs(self.model.coef_[0])
            top_indices = np.argsort(coef_abs)[-n_features_to_show:][::-1]

            self.logger.info(f"\nTop {n_features_to_show} Features by Coefficient Magnitude:")
            for idx in top_indices:
                feature_name = self.feature_names[idx]
                coef_value = self.model.coef_[0][idx]
                self.logger.info(f"  {feature_name}: {coef_value:.4f}")

    def _save_model_with_metadata(self):
        """
        Save model with comprehensive metadata.
        """
        try:
            # Prepare comprehensive metadata
            model_metadata = {
                'model_name': self.model_name,
                'model_type': type(self.model).__name__,
                'best_params': self.best_params,
                'cv_score': self.cv_score,
                'test_metrics': self.test_metrics,
                'feature_names': self.feature_names,
                'target_column': self.target_column,
                'n_features': len(self.feature_names) if self.feature_names else 0,
                'training_data_shape': self.data.shape,
                'random_state': self.random_state
            }

            # Add feature importance if available
            if self.feature_importance is not None:
                model_metadata['top_features'] = self.feature_importance.head(10).to_dict('records')

            # Get model save path
            model_path = config.data_paths.model_path

            # Save model with metadata
            save_model(self.model, model_path, model_metadata)
            self.logger.info(f"Predictive model saved successfully to {model_path}")

        except Exception as e:
            self.logger.error(f"Error saving model: {e}")
            raise
        
    def predict(self, new_data: pd.DataFrame) -> np.ndarray:
        """
        Make predictions on new data using the trained model.

        Args:
            new_data: DataFrame with same structure as training data

        Returns:
            Array of predictions
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")

        # Apply same preprocessing as training
        processed_data = self._preprocess_new_data(new_data)
        return self.model.predict(processed_data)

    def predict_proba(self, new_data: pd.DataFrame) -> np.ndarray:
        """
        Get prediction probabilities for new data.

        Args:
            new_data: DataFrame with same structure as training data

        Returns:
            Array of prediction probabilities
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet. Call train() first.")

        if not hasattr(self.model, 'predict_proba'):
            raise ValueError("Model does not support probability prediction")

        processed_data = self._preprocess_new_data(new_data)
        return self.model.predict_proba(processed_data)

    def _preprocess_new_data(self, new_data: pd.DataFrame) -> pd.DataFrame:
        """
        Apply same preprocessing pipeline to new data.

        Args:
            new_data: Raw data to preprocess

        Returns:
            Preprocessed data ready for prediction
        """
        if self.feature_names is None:
            raise ValueError("Model has not been trained yet. Feature names not available.")

        # Apply same exclusions as training
        exclude_columns = [
            col for col in new_data.columns
            if col.startswith('neighbourhood_') or
               col in ['patient_id', 'appointment_id', self.target_column]
        ]

        # Remove excluded columns
        X = new_data.drop(columns=[col for col in exclude_columns if col in new_data.columns])

        # Handle categorical encoding (same as training)
        categorical_columns = X.select_dtypes(include=['object', 'category']).columns
        if len(categorical_columns) > 0:
            X_encoded = pd.get_dummies(X, columns=categorical_columns, drop_first=True)
        else:
            X_encoded = X.copy()

        # Ensure same features as training
        missing_features = set(self.feature_names) - set(X_encoded.columns)
        extra_features = set(X_encoded.columns) - set(self.feature_names)

        # Add missing features with zeros
        for feature in missing_features:
            X_encoded[feature] = 0

        # Remove extra features
        X_encoded = X_encoded[self.feature_names]

        # Apply imputation
        X_imputed = pd.DataFrame(
            self.imputer.transform(X_encoded),
            columns=X_encoded.columns,
            index=X_encoded.index
        )

        return X_imputed

    def get_model_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive summary of the trained model.

        Returns:
            Dictionary containing model information
        """
        if self.model is None:
            return {"status": "No model trained yet."}

        summary = {
            'model_name': self.model_name,
            'model_type': type(self.model).__name__,
            'n_features': len(self.feature_names) if self.feature_names else 0,
            'target_column': self.target_column,
            'best_params': self.best_params,
            'cv_score': self.cv_score,
            'test_metrics': self.test_metrics,
            'training_data_shape': self.data.shape,
            'feature_names': self.feature_names[:10] if self.feature_names else None  # First 10 features
        }

        # Add feature importance summary if available
        if self.feature_importance is not None:
            summary['top_5_features'] = self.feature_importance.head(5).to_dict('records')

        return summary

    def load_model(self, model_path: str) -> 'PredictiveModelTrainer':
        """
        Load a previously trained model.

        Args:
            model_path: Path to the saved model

        Returns:
            Self for method chaining
        """
        try:
            self.model = load_model(model_path)
            self.logger.info(f"Model loaded successfully from {model_path}")
            return self
        except Exception as e:
            self.logger.error(f"Error loading model: {e}")
            raise

def main():
    """
    Comprehensive example usage of the refactored PredictiveModelTrainer.
    Demonstrates training multiple models and comparing their performance.
    """
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        logger.info("Starting predictive model training pipeline...")

        # Load feature engineered data
        data = ld.load_app_feature_data()
        logger.info(f"Loaded data with shape: {data.shape}")

        # Initialize predictive model trainer
        trainer = PredictiveModelTrainer(
            data=data,
            target_column='no_show',
            model_name='appointment_no_show_predictor'
        )

        # Models to train and compare
        models_to_train = [
            ('random_forest', 'Random Forest'),
            ('logistic_regression', 'Logistic Regression'),
            ('gradient_boosting', 'Gradient Boosting')
        ]

        results = []

        for model_type, model_name in models_to_train:
            logger.info(f"\n{'='*60}")
            logger.info(f"Training {model_name} ({model_type})")
            logger.info(f"{'='*60}")

            try:
                # Train the model
                model = trainer.train(model_type=model_type)

                # Get model summary
                summary = trainer.get_model_summary()
                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'summary': summary
                })

                logger.info(f"✓ {model_name} training completed successfully!")

            except Exception as e:
                logger.error(f"✗ Failed to train {model_name}: {e}")
                results.append({
                    'model_type': model_type,
                    'model_name': model_name,
                    'error': str(e)
                })

        # Summary of all results
        logger.info(f"\n{'='*60}")
        logger.info("TRAINING RESULTS SUMMARY")
        logger.info(f"{'='*60}")

        for result in results:
            model_name = result['model_name']
            if 'error' in result:
                logger.error(f"{model_name}: FAILED - {result['error']}")
            else:
                summary = result['summary']
                cv_score = summary.get('cv_score', 'N/A')
                test_metrics = summary.get('test_metrics', {})
                accuracy = test_metrics.get('accuracy', 'N/A')

                logger.info(f"{model_name}:")
                logger.info(f"  CV Score: {cv_score}")
                logger.info(f"  Test Accuracy: {accuracy}")
                logger.info(f"  Features: {summary.get('n_features', 'N/A')}")

        logger.info("\n✓ Predictive model training pipeline completed!")

    except Exception as e:
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    main()