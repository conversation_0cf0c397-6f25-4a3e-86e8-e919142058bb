{"cells": [{"cell_type": "markdown", "id": "f18ea164ec61c74f", "metadata": {}, "source": ["<h1>Data Overview</h1>"]}, {"cell_type": "markdown", "id": "6424626284217a31", "metadata": {}, "source": ["<h2>1. Import Libraries</h2>"]}, {"cell_type": "code", "execution_count": 3, "id": "e7a8f22ea3e171c2", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:09.736536Z", "start_time": "2025-07-05T22:12:09.731952Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "b0b30b76aca8219c", "metadata": {}, "source": ["<h2>2. Loading the Dataset and Initial Explore</h2>"]}, {"cell_type": "code", "execution_count": 4, "id": "35e764968da946fe", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.025170Z", "start_time": "2025-07-05T22:12:09.832252Z"}}, "outputs": [], "source": ["df_raw = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/notebooks/data/medical_appointment_no_show.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "fd35b4f426910314", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.085424Z", "start_time": "2025-07-05T22:12:10.056954Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PatientId", "rawType": "float64", "type": "float"}, {"name": "AppointmentID", "rawType": "int64", "type": "integer"}, {"name": "Gender", "rawType": "object", "type": "string"}, {"name": "ScheduledDay", "rawType": "object", "type": "string"}, {"name": "AppointmentDay", "rawType": "object", "type": "string"}, {"name": "Age", "rawType": "int64", "type": "integer"}, {"name": "Neighbourhood", "rawType": "object", "type": "string"}, {"name": "Scholarship", "rawType": "int64", "type": "integer"}, {"name": "Hipertension", "rawType": "int64", "type": "integer"}, {"name": "Diabetes", "rawType": "int64", "type": "integer"}, {"name": "Alcoholism", "rawType": "int64", "type": "integer"}, {"name": "Handcap", "rawType": "int64", "type": "integer"}, {"name": "SMS_received", "rawType": "int64", "type": "integer"}, {"name": "No-show", "rawType": "object", "type": "string"}], "ref": "553d3743-7d25-4780-ae17-61b1e4c45e0e", "rows": [["0", "29872499824296.0", "5642903", "F", "2016-04-29T18:38:08Z", "2016-04-29T00:00:00Z", "62", "JARDIM DA PENHA", "0", "1", "0", "0", "0", "0", "No"], ["1", "558997776694438.0", "5642503", "M", "2016-04-29T16:08:27Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "0", "0", "0", "0", "0", "No"], ["2", "4262962299951.0", "5642549", "F", "2016-04-29T16:19:04Z", "2016-04-29T00:00:00Z", "62", "MATA DA PRAIA", "0", "0", "0", "0", "0", "0", "No"], ["3", "867951213174.0", "5642828", "F", "2016-04-29T17:29:31Z", "2016-04-29T00:00:00Z", "8", "PONTAL DE CAMBURI", "0", "0", "0", "0", "0", "0", "No"], ["4", "8841186448183.0", "5642494", "F", "2016-04-29T16:07:23Z", "2016-04-29T00:00:00Z", "56", "JARDIM DA PENHA", "0", "1", "1", "0", "0", "0", "No"], ["5", "95985133231274.0", "5626772", "F", "2016-04-27T08:36:51Z", "2016-04-29T00:00:00Z", "76", "REPÚBLICA", "0", "1", "0", "0", "0", "0", "No"], ["6", "733688164476661.0", "5630279", "F", "2016-04-27T15:05:12Z", "2016-04-29T00:00:00Z", "23", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["7", "3449833394123.0", "5630575", "F", "2016-04-27T15:39:58Z", "2016-04-29T00:00:00Z", "39", "GOIABEIRAS", "0", "0", "0", "0", "0", "0", "Yes"], ["8", "56394729949972.0", "5638447", "F", "2016-04-29T08:02:16Z", "2016-04-29T00:00:00Z", "21", "ANDORINHAS", "0", "0", "0", "0", "0", "0", "No"], ["9", "78124564369297.0", "5629123", "F", "2016-04-27T12:48:25Z", "2016-04-29T00:00:00Z", "19", "CONQUISTA", "0", "0", "0", "0", "0", "0", "No"]], "shape": {"columns": 14, "rows": 10}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>9.598513e+13</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T08:36:51Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7.336882e+14</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:05:12Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3.449833e+12</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:39:58Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>5.639473e+13</td>\n", "      <td>5638447</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T08:02:16Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>ANDORINHAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>7.812456e+13</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T12:48:25Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      PatientId  AppointmentID Gender          ScheduledDay  \\\n", "0  2.987250e+13        5642903      F  2016-04-29T18:38:08Z   \n", "1  5.589978e+14        5642503      M  2016-04-29T16:08:27Z   \n", "2  4.262962e+12        5642549      F  2016-04-29T16:19:04Z   \n", "3  8.679512e+11        5642828      F  2016-04-29T17:29:31Z   \n", "4  8.841186e+12        5642494      F  2016-04-29T16:07:23Z   \n", "5  9.598513e+13        5626772      F  2016-04-27T08:36:51Z   \n", "6  7.336882e+14        5630279      F  2016-04-27T15:05:12Z   \n", "7  3.449833e+12        5630575      F  2016-04-27T15:39:58Z   \n", "8  5.639473e+13        5638447      F  2016-04-29T08:02:16Z   \n", "9  7.812456e+13        5629123      F  2016-04-27T12:48:25Z   \n", "\n", "         AppointmentDay  Age      Neighbourhood  Scholarship  Hipertension  \\\n", "0  2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0             1   \n", "1  2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0             0   \n", "2  2016-04-29T00:00:00Z   62      MATA DA PRAIA            0             0   \n", "3  2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0             0   \n", "4  2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0             1   \n", "5  2016-04-29T00:00:00Z   76          REPÚBLICA            0             1   \n", "6  2016-04-29T00:00:00Z   23         GOIABEIRAS            0             0   \n", "7  2016-04-29T00:00:00Z   39         GOIABEIRAS            0             0   \n", "8  2016-04-29T00:00:00Z   21         ANDORINHAS            0             0   \n", "9  2016-04-29T00:00:00Z   19          CONQUISTA            0             0   \n", "\n", "   Diabetes  Alcoholism  Handcap  SMS_received No-show  \n", "0         0           0        0             0      No  \n", "1         0           0        0             0      No  \n", "2         0           0        0             0      No  \n", "3         0           0        0             0      No  \n", "4         1           0        0             0      No  \n", "5         0           0        0             0      No  \n", "6         0           0        0             0     Yes  \n", "7         0           0        0             0     Yes  \n", "8         0           0        0             0      No  \n", "9         0           0        0             0      No  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display the first 10 rows of the dataset\n", "df_raw.head(10)"]}, {"cell_type": "code", "execution_count": 6, "id": "80e15c1715753c39", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.161033Z", "start_time": "2025-07-05T22:12:10.151478Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of the dataframe -> Rows:  110527\n", "Shape of the dataframe -> Columns:  14\n"]}], "source": ["# Display the shape of the dataset\n", "print(f\"Shape of the dataframe -> Rows: \", df_raw.shape[0])\n", "print(f\"Shape of the dataframe -> Columns: \", df_raw.shape[1])"]}, {"cell_type": "code", "execution_count": 7, "id": "4f7045cd35a598bb", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.373359Z", "start_time": "2025-07-05T22:12:10.340491Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 110527 entries, 0 to 110526\n", "Data columns (total 14 columns):\n", " #   Column          Non-Null Count   Dtype  \n", "---  ------          --------------   -----  \n", " 0   PatientId       110527 non-null  float64\n", " 1   AppointmentID   110527 non-null  int64  \n", " 2   Gender          110527 non-null  object \n", " 3   ScheduledDay    110527 non-null  object \n", " 4   AppointmentDay  110527 non-null  object \n", " 5   Age             110527 non-null  int64  \n", " 6   Neighbourhood   110527 non-null  object \n", " 7   Scholarship     110527 non-null  int64  \n", " 8   Hipertension    110527 non-null  int64  \n", " 9   Diabetes        110527 non-null  int64  \n", " 10  Alcoholism      110527 non-null  int64  \n", " 11  Handcap         110527 non-null  int64  \n", " 12  SMS_received    110527 non-null  int64  \n", " 13  No-show         110527 non-null  object \n", "dtypes: float64(1), int64(8), object(5)\n", "memory usage: 11.8+ MB\n", "Data types of Columns\n", " None\n"]}], "source": ["# Data types of the columns\n", "print(f\"Data types of Columns\\n\",df_raw.info())\n"]}, {"cell_type": "code", "execution_count": 8, "id": "8f7bc9b8c2516e8b", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.705362Z", "start_time": "2025-07-05T22:12:10.700501Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column Names:\n", " ['PatientId', 'AppointmentID', 'Gender', 'ScheduledDay', 'AppointmentDay', 'Age', 'Neighbourhood', 'Scholarship', 'Hipertension', 'Diabetes', 'Alcoholism', 'Handcap', 'SMS_received', 'No-show']\n"]}], "source": ["print(f\"Column Names:\\n\", df_raw.columns.to_list())"]}, {"cell_type": "code", "execution_count": 9, "id": "e290c5487875b40", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:10.881803Z", "start_time": "2025-07-05T22:12:10.827445Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Null values in the dataframe:\n", " PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64\n", "-------------------------------------------------------\n", "-------------------------------------------------------\n", "Missing values in the dataframe:\n", " PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64\n"]}], "source": ["# Check for `null` and `missing` values in the dataset\n", "print(f\"Null values in the dataframe:\\n\", df_raw.isnull().sum())\n", "print(f\"-------------------------------------------------------\")\n", "print(f\"-------------------------------------------------------\")\n", "print(f\"Missing values in the dataframe:\\n\", df_raw.isna().sum())"]}, {"cell_type": "code", "execution_count": 10, "id": "ebe5c59f3183f5ea", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:11.067150Z", "start_time": "2025-07-05T22:12:11.010138Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "PatientId", "rawType": "float64", "type": "float"}, {"name": "AppointmentID", "rawType": "float64", "type": "float"}, {"name": "Age", "rawType": "float64", "type": "float"}, {"name": "Scholarship", "rawType": "float64", "type": "float"}, {"name": "Hipertension", "rawType": "float64", "type": "float"}, {"name": "Diabetes", "rawType": "float64", "type": "float"}, {"name": "Alcoholism", "rawType": "float64", "type": "float"}, {"name": "Handcap", "rawType": "float64", "type": "float"}, {"name": "SMS_received", "rawType": "float64", "type": "float"}], "ref": "c98a39bf-2d36-4587-908f-7fe917028fd3", "rows": [["count", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0"], ["mean", "147496265710394.06", "5675305.123426855", "37.08887421173107", "0.09826558216544373", "0.1972459218109602", "0.07186479321794674", "0.030399811810688793", "0.022247957512643968", "0.32102563174608917"], ["std", "256094920291738.88", "71295.75153966916", "23.110204963681948", "0.29767475410942307", "0.39792134994753325", "0.2582650735076741", "0.17168555541436223", "0.1615427258143932", "0.46687273170178245"], ["min", "39217.84439", "5030230.0", "-1.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"], ["25%", "4172614444192.0", "5640285.5", "18.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"], ["50%", "31731838713978.0", "5680573.0", "37.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0"], ["75%", "94391720898175.0", "5725523.5", "55.0", "0.0", "0.0", "0.0", "0.0", "0.0", "1.0"], ["max", "999981631772427.0", "5790484.0", "115.0", "1.0", "1.0", "1.0", "1.0", "4.0", "1.0"]], "shape": {"columns": 9, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Age</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>37.088874</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>0.022248</td>\n", "      <td>0.321026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>23.110205</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>0.161543</td>\n", "      <td>0.466873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921784e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>18.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>37.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>55.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>115.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          PatientId  AppointmentID            Age    Scholarship  \\\n", "count  1.105270e+05   1.105270e+05  110527.000000  110527.000000   \n", "mean   1.474963e+14   5.675305e+06      37.088874       0.098266   \n", "std    2.560949e+14   7.129575e+04      23.110205       0.297675   \n", "min    3.921784e+04   5.030230e+06      -1.000000       0.000000   \n", "25%    4.172614e+12   5.640286e+06      18.000000       0.000000   \n", "50%    3.173184e+13   5.680573e+06      37.000000       0.000000   \n", "75%    9.439172e+13   5.725524e+06      55.000000       0.000000   \n", "max    9.999816e+14   5.790484e+06     115.000000       1.000000   \n", "\n", "        Hipertension       Diabetes     Alcoholism        Handcap  \\\n", "count  110527.000000  110527.000000  110527.000000  110527.000000   \n", "mean        0.197246       0.071865       0.030400       0.022248   \n", "std         0.397921       0.258265       0.171686       0.161543   \n", "min         0.000000       0.000000       0.000000       0.000000   \n", "25%         0.000000       0.000000       0.000000       0.000000   \n", "50%         0.000000       0.000000       0.000000       0.000000   \n", "75%         0.000000       0.000000       0.000000       0.000000   \n", "max         1.000000       1.000000       1.000000       4.000000   \n", "\n", "        SMS_received  \n", "count  110527.000000  \n", "mean        0.321026  \n", "std         0.466873  \n", "min         0.000000  \n", "25%         0.000000  \n", "50%         0.000000  \n", "75%         1.000000  \n", "max         1.000000  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Describe the dataset\n", "df_raw.describe()"]}, {"cell_type": "code", "execution_count": 11, "id": "586aa98642cbf93f", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:11.389547Z", "start_time": "2025-07-05T22:12:11.196919Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "PatientId", "rawType": "float64", "type": "float"}, {"name": "AppointmentID", "rawType": "float64", "type": "float"}, {"name": "Gender", "rawType": "object", "type": "unknown"}, {"name": "ScheduledDay", "rawType": "object", "type": "unknown"}, {"name": "AppointmentDay", "rawType": "object", "type": "unknown"}, {"name": "Age", "rawType": "float64", "type": "float"}, {"name": "Neighbourhood", "rawType": "object", "type": "unknown"}, {"name": "Scholarship", "rawType": "float64", "type": "float"}, {"name": "Hipertension", "rawType": "float64", "type": "float"}, {"name": "Diabetes", "rawType": "float64", "type": "float"}, {"name": "Alcoholism", "rawType": "float64", "type": "float"}, {"name": "Handcap", "rawType": "float64", "type": "float"}, {"name": "SMS_received", "rawType": "float64", "type": "float"}, {"name": "No-show", "rawType": "object", "type": "unknown"}], "ref": "0ee2cdf1-2caf-4d5d-a9bf-e19c9a94afd3", "rows": [["count", "110527.0", "110527.0", "110527", "110527", "110527", "110527.0", "110527", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527.0", "110527"], ["unique", null, null, "2", "103549", "27", null, "81", null, null, null, null, null, null, "2"], ["top", null, null, "F", "2016-05-06T07:09:54Z", "2016-06-06T00:00:00Z", null, "JARDIM CAMBURI", null, null, null, null, null, null, "No"], ["freq", null, null, "71840", "24", "4692", null, "7717", null, null, null, null, null, null, "88208"], ["mean", "147496265710394.06", "5675305.123426855", null, null, null, "37.08887421173107", null, "0.09826558216544373", "0.1972459218109602", "0.07186479321794674", "0.030399811810688793", "0.022247957512643968", "0.32102563174608917", null], ["std", "256094920291738.88", "71295.75153966916", null, null, null, "23.110204963681948", null, "0.29767475410942307", "0.39792134994753325", "0.2582650735076741", "0.17168555541436223", "0.1615427258143932", "0.46687273170178245", null], ["min", "39217.84439", "5030230.0", null, null, null, "-1.0", null, "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null], ["25%", "4172614444192.0", "5640285.5", null, null, null, "18.0", null, "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null], ["50%", "31731838713978.0", "5680573.0", null, null, null, "37.0", null, "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", null], ["75%", "94391720898175.0", "5725523.5", null, null, null, "55.0", null, "0.0", "0.0", "0.0", "0.0", "0.0", "1.0", null], ["max", "999981631772427.0", "5790484.0", null, null, null, "115.0", null, "1.0", "1.0", "1.0", "1.0", "4.0", "1.0", null]], "shape": {"columns": 14, "rows": 11}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>103549</td>\n", "      <td>27</td>\n", "      <td>NaN</td>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>F</td>\n", "      <td>2016-05-06T07:09:54Z</td>\n", "      <td>2016-06-06T00:00:00Z</td>\n", "      <td>NaN</td>\n", "      <td>JARDIM CAMBURI</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>71840</td>\n", "      <td>24</td>\n", "      <td>4692</td>\n", "      <td>NaN</td>\n", "      <td>7717</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>88208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>37.088874</td>\n", "      <td>NaN</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>0.022248</td>\n", "      <td>0.321026</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>23.110205</td>\n", "      <td>NaN</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>0.161543</td>\n", "      <td>0.466873</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921784e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>37.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>115.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           PatientId  AppointmentID  Gender          ScheduledDay  \\\n", "count   1.105270e+05   1.105270e+05  110527                110527   \n", "unique           NaN            NaN       2                103549   \n", "top              NaN            NaN       F  2016-05-06T07:09:54Z   \n", "freq             NaN            NaN   71840                    24   \n", "mean    1.474963e+14   5.675305e+06     NaN                   NaN   \n", "std     2.560949e+14   7.129575e+04     NaN                   NaN   \n", "min     3.921784e+04   5.030230e+06     NaN                   NaN   \n", "25%     4.172614e+12   5.640286e+06     NaN                   NaN   \n", "50%     3.173184e+13   5.680573e+06     NaN                   NaN   \n", "75%     9.439172e+13   5.725524e+06     NaN                   NaN   \n", "max     9.999816e+14   5.790484e+06     NaN                   NaN   \n", "\n", "              AppointmentDay            Age   Neighbourhood    Scholarship  \\\n", "count                 110527  110527.000000          110527  110527.000000   \n", "unique                    27            NaN              81            NaN   \n", "top     2016-06-06T00:00:00Z            NaN  JARDIM CAMBURI            NaN   \n", "freq                    4692            NaN            7717            NaN   \n", "mean                     NaN      37.088874             NaN       0.098266   \n", "std                      NaN      23.110205             NaN       0.297675   \n", "min                      NaN      -1.000000             NaN       0.000000   \n", "25%                      NaN      18.000000             NaN       0.000000   \n", "50%                      NaN      37.000000             NaN       0.000000   \n", "75%                      NaN      55.000000             NaN       0.000000   \n", "max                      NaN     115.000000             NaN       1.000000   \n", "\n", "         Hipertension       Diabetes     Alcoholism        Handcap  \\\n", "count   110527.000000  110527.000000  110527.000000  110527.000000   \n", "unique            NaN            NaN            NaN            NaN   \n", "top               NaN            NaN            NaN            NaN   \n", "freq              NaN            NaN            NaN            NaN   \n", "mean         0.197246       0.071865       0.030400       0.022248   \n", "std          0.397921       0.258265       0.171686       0.161543   \n", "min          0.000000       0.000000       0.000000       0.000000   \n", "25%          0.000000       0.000000       0.000000       0.000000   \n", "50%          0.000000       0.000000       0.000000       0.000000   \n", "75%          0.000000       0.000000       0.000000       0.000000   \n", "max          1.000000       1.000000       1.000000       4.000000   \n", "\n", "         SMS_received No-show  \n", "count   110527.000000  110527  \n", "unique            NaN       2  \n", "top               NaN      No  \n", "freq              NaN   88208  \n", "mean         0.321026     NaN  \n", "std          0.466873     NaN  \n", "min          0.000000     NaN  \n", "25%          0.000000     NaN  \n", "50%          0.000000     NaN  \n", "75%          1.000000     NaN  \n", "max          1.000000     NaN  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Summary of the dataset\n", "df_raw.describe(include='all')"]}, {"cell_type": "code", "execution_count": 12, "id": "6436a46e5a9b3079", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.062092Z", "start_time": "2025-07-05T22:12:11.598706Z"}}, "outputs": [{"data": {"text/plain": ["array([[<Axes: title={'center': 'PatientId'}>,\n", "        <Axes: title={'center': 'AppointmentID'}>,\n", "        <Axes: title={'center': 'Age'}>],\n", "       [<Axes: title={'center': 'Scholarship'}>,\n", "        <Axes: title={'center': 'Hipertension'}>,\n", "        <Axes: title={'center': 'Diabetes'}>],\n", "       [<Axes: title={'center': 'Alcoholism'}>,\n", "        <Axes: title={'center': 'Handcap'}>,\n", "        <Axes: title={'center': 'SMS_received'}>]], dtype=object)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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**********************************//79qK2txYcffgh7e3sYGhqiZ8+eeOyxx/DRRx812N+1a9fw0ksvQSaTwcrKCq+++iqUSqVaTP1Hp+oeHUpJScGcOXMgl8thaGiIUaNG4cSJE83Wj+hBcr+/nePHjyMgIABmZmYwMDCAi4sLvvzyS7WYukeNsrKy8Oqrr6J3794wMjLCggUL8M9//hMAYGtrKz6KdPcjf9u2bYO7uzuMjY3x0EMPwcfHp0E56h4v+u233zBhwgQ89NBDsLGxwdy5c6FSqdRi161bh8GDB+Ohhx6CiYkJBg4ciIULFzaof/3HDnfs2AF3d3cYGRnBxMQEY8eORU5OjlpM3SNSp06darHNIiJ1ffv2RVxcHMrLy7F+/XoAjT92uG3bNnh7e8Pa2hqGhoZwcHDA/PnzUVlZ2eh2T506hTFjxsDY2Bi9e/fGrFmz8Oeff6rFCIKAtWvXYsiQITA0NESvXr3w/PPP4/z582KMp6cnMjIycOnSJbVHJ+tUVVXhww8/xMCBAyGVStG7d2+88soruH79utq+9u3bB09PT5ibm8PQ0BB9+/bFc88916BMpDlMvqgBd3d3HDlyBOHh4Thy5Aiqq6sbjXvvvfcwefJkKBQKJCcnIz09HVOnTsWlS5fU4oqLizF58mRMmTIFO3bswPjx47FgwQKkpKSIMdevX8eIESOQlZWFDz74ADt27ICXlxciIyMxa9asZst78+ZNAMDixYuRkZGBDRs2YMCAAfD09Gz0PYmPP/4Y+/btw8qVK7Fr1y4MHDgQsbGxiIqKwksvvYSMjAxs27YN06ZNa/S9j+eeew6PPvootm/fjvnz5yM1NRVvvfVWC0f1bwsXLsT58+fx+eef4/PPP8fVq1fh6emp1qASdUd1d5nrT4IgtOrzrfnt7N+/Hx4eHrh16xY+/fRTfPfddxgyZAheeOEFJCcnN9jmq6++Cj09PWzevBlff/013njjDcyePRsA8M033yAnJwc5OTl4/PHHAQDR0dF46aWX4OjoiC+//BKbN29GeXk5Ro4cidOnT6ttu7q6GgEBARgzZgy+++47vPrqq1i9ejWWL18uxqSlpSEsLAyjRo1Ceno6vv32W7z11ltNnrTVSU1NxTPPPANTU1Ns3boVSUlJKC0thaenJ7KzsxvE30+bRfQgmzBhAnR0dHDo0KEmY86dO4cJEyYgKSkJmZmZiIiIwJdffgl/f/8GsdXV1ZgwYQLGjBmDb7/9FrNmzcL69evxwgsvqMXNmDEDERER8PLywrfffou1a9fi1KlTGDFiBK5duwYAWLt2LTw8PCCXy8W2qu4CTG1tLZ555hksW7YMQUFByMjIwLJly7B79254enri9u3bAP6+KO3r6wt9fX188cUXyMzMxLJly2BsbIyqqipNHUaqTyCq548//hCefPJJAYAAQNDT0xNGjBghxMTECOXl5YIgCML58+cFHR0dYfLkyc1ua9SoUQIA4ciRI2rLHR0dBR8fH3F+/vz5jca98cYbgkQiEc6ePSsuAyAsXry4yX3euXNHqK6uFsaMGSM8++yz4vILFy4IAIRHHnlEqKqqUvuMn5+fMGTIkGbrsnjxYgGAEBsbq7Y8LCxMMDAwEGpra8Vl/fr1E6ZOnSrO79+/XwAgPP7442pxFy9eFPT09ITXXnut2X0TdVUbNmwQ25Kmpn79+onx9/PbGThwoODi4iJUV1erlcHPz0+wtrYWampq1Mr08ssvNyjvihUrBADChQsX1JZfvnxZ0NXVFWbPnq22vLy8XJDL5UJgYKC4bOrUqQIA4csvv1SLnTBhgmBvby/Oz5o1S+jZs2cTR069/vv37xcEQRBqamoEhUIhODs7i/WpK4elpaUwYsQIcVlb2iyiB1FdW3Ds2LEmY6ysrAQHBwdBEP73m2pKbW2tUF1dLRw8eFAAIPz888/iurp24aOPPlL7zNKlSwUAQnZ2tiAIgpCTkyMAEOLi4tTiCgsLBUNDQ2HevHniMl9fX7X2s87WrVsFAML27dvVlh87dkwAIKxdu1YQBEH4+uuvBQBCfn5+k3UizeOdL2rA3Nwc//rXv3Ds2DEsW7YMzzzzDH799VcsWLAAzs7O+OOPP7B7927U1NRg5syZLW5PLpfjiSeeUFv22GOPqd0h27dvHxwdHRvEhYSEQBAE7Nu3r9l9fPrpp3j88cdhYGAAXV1d6OnpYe/evThz5kyD2ICAAOjp6akte+KJJ/Dzzz8jLCwMP/74I8rKyprcV0BAQIO6/PXXXw16RWpMUFCQ2mMB/fr1w4gRI7B///4WP0vUlW3atAnHjh1rMD355JOt+nxLv53ffvsNv/zyCyZPngwAanfXJkyYgKKiIpw9e1Ztm88991yry//jjz/izp07ePnll9W2bWBggFGjRjW4yy6RSBpc+a7f7j3xxBO4desWXnrpJXz33Xet6kn17NmzuHr1KoKDg9UeA3/ooYfw3HPPITc3t8HjQvfTZhE96IQW7s6fP38eQUFBkMvl0NHRgZ6eHkaNGgUAjZ6D1LVRdYKCggBAbMt27twJiUSCKVOmqLU1crkcgwcPblXPpzt37kTPnj3h7++vto0hQ4ZALpeL2xgyZAj09fUxffp0bNy4kU/haAk73KAmDR06VHwBtbq6Gm+//TZWr16N2NhYyGQyAGjVi6fm5uYNlkmlUvG2N/D3e2Z3dzddR6FQiOubsmrVKsydOxevv/46PvjgA1hYWEBHRwfvvvtuow2ftbV1g2ULFiyAsbExUlJS8Omnn0JHRwdPPfUUli9frvYSbmP1qeus4+76NEUulze67Oeff27xs0RdmYODQ4PfEgDIZDIUFha2+PmWfjt1j+JERkYiMjKy0W3UT24aawuaUrf9YcOGNbq+/vuwRkZGDTrwkEql+Ouvv8T54OBg3LlzB4mJiXjuuedQW1uLYcOG4cMPP8TYsWMb3U9dW9hY2RUKBWpra1FaWgojIyNx+f20WUQPssrKSty4cQPOzs6Nrq+oqMDIkSNhYGCADz/8EI8++iiMjIxQWFiISZMmNfiN6erqNvg91rVtdb/ta9euQRAEWFlZNbrPAQMGtFjua9eu4datW9DX1290fV1b+Mgjj2DPnj2IjY3FzJkzUVlZiQEDBiA8PBxvvvlmi/uhe8Pki1pFT08PixcvxurVq1FQUICJEycCAK5cuQIbG5v73r65uTmKiooaLL969SoAwMLCosnPpqSkwNPTE+vWrVNbXl5e3mh8Y2N06OrqYs6cOZgzZw5u3bqFPXv2YOHChfDx8UFhYaHaicz9KC4ubnRZYwkqEf1PS7+dujZiwYIFmDRpUqPbsLe3V5tvy3g9ddv/+uuv0a9fv1Z/riWvvPIKXnnlFVRWVuLQoUNYvHgx/Pz88Ouvvza6n7r6NtVe9ujRA7169dJY+YgeZBkZGaipqYGnp2ej6/ft24erV6/iwIED4t0uAE2OE3jnzh3cuHFD7W9+Xdt2d1smkUjwr3/9q9GemFvTO7OFhQXMzc2RmZnZ6HoTExPx/0eOHImRI0eipqYGx48fR3x8PCIiImBlZYUXX3yxxX1R2/GxQ2qgsT/qwP9unysUCnh7e0NHR6dBwnOvxowZg9OnT+Onn35SW75p0yZIJBKMHj26yc9KJJIGjdF//vOfBj1/tVbPnj3x/PPPY+bMmbh58yYuXrx4T9tpzNatW9UeYbh06RIOHz7cZMNORH9r6bdjb28POzs7/Pzzz+Jd+/rT3SccTWnqrpCPjw90dXXx3//+t8nt3w9jY2OMHz8eixYtQlVVFU6dOtVonL29PR5++GGkpqaqHY/Kykps375d7AGRiO7P5cuXERkZCZlMhhkzZjQaU3cBp/45SF3viI3ZsmWL2nxqaioAiG2Zn58fBEHA77//3mg7c/dduPpPEdXx8/PDjRs3UFNT0+g26l+IAgAdHR24ubnhk08+AYAG52OkObzzRQ34+PigT58+8Pf3x8CBA1FbW4v8/HzExcXhoYcewptvvon+/ftj4cKF+OCDD3D79m2xG+PTp0/jjz/+wJIlS9q0z7feegubNm2Cr68v3n//ffTr1w8ZGRlYu3Yt3njjDTz66KNNftbPzw8ffPABFi9ejFGjRuHs2bN4//33YWtrizt37rRq//7+/uI4H71798alS5ewZs0a9OvXD3Z2dm2qS3NKSkrw7LPPIjQ0FEqlEosXL4aBgQEWLFigsX0QdUet+e2sX78e48ePh4+PD0JCQvDwww/j5s2bOHPmDH766Sd89dVXLe6n7sTmo48+wtSpU6Gnpwd7e3v0798f77//PhYtWoTz589j3Lhx6NWrF65du4ajR4/C2Ni4ze1eaGgoDA0N4eHhAWtraxQXFyMmJgYymazZxxtjY2MxefJk+Pn5YcaMGVCpVFixYgVu3bqFZcuWtakMRAQUFBSI70WVlJTgX//6FzZs2AAdHR2kp6ejd+/ejX5uxIgR6NWrF15//XUsXrwYenp62LJlS5OvEujr6yMuLg4VFRUYNmwYDh8+jA8//BDjx48X33/18PDA9OnT8corr+D48eN46qmnYGxsjKKiImRnZ8PZ2RlvvPEGgL/bq2+++Qbr1q2Dq6srevTogaFDh+LFF1/Eli1bMGHCBLz55pt44oknoKenhytXrmD//v145pln8Oyzz+LTTz/Fvn374Ovri759++Kvv/7CF198AQDiwNKkeUy+qIF33nkH3333HVavXo2ioiKoVCpYW1vDy8sLCxYsgIODAwDg/fffh52dHeLj4zF58mTo6urCzs4O4eHhbd5n7969cfjwYSxYsAALFixAWVkZBgwYgNjYWMyZM6fZzy5atAh//vknkpKSEBsbC0dHR3z66adIT09v1YupADB69Ghs374dn3/+OcrKyiCXyzF27Fi8++67DTrnuB/R0dE4duwYXnnlFZSVleGJJ55AWloaHnnkEY3tg6g7as1vZ/To0Th69CiWLl2KiIgIlJaWwtzcHI6OjggMDGzVfjw9PbFgwQJs3LgRiYmJqK2txf79+8Xljo6O+Oijj7B161aoVCrI5XIMGzYMr7/+epvrNHLkSCQnJ+PLL79EaWkpLCws8OSTT2LTpk1NnuwBf7+gb2xsjJiYGLzwwgvQ0dHB8OHDsX//fowYMaLN5SB60L3yyisA/k6OevbsCQcHB7z99tt47bXXmv0tmpubIyMjA3PnzsWUKVNgbGyMZ555Btu2bROHqLibnp4edu7cifDwcHz44YcwNDREaGgoVqxYoRa3fv16DB8+HOvXr8fatWtRW1sLhUIBDw8PtY7J3nzzTZw6dQoLFy6EUqmEIAgQBAE6OjrYsWMHPvroI2zevBkxMTHQ1dVFnz59MGrUKPEi05AhQ5CVlYXFixejuLgYDz30EJycnLBjxw54e3tr4tBSIyRCS924ENF9O3DgAEaPHo2vvvoKzz//fEcXh6jL4G+HiIi6E77zRUREREREpAVMvoiIiIiIiLSAjx0SERERERFpAe98ERERERERaQGTLyIiIiIiIi1g8kVERERERKQFHOerBbW1tbh69SpMTEzEkcyJ6P4JgoDy8nIoFAr06PFgXgdi+0LUPti+sH0hai/3274w+WrB1atXYWNj09HFIOq2CgsL0adPn44uRodg+0LUvti+sH0hai/32r4w+WqBiYkJgL8PsKmpaQeXhqj7KCsrg42NjfgbexCxfSFqH2xf2L4QtZf7bV+YfLWg7la9qakpGy+idvAgPw7D9oWofbF9YftC1F7utX15MB+EJiIiIiIi0jImX0RERERERFrQ5uTr0KFD8Pf3h0KhgEQiwbfffqu2XhAEREVFQaFQwNDQEJ6enjh16pRajEqlwuzZs2FhYQFjY2MEBATgypUrajGlpaUIDg6GTCaDTCZDcHAwbt26pRZz+fJl+Pv7w9jYGBYWFggPD0dVVZVazMmTJzFq1CgYGhri4Ycfxvvvvw9BENpabSIiIiIiovvS5uSrsrISgwcPRkJCQqPrY2NjsWrVKiQkJODYsWOQy+UYO3YsysvLxZiIiAikp6cjLS0N2dnZqKiogJ+fH2pqasSYoKAg5OfnIzMzE5mZmcjPz0dwcLC4vqamBr6+vqisrER2djbS0tKwfft2zJ07V4wpKyvD2LFjoVAocOzYMcTHx2PlypVYtWpVW6tNRERERER0f4T7AEBIT08X52trawW5XC4sW7ZMXPbXX38JMplM+PTTTwVBEIRbt24Jenp6Qlpamhjz+++/Cz169BAyMzMFQRCE06dPCwCE3NxcMSYnJ0cAIPzyyy+CIAjCDz/8IPTo0UP4/fffxZitW7cKUqlUUCqVgiAIwtq1awWZTCb89ddfYkxMTIygUCiE2traVtVRqVQKAMRtEpFm8LfFY0DUXvjb4jEgai/3+9vS6DtfFy5cQHFxMby9vcVlUqkUo0aNwuHDhwEAeXl5qK6uVotRKBRwcnISY3JyciCTyeDm5ibGDB8+HDKZTC3GyckJCoVCjPHx8YFKpUJeXp4YM2rUKEilUrWYq1ev4uLFi5qsOhERERERUbM0mnwVFxcDAKysrNSWW1lZieuKi4uhr6+PXr16NRtjaWnZYPuWlpZqMfX306tXL+jr6zcbUzdfF1OfSqVCWVmZ2kRERERERHS/2qW3w/r93guC0GJf+PVjGovXRIzw/zvbaKo8MTExYicfMpmMo8MTEREREZFGaHSQZblcDuDvu0rW1tbi8pKSEvGOk1wuR1VVFUpLS9XufpWUlGDEiBFizLVr1xps//r162rbOXLkiNr60tJSVFdXq8XUv8NVUlICoOHduToLFizAnDlzxPm6Uaxbo//8jFbFXVzm26o4IqI6rWlf2LYQNe7QoUNYsWIF8vLyUFRUhPT0dEycOFFcLwgClixZgs8++wylpaVwc3PDJ598gkGDBokxKpUKkZGR2Lp1K27fvo0xY8Zg7dq16NOnjxhTWlqK8PBw7NixAwAQEBCA+Ph49OzZU4y5fPkyZs6ciX379sHQ0BBBQUFYuXIl9PX1xZiTJ09i1qxZOHr0KMzMzDBjxgy8++677TJoNM9diLRLo3e+bG1tIZfLsXv3bnFZVVUVDh48KCZWrq6u0NPTU4spKipCQUGBGOPu7g6lUomjR4+KMUeOHIFSqVSLKSgoQFFRkRiTlZUFqVQKV1dXMebQoUNq3c9nZWVBoVCgf//+jdZBKpWKo8FzVHgiIqKujz01E1Fn0eY7XxUVFfjtt9/E+QsXLiA/Px9mZmbo27cvIiIiEB0dDTs7O9jZ2SE6OhpGRkYICgoCAMhkMkybNg1z586Fubk5zMzMEBkZCWdnZ3h5eQEAHBwcMG7cOISGhmL9+vUAgOnTp8PPzw/29vYAAG9vbzg6OiI4OBgrVqzAzZs3ERkZidDQUDFhCgoKwpIlSxASEoKFCxfi3LlziI6OxnvvvdcuV4+IiIio8xk/fjzGjx/f6DpBELBmzRosWrQIkyZNAgBs3LgRVlZWSE1NxYwZM6BUKpGUlITNmzeL5yopKSmwsbHBnj174OPjgzNnziAzMxO5ublih2GJiYlwd3fH2bNnYW9vj6ysLJw+fRqFhYVih2FxcXEICQnB0qVLYWpqii1btuCvv/5CcnIypFIpnJyc8Ouvv2LVqlWYM2cOz1+Iurg23/k6fvw4XFxc4OLiAgCYM2cOXFxc8N577wEA5s2bh4iICISFhWHo0KH4/fffkZWVBRMTE3Ebq1evxsSJExEYGAgPDw8YGRnh+++/h46OjhizZcsWODs7w9vbG97e3njsscewefNmcb2Ojg4yMjJgYGAADw8PBAYGYuLEiVi5cqUYI5PJsHv3bly5cgVDhw5FWFgY5syZo/ZYIRERET242FMzEWlTm+98eXp6ip1WNEYikSAqKgpRUVFNxhgYGCA+Ph7x8fFNxpiZmSElJaXZsvTt2xc7d+5sNsbZ2RmHDh1qNoaIiIgeTM311Hzp0iUxRps9Ndd/NeLunpptbW0brYdKpYJKpRLn2VszUefULr0dEhEREXUlXbmnZoC9NRN1FUy+iIiI6IF1d0/Nd2uqp+bmYlrTU3P9/Wiip2bg796alUqlOBUWFjZfcSLqEEy+iIiI6IHVHXpqBthbM1FXweSLiIiIurWKigrk5+cjPz8fwP96ar58+TIkEonYU3N6ejoKCgoQEhLSZE/Ne/fuxYkTJzBlypQme2rOzc1Fbm4uQkNDm+yp+cSJE9i7d2+jPTVLpVKEhISgoKAA6enpiI6OZk+HRN2ERgdZJiIiIupsjh8/jtGjR4vzdb0eT506FcnJyZg3bx5u376NsLAwcZDlxnpq1tXVRWBgoDjIcnJycoOemsPDw8VeEQMCAtTGFqvrqTksLAweHh5qgyzXqeupeebMmRg6dCh69erFnpqJuhGJ0FzXhYSysjLIZDIolcoWb+FzlHii1mvLb6u70nT7wraF6G9sX1p/DHjuQtQ299u+8LFDIiIiIiIiLWDyRUREREREpAVMvoiIiIiIiLSAyRcREREREZEWMPkioi7jzp07eOedd2BrawtDQ0MMGDAA77//Pmpra8UYQRAQFRUFhUIBQ0NDeHp64tSpU2rbUalUmD17NiwsLGBsbIyAgABcuXJFLaa0tBTBwcGQyWSQyWQIDg7GrVu31GIuX74Mf39/GBsbw8LCAuHh4Wpj8xARERHdjckXEXUZy5cvx6effoqEhAScOXMGsbGxWLFiBeLj48WY2NhYrFq1CgkJCTh27BjkcjnGjh2L8vJyMSYiIgLp6elIS0tDdnY2Kioq4Ofnh5qaGjEmKCgI+fn5yMzMRGZmJvLz8xEcHCyur6mpga+vLyorK5GdnY20tDRs374dc+fO1c7BICIioi6H43wRUZeRk5ODZ555Br6+f3d53L9/f2zduhXHjx8H8PddrzVr1mDRokWYNGkSAGDjxo2wsrJCamoqZsyYAaVSiaSkJGzevFkcHDUlJQU2NjbYs2cPfHx8cObMGWRmZiI3Nxdubm4AgMTERLi7u+Ps2bOwt7dHVlYWTp8+jcLCQigUCgBAXFwcQkJCsHTp0ge2e2siIiJqGu98EVGX8eSTT2Lv3r349ddfAQA///wzsrOzMWHCBADAhQsXUFxcLA5wCgBSqRSjRo3C4cOHAQB5eXmorq5Wi1EoFHBychJjcnJyIJPJxMQLAIYPHw6ZTKYW4+TkJCZeAODj4wOVSoW8vLx2OgJERETUlfHOFxF1GW+//TaUSiUGDhwIHR0d1NTUYOnSpXjppZcAAMXFxQAAKysrtc9ZWVnh0qVLYoy+vj569erVIKbu88XFxbC0tGywf0tLS7WY+vvp1asX9PX1xZj6VCoVVCqVOF9WVtbquhMREVHXxztfRNRlbNu2DSkpKUhNTcVPP/2EjRs3YuXKldi4caNanEQiUZsXBKHBsvrqxzQWfy8xd4uJiRE78JDJZLCxsWm2TERERNS9MPkioi7jn//8J+bPn48XX3wRzs7OCA4OxltvvYWYmBgAgFwuB4AGd55KSkrEu1RyuRxVVVUoLS1tNubatWsN9n/9+nW1mPr7KS0tRXV1dYM7YnUWLFgApVIpToWFhW09BERERNSFMfkioi7jzz//RI8e6s2Wjo6O2NW8ra0t5HI5du/eLa6vqqrCwYMHMWLECACAq6sr9PT01GKKiopQUFAgxri7u0OpVOLo0aNizJEjR6BUKtViCgoKUFRUJMZkZWVBKpXC1dW10fJLpVKYmpqqTURERPTg4DtfRNRl+Pv7Y+nSpejbty8GDRqEEydOYNWqVXj11VcB/P0YYEREBKKjo2FnZwc7OztER0fDyMgIQUFBAACZTIZp06Zh7ty5MDc3h5mZGSIjI+Hs7Cz2fujg4IBx48YhNDQU69evBwBMnz4dfn5+sLe3BwB4e3vD0dERwcHBWLFiBW7evInIyEiEhoYyqSIiIqJGMfkioi4jPj4e7777LsLCwlBSUgKFQoEZM2bgvffeE2PmzZuH27dvIywsDKWlpXBzc0NWVhZMTEzEmNWrV0NXVxeBgYG4ffs2xowZg+TkZOjo6IgxW7ZsQXh4uNgrYkBAABISEsT1Ojo6yMjIQFhYGDw8PGBoaIigoCCsXLlSC0eCiIiIuiKJIAhCRxeiMysrK4NMJoNSqWzxanb/+Rmt2ubFZb6aKBpRl9aW31Z3pen2hW0L0d/YvrT+GPDchaht7rd94TtfREREREREWsDki4iIiIiISAuYfBEREREREWkBky8iIiIiIiItYPJFRERERESkBUy+iIiIiIiItIDJFxERERERkRYw+SIiIiIiItICJl9ERERERERaoPHk686dO3jnnXdga2sLQ0NDDBgwAO+//z5qa2vFGEEQEBUVBYVCAUNDQ3h6euLUqVNq21GpVJg9ezYsLCxgbGyMgIAAXLlyRS2mtLQUwcHBkMlkkMlkCA4Oxq1bt9RiLl++DH9/fxgbG8PCwgLh4eGoqqrSdLWJiIiIiIiapfHka/ny5fj000+RkJCAM2fOIDY2FitWrEB8fLwYExsbi1WrViEhIQHHjh2DXC7H2LFjUV5eLsZEREQgPT0daWlpyM7ORkVFBfz8/FBTUyPGBAUFIT8/H5mZmcjMzER+fj6Cg4PF9TU1NfD19UVlZSWys7ORlpaG7du3Y+7cuZquNhERERERUbN0Nb3BnJwcPPPMM/D19QUA9O/fH1u3bsXx48cB/H3Xa82aNVi0aBEmTZoEANi4cSOsrKyQmpqKGTNmQKlUIikpCZs3b4aXlxcAICUlBTY2NtizZw98fHxw5swZZGZmIjc3F25ubgCAxMREuLu74+zZs7C3t0dWVhZOnz6NwsJCKBQKAEBcXBxCQkKwdOlSmJqaarr6REREREREjdL4na8nn3wSe/fuxa+//goA+Pnnn5GdnY0JEyYAAC5cuIDi4mJ4e3uLn5FKpRg1ahQOHz4MAMjLy0N1dbVajEKhgJOTkxiTk5MDmUwmJl4AMHz4cMhkMrUYJycnMfECAB8fH6hUKuTl5Wm66kRERERERE3S+J2vt99+G0qlEgMHDoSOjg5qamqwdOlSvPTSSwCA4uJiAICVlZXa56ysrHDp0iUxRl9fH7169WoQU/f54uJiWFpaNti/paWlWkz9/fTq1Qv6+vpiTH0qlQoqlUqcLysra3XdiYiIiIiImqLxO1/btm1DSkoKUlNT8dNPP2Hjxo1YuXIlNm7cqBYnkUjU5gVBaLCsvvoxjcXfS8zdYmJixA48ZDIZbGxsmi0TERERdX3sMIyItEHjydc///lPzJ8/Hy+++CKcnZ0RHByMt956CzExMQAAuVwOAA3uPJWUlIh3qeRyOaqqqlBaWtpszLVr1xrs//r162ox9fdTWlqK6urqBnfE6ixYsABKpVKcCgsL23oIiIiIqIthh2FEpA0aT77+/PNP9OihvlkdHR3xypGtrS3kcjl2794trq+qqsLBgwcxYsQIAICrqyv09PTUYoqKilBQUCDGuLu7Q6lU4ujRo2LMkSNHoFQq1WIKCgpQVFQkxmRlZUEqlcLV1bXR8kulUpiamqpNRERE1L3d3WFY//798fzzz8Pb27vJDsOcnJywceNG/Pnnn0hNTQUAscOwuLg4eHl5wcXFBSkpKTh58iT27NkDAGKHYZ9//jnc3d3h7u6OxMRE7Ny5E2fPngUAscOwlJQUuLi4wMvLC3FxcUhMTOTrEERdnMaTL39/fyxduhQZGRm4ePEi0tPTsWrVKjz77LMA/n4MMCIiAtHR0UhPT0dBQQFCQkJgZGSEoKAgAIBMJsO0adMwd+5c7N27FydOnMCUKVPg7Ows9n7o4OCAcePGITQ0FLm5ucjNzUVoaCj8/Pxgb28PAPD29oajoyOCg4Nx4sQJ7N27F5GRkQgNDWVSRURERKKu3mGYSqVCWVmZ2kREnY/GO9yIj4/Hu+++i7CwMJSUlEChUGDGjBl47733xJh58+bh9u3bCAsLQ2lpKdzc3JCVlQUTExMxZvXq1dDV1UVgYCBu376NMWPGIDk5GTo6OmLMli1bEB4eLjZyAQEBSEhIENfr6OggIyMDYWFh8PDwgKGhIYKCgrBy5UpNV5uIiIi6sK7eYVhMTAyWLFnS1moTkZZpPPkyMTHBmjVrsGbNmiZjJBIJoqKiEBUV1WSMgYEB4uPj1Z61rs/MzAwpKSnNlqdv377YuXNnS8UmIiKiB9jdHYYNGjQI+fn5iIiIgEKhwNSpU8W4ztph2IIFCzBnzhxxvqysjJ2GEXVCGk++iIiIiLqauzsMAwBnZ2dcunQJMTExmDp1qlqHYdbW1uLnmuow7O67XyUlJeL76K3tMOzIkSNq61vqMEwqlUIqld5r9YlISzT+zhcRERFRV9PVOwwjoq6Bd76IiIjogVfXYVjfvn0xaNAgnDhxAqtWrcKrr74KQL3DMDs7O9jZ2SE6OrrJDsPMzc1hZmaGyMjIJjsMW79+PQBg+vTpTXYYtmLFCty8eZMdhhF1E0y+iIiI6IHHDsOISBskgiAIHV2IzqysrAwymQxKpbLFq03952e0apsXl/lqomhEXVpbflvdlabbF7YtRH9j+9L6Y8BzF6K2ud/2he98ERERERERaQGTLyIiIiIiIi1g8kVERERERKQFTL6IiIiIiIi0gMkXERERERGRFjD5IiIiIiIi0gImX0RERERERFrA5IuIiIiIiEgLmHwRERERERFpAZMvIiIiIiIiLWDyRURdyu+//44pU6bA3NwcRkZGGDJkCPLy8sT1giAgKioKCoUChoaG8PT0xKlTp9S2oVKpMHv2bFhYWMDY2BgBAQG4cuWKWkxpaSmCg4Mhk8kgk8kQHByMW7duqcVcvnwZ/v7+MDY2hoWFBcLDw1FVVdVudSciIqKujckXEXUZpaWl8PDwgJ6eHnbt2oXTp08jLi4OPXv2FGNiY2OxatUqJCQk4NixY5DL5Rg7dizKy8vFmIiICKSnpyMtLQ3Z2dmoqKiAn58fampqxJigoCDk5+cjMzMTmZmZyM/PR3BwsLi+pqYGvr6+qKysRHZ2NtLS0rB9+3bMnTtXK8eCiIiIuh7dji4AEVFrLV++HDY2NtiwYYO4rH///uL/C4KANWvWYNGiRZg0aRIAYOPGjbCyskJqaipmzJgBpVKJpKQkbN68GV5eXgCAlJQU2NjYYM+ePfDx8cGZM2eQmZmJ3NxcuLm5AQASExPh7u6Os2fPwt7eHllZWTh9+jQK/197dx8XVZn3D/wz8TA8BBMPwUihUsuNElqEhgNt2g2CLei6bsu26KyUoS0mkrCmed9F3RvkA+omZUomJiJta5TpSqApxSpKBK2oS9tmiQViOQ6oBIjX7w9/nPUwgKDDgYHP+/U6r5dzne+cc10znMvzvc65ztTUwMvLCwCQkZGBuLg4vPzyy3B2dlboUyEiIiJLwStfRGQxdu7ciXHjxuE3v/kNPDw8EBgYiKysLGn9yZMnUVdXh4iICKlMrVZj4sSJOHjwIACgvLwcra2tshgvLy8EBARIMYcOHYJGo5ESLwCYMGECNBqNLCYgIEBKvAAgMjISzc3Nstsgr9Xc3IyGhgbZQkREREMHky8ishhff/011q9fD19fX3z00Ud46qmnkJiYiLfffhsAUFdXBwDw9PSUvc/T01NaV1dXB1tbW7i4uHQb4+HhYbJ/Dw8PWUzH/bi4uMDW1laK6Sg9PV2aQ6bRaODt7d3bj4CIiIgsGJMvIrIYV65cwf3334+0tDQEBgZi3rx5iI+Px/r162VxKpVK9loIYVLWUceYzuJvJOZaS5cuhdFolJaamppu60RERESDC5MvIrIYw4YNg7+/v6xs9OjROHXqFABAq9UCgMmVp/r6eukqlVarRUtLCwwGQ7cxZ86cMdn/2bNnZTEd92MwGNDa2mpyRaydWq2Gs7OzbCEiIqKhg8kXEVmM0NBQVFdXy8q+/PJLjBgxAgDg4+MDrVaLoqIiaX1LSwuKi4sREhICAAgKCoKNjY0spra2FlVVVVKMTqeD0WjEkSNHpJjDhw/DaDTKYqqqqlBbWyvFFBYWQq1WIygoyMwtJyIiosGATzskIovxzDPPICQkBGlpaYiJicGRI0ewceNGbNy4EcDV2wCTkpKQlpYGX19f+Pr6Ii0tDQ4ODoiNjQUAaDQazJkzB8nJyXBzc4OrqytSUlIwZswY6emHo0ePxpQpUxAfH48NGzYAAObOnYvo6Gj4+fkBACIiIuDv7w+9Xo+VK1fi3LlzSElJQXx8PK9oERERUaeYfBGRxRg/fjzy8/OxdOlSvPTSS/Dx8cHatWsxc+ZMKWbx4sVoampCQkICDAYDgoODUVhYCCcnJylmzZo1sLa2RkxMDJqamhAWFobs7GxYWVlJMdu2bUNiYqL0VMRp06YhMzNTWm9lZYXdu3cjISEBoaGhsLe3R2xsLFatWqXAJ0FERESWSCWEEP1diYGsoaEBGo0GRqPxuqPZI5fs7tE2v3klyhxVI7JovTm2Bitz9y/sW4iuYv/S88+A5y5EvXOz/QvnfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAvok+fruu+8wa9YsuLm5wcHBAffddx/Ky8ul9UIIpKamwsvLC/b29pg0aRKOHTsm20ZzczMWLFgAd3d3ODo6Ytq0aTh9+rQsxmAwQK/XQ6PRQKPRQK/X4/z587KYU6dOYerUqXB0dIS7uzsSExPR0tLSF80mIiIiIiLqktmTL4PBgNDQUNjY2GDPnj04fvw4MjIycNttt0kxK1aswOrVq5GZmYmysjJotVpMnjwZjY2NUkxSUhLy8/ORl5eHkpISXLhwAdHR0Whra5NiYmNjUVlZiYKCAhQUFKCyshJ6vV5a39bWhqioKFy8eBElJSXIy8vDjh07kJycbO5mExERERERdcvsv/O1fPlyeHt7Y/PmzVLZyJEjpX8LIbB27VosW7YMM2bMAABs2bIFnp6eyM3Nxbx582A0GrFp0yZs3bpV+tHTnJwceHt7Y+/evYiMjMSJEydQUFCA0tJSBAcHAwCysrKg0+lQXV0NPz8/FBYW4vjx46ipqYGXlxcAICMjA3FxcXj55ZeH7ONniYiIiIhIeWa/8rVz506MGzcOv/nNb+Dh4YHAwEBkZWVJ60+ePIm6ujrph0sBQK1WY+LEiTh48CAAoLy8HK2trbIYLy8vBAQESDGHDh2CRqOREi8AmDBhAjQajSwmICBASrwAIDIyEs3NzbLbIImIiIiIiPqa2ZOvr7/+GuvXr4evry8++ugjPPXUU0hMTMTbb78NAKirqwMAeHp6yt7n6ekpraurq4OtrS1cXFy6jfHw8DDZv4eHhyym435cXFxga2srxXTU3NyMhoYG2UJERESDH+esE1FfM3vydeXKFdx///1IS0tDYGAg5s2bh/j4eKxfv14Wp1KpZK+FECZlHXWM6Sz+RmKulZ6eLnWGGo0G3t7e3daJiIiILB/nrBOREsyefA0bNgz+/v6ystGjR+PUqVMAAK1WCwAmV57q6+ulq1RarRYtLS0wGAzdxpw5c8Zk/2fPnpXFdNyPwWBAa2uryRWxdkuXLoXRaJSWmpqaHrWbiIiILNe1c9YfeOABjBw5EmFhYbj77rsBmM5ZDwgIwJYtW3Dp0iXk5uYCgDRnPSMjA+Hh4QgMDEROTg6OHj2KvXv3AoA0Z/3NN9+ETqeDTqdDVlYWdu3aherqagCQ5qzn5OQgMDAQ4eHhyMjIQFZWFu/IIbJwZk++QkNDpc6j3ZdffokRI0YAAHx8fKDValFUVCStb2lpQXFxMUJCQgAAQUFBsLGxkcXU1taiqqpKitHpdDAajThy5IgUc/jwYRiNRllMVVUVamtrpZjCwkKo1WoEBQV1Wn+1Wg1nZ2fZQkRERIObpc9Z57QJIstg9uTrmWeeQWlpKdLS0vDVV18hNzcXGzduxPz58wFcvQ0wKSkJaWlpyM/PR1VVFeLi4uDg4IDY2FgAgEajwZw5c5CcnIx9+/ahoqICs2bNwpgxY6SnH44ePRpTpkxBfHw8SktLUVpaivj4eERHR8PPzw8AEBERAX9/f+j1elRUVGDfvn1ISUlBfHw8kyoiIiKSWPqcdU6bILIMZn/U/Pjx45Gfn4+lS5fipZdego+PD9auXYuZM2dKMYsXL0ZTUxMSEhJgMBgQHByMwsJCODk5STFr1qyBtbU1YmJi0NTUhLCwMGRnZ8PKykqK2bZtGxITE6URpmnTpiEzM1Nab2Vlhd27dyMhIQGhoaGwt7dHbGwsVq1aZe5mExERkQW7cuUKxo0bh7S0NABAYGAgjh07hvXr1+P3v/+9FDdQ56wvXboUixYtkl43NDQwASMagMyefAFAdHQ0oqOju1yvUqmQmpqK1NTULmPs7Oywbt06rFu3rssYV1dX5OTkdFuX4cOHY9euXdetMxEREQ1dXc1Z37FjBwD5nPVhw4ZJMV3NWb/26ld9fb00JaKnc9YPHz4sW3+9OetqtRpqtbpXbSYi5Zn9tkMiIiIiS2Ppc9aJyDL0yZUvIiIiIkvyzDPPICQkBGlpaYiJicGRI0ewceNGbNy4EYB8zrqvry98fX2RlpbW5Zx1Nzc3uLq6IiUlpcs56xs2bAAAzJ07t8s56ytXrsS5c+c4Z51okGDyRUREREMe56wTkRJUQgjR35UYyBoaGqDRaGA0Gq872jRyye4ebfObV6LMUTUii9abY2uwMnf/wr6F6Cr2Lz3/DHjuQtQ7N9u/cM4XERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcRWaT09HSoVCokJSVJZUIIpKamwsvLC/b29pg0aRKOHTsme19zczMWLFgAd3d3ODo6Ytq0aTh9+rQsxmAwQK/XQ6PRQKPRQK/X4/z587KYU6dOYerUqXB0dIS7uzsSExPR0tLSV80lIiKiQYDJFxFZnLKyMmzcuBFjx46Vla9YsQKrV69GZmYmysrKoNVqMXnyZDQ2NkoxSUlJyM/PR15eHkpKSnDhwgVER0ejra1NiomNjUVlZSUKCgpQUFCAyspK6PV6aX1bWxuioqJw8eJFlJSUIC8vDzt27EBycnLfN56IiIgsFpMvIrIoFy5cwMyZM5GVlQUXFxepXAiBtWvXYtmyZZgxYwYCAgKwZcsWXLp0Cbm5uQAAo9GITZs2ISMjA+Hh4QgMDEROTg6OHj2KvXv3AgBOnDiBgoICvPnmm9DpdNDpdMjKysKuXbtQXV0NACgsLMTx48eRk5ODwMBAhIeHIyMjA1lZWWhoaFD+QyEiIiKLwOSLiCzK/PnzERUVhfDwcFn5yZMnUVdXh4iICKlMrVZj4sSJOHjwIACgvLwcra2tshgvLy8EBARIMYcOHYJGo0FwcLAUM2HCBGg0GllMQEAAvLy8pJjIyEg0NzejvLy8y7o3NzejoaFBthAREdHQYd3fFSAi6qm8vDx8/vnnKCsrM1lXV1cHAPD09JSVe3p64ttvv5VibG1tZVfM2mPa319XVwcPDw+T7Xt4eMhiOu7HxcUFtra2Ukxn0tPT8eKLL16vmURERDRI8coXEVmEmpoaLFy4EDk5ObCzs+syTqVSyV4LIUzKOuoY01n8jcR0tHTpUhiNRmmpqanptl5EREQ0uDD5IiKLUF5ejvr6egQFBcHa2hrW1tYoLi7Gq6++Cmtra+lKVMcrT/X19dI6rVaLlpYWGAyGbmPOnDljsv+zZ8/KYjrux2AwoLW11eSK2LXUajWcnZ1lCxEREQ0dTL6IyCKEhYXh6NGjqKyslJZx48Zh5syZqKysxF133QWtVouioiLpPS0tLSguLkZISAgAICgoCDY2NrKY2tpaVFVVSTE6nQ5GoxFHjhyRYg4fPgyj0SiLqaqqQm1trRRTWFgItVqNoKCgPv0ciIiIyHJxzhcRWQQnJycEBATIyhwdHeHm5iaVJyUlIS0tDb6+vvD19UVaWhocHBwQGxsLANBoNJgzZw6Sk5Ph5uYGV1dXpKSkYMyYMdIDPEaPHo0pU6YgPj4eGzZsAADMnTsX0dHR8PPzAwBERETA398fer0eK1euxLlz55CSkoL4+HhezSIiIqIuMfkiokFj8eLFaGpqQkJCAgwGA4KDg1FYWAgnJycpZs2aNbC2tkZMTAyampoQFhaG7OxsWFlZSTHbtm1DYmKi9FTEadOmITMzU1pvZWWF3bt3IyEhAaGhobC3t0dsbCxWrVqlXGOJiIjI4vT5bYfp6elQqVRISkqSyoQQSE1NhZeXF+zt7TFp0iQcO3ZM9r7m5mYsWLAA7u7ucHR0xLRp03D69GlZjMFggF6vh0ajgUajgV6vx/nz52Uxp06dwtSpU+Ho6Ah3d3ckJiaipaWlr5pLRAo6cOAA1q5dK71WqVRITU1FbW0tfvrpJxQXF5tcLbOzs8O6devw448/4tKlS/jwww/h7e0ti3F1dUVOTo70OPicnBzcdtttspjhw4dj165duHTpEn788UesW7cOarW6r5pKREREg0CfJl9lZWXYuHEjxo4dKytfsWIFVq9ejczMTJSVlUGr1WLy5MlobGyUYpKSkpCfn4+8vDyUlJTgwoULiI6ORltbmxQTGxuLyspKFBQUoKCgAJWVldDr9dL6trY2REVF4eLFiygpKUFeXh527NiB5OTkvmw2ERERERGRiT5Lvi5cuICZM2ciKytL9ps6QgisXbsWy5Ytw4wZMxAQEIAtW7bg0qVLyM3NBQAYjUZs2rQJGRkZCA8PR2BgIHJycnD06FHs3bsXAHDixAkUFBTgzTffhE6ng06nQ1ZWFnbt2oXq6moAVyfAHz9+HDk5OQgMDER4eDgyMjKQlZXFHzclIiKiTvGuHSLqK32WfM2fPx9RUVHSJPZ2J0+eRF1dnTSXArj6+OWJEyfi4MGDAK4+Urq1tVUW4+XlhYCAACnm0KFD0Gg0CA4OlmImTJgAjUYjiwkICICXl5cUExkZiebmZpSXl3da7+bmZulWo/aFiIiIhgbetUNEfalPkq+8vDx8/vnnSE9PN1nX/ts4HX8Lx9PTU1pXV1cHW1tb2RWzzmI8PDxMtu/h4SGL6bgfFxcX2NramvxGT7v09HRpNEqj0ZjMBSEiIqLBiXftEFFfM3vyVVNTg4ULFyInJwd2dnZdxqlUKtlrIYRJWUcdYzqLv5GYay1duhRGo1Faampquq0TERERDQ6WetcOEVkOsz9qvry8HPX19bIfGm1ra8Mnn3yCzMxMaWSnrq4Ow4YNk2Lq6+ulq1RarRYtLS0wGAyykaf6+nrpR061Wi3OnDljsv+zZ8/KtnP48GHZeoPBgNbWVpMrYu3UajWfWEZERDTEtN+1U1ZWZrKuu7t2vv32Wymmv+7aAa5Om2hubpZe8yoZ0cBk9itfYWFhOHr0KCorK6Vl3LhxmDlzJiorK3HXXXdBq9WiqKhIek9LSwuKi4ulxCooKAg2NjaymNraWlRVVUkxOp0ORqMRR44ckWIOHz4Mo9Eoi6mqqkJtba0UU1hYCLVaLUsOiYiIaOiy9Lt2AE6bILIUZr/y5eTkZPK7Oo6OjnBzc5PKk5KSkJaWBl9fX/j6+iItLQ0ODg6IjY0FAGg0GsyZMwfJyclwc3ODq6srUlJSMGbMGOlWgNGjR2PKlCmIj4/Hhg0bAABz585FdHQ0/Pz8AAARERHw9/eHXq/HypUrce7cOaSkpCA+Ph7Ozs7mbjoRERFZIEu/awe4Om1i0aJF0uuGhgYmYEQDUJ//yHJnFi9ejKSkJCQkJGDcuHH47rvvUFhYCCcnJylmzZo1mD59OmJiYhAaGgoHBwd8+OGHsLKykmK2bduGMWPGICIiAhERERg7diy2bt0qrbeyssLu3bthZ2eH0NBQxMTEYPr06Vi1apWi7SUiIqKBazDctaNWq+Hs7CxbiGjgMfuVr84cOHBA9lqlUiE1NRWpqaldvsfOzg7r1q3DunXruoxxdXVFTk5Ot/sePnw4du3a1ZvqEhER0RDCu3aISCmKJF9ERERElmzx4sVoampCQkICDAYDgoODO71rx9raGjExMWhqakJYWBiys7NN7tpJTEyUnoo4bdo0ZGZmSuvb79pJSEhAaGgo7O3tERsby7t2iAYJJl9EREREHfCuHSLqC/0y54uIiIiIiGioYfJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAJl9EREREREQKYPJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAJl9EREREREQKYPJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAJl9EREREREQKYPJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVEFiM9PR3jx4+Hk5MTPDw8MH36dFRXV8tihBBITU2Fl5cX7O3tMWnSJBw7dkwW09zcjAULFsDd3R2Ojo6YNm0aTp8+LYsxGAzQ6/XQaDTQaDTQ6/U4f/68LObUqVOYOnUqHB0d4e7ujsTERLS0tPRJ24mGgpFLdl93ISKyZEy+iMhiFBcXY/78+SgtLUVRUREuX76MiIgIXLx4UYpZsWIFVq9ejczMTJSVlUGr1WLy5MlobGyUYpKSkpCfn4+8vDyUlJTgwoULiI6ORltbmxQTGxuLyspKFBQUoKCgAJWVldDr9dL6trY2REVF4eLFiygpKUFeXh527NiB5ORkZT4MIiIisjjW/V0BIqKeKigokL3evHkzPDw8UF5ejoceeghCCKxduxbLli3DjBkzAABbtmyBp6cncnNzMW/ePBiNRmzatAlbt25FeHg4ACAnJwfe3t7Yu3cvIiMjceLECRQUFKC0tBTBwcEAgKysLOh0OlRXV8PPzw+FhYU4fvw4ampq4OXlBQDIyMhAXFwcXn75ZTg7Oyv4yRAREZEl4JUvIrJYRqMRAODq6goAOHnyJOrq6hARESHFqNVqTJw4EQcPHgQAlJeXo7W1VRbj5eWFgIAAKebQoUPQaDRS4gUAEyZMgEajkcUEBARIiRcAREZGorm5GeXl5Z3Wt7m5GQ0NDbKFiIiIhg4mX0RkkYQQWLRoER588EEEBAQAAOrq6gAAnp6eslhPT09pXV1dHWxtbeHi4tJtjIeHh8k+PTw8ZDEd9+Pi4gJbW1sppqP09HRpDplGo4G3t3dvm01EREQWzOzJFyfEE5ESnn76afzjH//A9u3bTdapVCrZayGESVlHHWM6i7+RmGstXboURqNRWmpqarqtExEREQ0uZk++OCGeiPraggULsHPnTuzfvx933nmnVK7VagHA5MpTfX29dJVKq9WipaUFBoOh25gzZ86Y7Pfs2bOymI77MRgMaG1tNbki1k6tVsPZ2Vm2ENHAwMFjIlKC2ZOvgoICxMXF4Z577sG9996LzZs349SpU9IciI4T4gMCArBlyxZcunQJubm5ACBNiM/IyEB4eDgCAwORk5ODo0ePYu/evQAgTYh/8803odPpoNPpkJWVhV27dkmdZfuE+JycHAQGBiI8PBwZGRnIysriXAsiCySEwNNPP4333nsPH3/8MXx8fGTrfXx8oNVqUVRUJJW1tLSguLgYISEhAICgoCDY2NjIYmpra1FVVSXF6HQ6GI1GHDlyRIo5fPgwjEajLKaqqgq1tbVSTGFhIdRqNYKCgszfeCLqUxw8JiIl9PnTDns7IX7evHnXnRAfGRl53Qnxfn5+150Q//DDD5vUt7m5Gc3NzdJrJmlEA8f8+fORm5uLDz74AE5OTtKVJ41GA3t7e6hUKiQlJSEtLQ2+vr7w9fVFWloaHBwcEBsbK8XOmTMHycnJcHNzg6urK1JSUjBmzBjp6YejR4/GlClTEB8fjw0bNgAA5s6di+joaPj5+QEAIiIi4O/vD71ej5UrV+LcuXNISUlBfHw8r2gRWSA+TZWIlNCnD9zghHgiMqf169fDaDRi0qRJGDZsmLS88847UszixYuRlJSEhIQEjBs3Dt999x0KCwvh5OQkxaxZswbTp09HTEwMQkND4eDggA8//BBWVlZSzLZt2zBmzBhEREQgIiICY8eOxdatW6X1VlZW2L17N+zs7BAaGoqYmBhMnz4dq1atUubDIKI+ZWlPUyUiy9CnV77aJ8SXlJSYrBvIE+IXLVokvW5oaGACRjRACCGuG6NSqZCamorU1NQuY+zs7LBu3TqsW7euyxhXV1fk5OR0u6/hw4dj165d160TEVmW3g4ef/vtt1JMfw0e884dIsvQZ1e+OCGeiIiILJElPk2Vd+4QWQazJ1+cEE9ERESWylIHj/lTFkSWwezJ1/z585GTk4Pc3FxpQnxdXR2ampoAQDYhPj8/H1VVVYiLi+tyQvy+fftQUVGBWbNmdTkhvrS0FKWlpYiPj+9yQnxFRQX27dvHCfFERERkwtIHj3nnDpFlMPucr/Xr1wMAJk2aJCvfvHkz4uLiAFydEN/U1ISEhAQYDAYEBwd3OiHe2toaMTExaGpqQlhYGLKzs00mxCcmJkoTW6dNm4bMzExpffuE+ISEBISGhsLe3h6xsbGcEE9EREQyfJoqESnB7MkXJ8QTERGRpeHgMREpoc9/54uIiIhooOPgMREpoU9/54uIiIiIiIiuYvJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAJl9EREREREQKYPJFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERAqw7u8KEJFlGblk93VjvnklSoGaEBEREVkWXvkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAn3ZIRERERESDVk+e1Awo87RmXvkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlLAkEi+Xn/9dfj4+MDOzg5BQUH49NNP+7tKRDRIsH8hor7C/oVo8Bn0ydc777yDpKQkLFu2DBUVFfj5z3+ORx55BKdOnervqhGRhWP/QkR9hf0L0eA06JOv1atXY86cOXjyyScxevRorF27Ft7e3li/fn1/V42ILBz7FyLqK+xfiAYn6/6uQF9qaWlBeXk5lixZIiuPiIjAwYMHO31Pc3MzmpubpddGoxEA0NDQcN39XWm+1KN69WRbRANVT/7Oe/I33h4jhLjpOvWHgdi/sG8hS8f+5Sol+xeeu9BQYM6/85vtXwZ18vXDDz+gra0Nnp6esnJPT0/U1dV1+p709HS8+OKLJuXe3t5mq5dmrdk2RTQg9eZvvLGxERqNps/q0lcGYv/CvoWGAvYv7F+I+ooS/cugTr7aqVQq2WshhElZu6VLl2LRokXS6ytXruDcuXNwc3Pr8j3A1SzY29sbNTU1cHZ2Nk/FFcY2DAyW3oae1l8IgcbGRnh5eSlYO/Nj/9Izlt4GS68/MLTawP6lZ/3LUPqbGMjYhoFBqf5lUCdf7u7usLKyMhklqq+vNxlNaqdWq6FWq2Vlt912W4/36ezsbLF/dO3YhoHB0tvQk/pb4oh0O/YvN8bS22Dp9QeGThvYv/S8fxkqfxMDHdswMPR1/zKoH7hha2uLoKAgFBUVycqLiooQEhLST7UiosGA/QsR9RX2L0SD16C+8gUAixYtgl6vx7hx46DT6bBx40acOnUKTz31VH9XjYgsHPsXIuor7F+IBqdBn3z99re/xY8//oiXXnoJtbW1CAgIwN/+9jeMGDHCrPtRq9V44YUXTC75WxK2YWCw9DZYev17g/1Lz1l6Gyy9/gDbYGmU6F8Gw+fJNgwMbEPPqYSlPoeViIiIiIjIggzqOV9EREREREQDBZMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+erC66+/Dh8fH9jZ2SEoKAiffvppt/HFxcUICgqCnZ0d7rrrLrzxxhsmMTt27IC/vz/UajX8/f2Rn5/fV9UH0Ls2vPfee5g8eTJuv/12ODs7Q6fT4aOPPpLFZGdnQ6VSmSw//fTTgGjDgQMHOq3fP//5T1ncQP4e4uLiOm3DPffcI8Uo/T188sknmDp1Kry8vKBSqfD+++9f9z0D8XgYSNi/sH/pjzYMtP6FfUvfYP/C/qU/2sD+pRcEmcjLyxM2NjYiKytLHD9+XCxcuFA4OjqKb7/9ttP4r7/+Wjg4OIiFCxeK48ePi6ysLGFjYyP++te/SjEHDx4UVlZWIi0tTZw4cUKkpaUJa2trUVpaOiDasHDhQrF8+XJx5MgR8eWXX4qlS5cKGxsb8fnnn0sxmzdvFs7OzqK2tla29JXetmH//v0CgKiurpbV7/Lly1LMQP8ezp8/L6t7TU2NcHV1FS+88IIUo/T38Le//U0sW7ZM7NixQwAQ+fn53cYPxONhIGH/wv6lv9ow0PoX9i3mx/6F/Ut/tYH9S88x+erEAw88IJ566ilZ2ahRo8SSJUs6jV+8eLEYNWqUrGzevHliwoQJ0uuYmBgxZcoUWUxkZKR47LHHzFRrud62oTP+/v7ixRdflF5v3rxZaDQac1XxunrbhvbOy2AwdLlNS/se8vPzhUqlEt98841UpvT3cK2edGAD8XgYSNi/XMX+5eYNpv6FfYt5sH+5iv3LzWP/0nfHAm877KClpQXl5eWIiIiQlUdERODgwYOdvufQoUMm8ZGRkfjss8/Q2trabUxX27wZN9KGjq5cuYLGxka4urrKyi9cuIARI0bgzjvvRHR0NCoqKsxW72vdTBsCAwMxbNgwhIWFYf/+/bJ1lvY9bNq0CeHh4SY/qqnU93AjBtrxMJCwf7mK/cvNG4r9y0A7FgYa9i9XsX+5eexfruqrY4HJVwc//PAD2tra4OnpKSv39PREXV1dp++pq6vrNP7y5cv44Ycfuo3paps340ba0FFGRgYuXryImJgYqWzUqFHIzs7Gzp07sX37dtjZ2SE0NBT/+te/zFp/4MbaMGzYMGzcuBE7duzAe++9Bz8/P4SFheGTTz6RYizpe6itrcWePXvw5JNPysqV/B5uxEA7HgYS9i9XsX/pnzZcyxL7l4F2LAw07F+uYv/SP224FvuX7lnfXFUHL5VKJXsthDApu158x/LebvNm3ej+tm/fjtTUVHzwwQfw8PCQyidMmIAJEyZIr0NDQ3H//fdj3bp1ePXVV81X8Wv0pg1+fn7w8/OTXut0OtTU1GDVqlV46KGHbmib5nCj+8vOzsZtt92G6dOny8r743vorYF4PAwk7F/Yv5jLUOtfBuKxMNCwf2H/Yi7sX/rmWOCVrw7c3d1hZWVlksXW19ebZLvttFptp/HW1tZwc3PrNqarbd6MG2lDu3feeQdz5szBX/7yF4SHh3cbe8stt2D8+PF9MmJxM2241oQJE2T1s5TvQQiBt956C3q9Hra2tt3G9uX3cCMG2vEwkLB/Yf9iLkOxfxlox8JAw/6F/Yu5sH+5qq+OBSZfHdja2iIoKAhFRUWy8qKiIoSEhHT6Hp1OZxJfWFiIcePGwcbGptuYrrZ5M26kDcDVEaO4uDjk5uYiKirquvsRQqCyshLDhg276Tp3dKNt6KiiokJWP0v4HoCrjzv96quvMGfOnOvupy+/hxsx0I6HgYT9C/sXcxmK/ctAOxYGGvYv7F/Mhf3LVX12LPTq8RxDRPvjNTdt2iSOHz8ukpKShKOjo/TEliVLlgi9Xi/Ftz+e8plnnhHHjx8XmzZtMnk85d///ndhZWUlXnnlFXHixAnxyiuvKPKI0J62ITc3V1hbW4vXXntN9vjP8+fPSzGpqamioKBA/Pvf/xYVFRXi8ccfF9bW1uLw4cMDog1r1qwR+fn54ssvvxRVVVViyZIlAoDYsWOHFDPQv4d2s2bNEsHBwZ1uU+nvobGxUVRUVIiKigoBQKxevVpUVFRIj5u1hONhIGH/wv6lv9rQbqD0L+xbzI/9C/uX/mpDO/Yv18fkqwuvvfaaGDFihLC1tRX333+/KC4ultbNnj1bTJw4URZ/4MABERgYKGxtbcXIkSPF+vXrTbb57rvvCj8/P2FjYyNGjRolO6j6uw0TJ04UAEyW2bNnSzFJSUli+PDhwtbWVtx+++0iIiJCHDx4cMC0Yfny5eLuu+8WdnZ2wsXFRTz44INi9+7dJtscyN+DEFd/K8Pe3l5s3Lix0+0p/T20PwK3q78NSzkeBhL2L+xf+qMNQgys/oV9S99g/8L+pT/aIAT7l55SCfH/Z5MRERERERFRn+GcLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMv6rFXX30VKpUKAQEBna5XqVRITU3tk31/8803UKlUWLVqldm2mZ2dDZVKhW+++UYqi4uLw8iRI822DyK6Oe3H6Weffdbp+ujo6H45Zg8cOACVSoUDBw4ovm+ioerw4cP41a9+heHDh0OtVsPT0xM6nQ7JyclSzKRJk6BSqXDXXXdBCGGyjU8++QQqlQoqlQrZ2dm93r4l6s/+KjU1FSqVSvH9DmRMvqjH3nrrLQDAsWPHcPjw4X6uTd/43//9X+Tn5/d3NYiIiOgau3fvRkhICBoaGrBixQoUFhbiz3/+M0JDQ/HOO+/IYp2cnHDy5El8/PHHJtt566234OzsfFPbtzT3338/Dh06hPvvv7+/q0IArPu7AmQZPvvsM3zxxReIiorC7t27sWnTJgQHB/d3tczu7rvv7u8qEBERUQcrVqyAj48PPvroI1hb/+f09bHHHsOKFStkscOHD4eTkxPeeusthIWFSeWNjY149913MXPmTGRlZd3w9m/EpUuX4ODgcNPbuRHOzs6YMGFCv+ybTPHKF/XIpk2bAACvvPIKQkJCkJeXh0uXLl33fd999x3mzp0Lb29v2NrawsvLC48++ijOnDkjxZw6dQqzZs2Ch4cH1Go1Ro8ejYyMDFy5cqXTba5evRo+Pj649dZbodPpUFpaahKzc+dO6HQ6ODg4wMnJCZMnT8ahQ4euW9/Objt89913ERwcDI1GAwcHB9x111144oknpPXtl/Nzc3Px7LPPYtiwYbj11lsxdepUnDlzBo2NjZg7dy7c3d3h7u6Oxx9/HBcuXLhuXYio91577TU89NBD8PDwgKOjI8aMGYMVK1agtbVVFjdp0iQEBASgrKwMP//5z6Vj+5VXXjHpe/75z39iypQpcHBwgLu7O5566ik0NjZ2uv+CggKEhYVJ/cXo0aORnp4urf/ss8/w2GOPYeTIkbC3t8fIkSPxu9/9Dt9++61sO+23WxYVFeHxxx+Hq6srHB0dMXXqVHz99ddm+rSILMePP/4Id3d3WWLU7pZbTE9nn3jiCbz33ns4f/68VJaXlwfgakJ1s9vvTvutdp9//jkeffRRuLi4SIO7Qgi8/vrruO+++2Bvbw8XFxc8+uijnR7X1+tPgKt9yrRp0+Dq6go7OzsEBgbiL3/5iyym422Ha9euhUqlwldffWWyz2effRa2trb44YcfpLK9e/ciLCwMzs7OcHBwQGhoKPbt22fy3t27d+O+++6DWq2Gj4+PWaeKDCZMvui6mpqasH37dowfPx4BAQF44oknpNGj7nz33XcYP3488vPzsWjRIuzZswdr166FRqOBwWAAAJw9exYhISEoLCzE//3f/2Hnzp0IDw9HSkoKnn76aZNtvvbaaygqKsLatWuxbds2XLx4Eb/4xS9gNBqlmNzcXPzyl7+Es7Mztm/fjk2bNsFgMGDSpEkoKSnpVdsPHTqE3/72t7jrrruQl5eH3bt34/nnn8fly5dNYp977jnU19cjOzsbGRkZOHDgAH73u9/h17/+NTQaDbZv347Fixdj69ateO6553pVD6Khrq2tDZcvXzZZOs7p+Pe//43Y2Fhs3boVu3btwpw5c7By5UrMmzfPZJt1dXWYOXMmZs2ahZ07d+KRRx7B0qVLkZOTI8WcOXMGEydORFVVFV5//XVs3boVFy5c6LR/2rRpE37xi1/gypUreOONN/Dhhx8iMTERp0+flmK++eYb+Pn5Ye3atfjoo4+wfPly1NbWYvz48bKTnXZz5szBLbfcgtzcXKxduxZHjhzBpEmTZCeUREOBTqfD4cOHkZiYiMOHD5sMqHT02GOPwcrKCtu3b5fKNm3ahEcffbTT2w57u/2emDFjBn72s5/h3XffxRtvvAEAmDdvHpKSkhAeHo73338fr7/+Oo4dO4aQkBDZwHRP+pP9+/cjNDQU58+fxxtvvIEPPvgA9913H37729+azGe71qxZs2Bra2sS09bWhpycHEydOhXu7u4AgJycHERERMDZ2RlbtmzBX/7yF7i6uiIyMlKWgO3btw+//OUv4eTkhLy8PKxcuRJ/+ctfsHnz5pv+HAcdQXQdb7/9tgAg3njjDSGEEI2NjeLWW28VP//5z2VxAMQLL7wgvX7iiSeEjY2NOH78eJfbXrJkiQAgDh8+LCv/wx/+IFQqlaiurhZCCHHy5EkBQIwZM0ZcvnxZijty5IgAILZv3y6EEKKtrU14eXmJMWPGiLa2NimusbFReHh4iJCQEKls8+bNAoA4efKkVDZ79mwxYsQI6fWqVasEAHH+/Pku27B//34BQEydOlVWnpSUJACIxMREWfn06dOFq6trl9sjov9oP067W649Zq/V1tYmWltbxdtvvy2srKzEuXPnpHUTJ07stO/x9/cXkZGR0utnn31WqFQqUVlZKYubPHmyACD2798vhLjaxzg7O4sHH3xQXLlypcftu3z5srhw4YJwdHQUf/7zn03a/atf/UoW//e//10AEH/60596vA+iweCHH34QDz74oHTc29jYiJCQEJGeni4aGxuluIkTJ4p77rlHCHH1//Rx48YJIYQ4duyYACAOHDggysrKBACxefPmXm+/J1544QUBQDz//POy8kOHDgkAIiMjQ1ZeU1Mj7O3txeLFi4UQPe9PRo0aJQIDA0Vra6usPDo6WgwbNkw6D2o/T2nvr4QQYsaMGeLOO++UnSv97W9/EwDEhx9+KIQQ4uLFi8LV1dXk/KatrU3ce++94oEHHpDKgoODhZeXl2hqapLKGhoahKurq2C6IccrX3RdmzZtgr29vXSZ/tZbb8VvfvMbfPrpp/jXv/7V5fv27NmDhx9+GKNHj+4y5uOPP4a/vz8eeOABWXlcXByEECaTZaOiomBlZSW9Hjt2LABIt+xUV1fj+++/h16vl90mcOutt+LXv/41SktLe3S7ZLvx48cDAGJiYvCXv/wF3333XZex0dHRstft7Y6KijIpP3fuHG89JOqFt99+G2VlZSbLgw8+KIurqKjAtGnT4ObmBisrK9jY2OD3v/892tra8OWXX8pitVqtSd8zduxY2S2A+/fvxz333IN7771XFhcbGyt7ffDgQTQ0NCAhIaHbJ3tduHABzz77LH72s5/B2toa1tbWuPXWW3Hx4kWcOHHCJH7mzJmy1yEhIRgxYgT279/f5T6IBiM3Nzd8+umnKCsrwyuvvIJf/vKX+PLLL7F06VKMGTOm0yvHTzzxBD777DMcPXoUmzZtwt13342HHnrIbNu/nl//+tey17t27YJKpcKsWbNkV/C1Wi3uvfde6bbAnvQnX331Ff75z39KfcS12/vFL36B2tpaVFdXd1m3xx9/HKdPn8bevXulss2bN0Or1eKRRx6R6nHu3DnMnj1btv0rV65gypQpKCsrw8WLF3Hx4kWUlZVhxowZsLOzk7bn5OSEqVOn9vpzG+yYfFG3vvrqK3zyySeIioqCEALnz5/H+fPn8eijjwL4zxMQO3P27Fnceeed3W7/xx9/xLBhw0zKvby8pPXXcnNzk71Wq9UArt4aeW18V9u8cuWKdMtjTzz00EN4//33cfnyZfz+97/HnXfeiYCAANltDO1cXV1lr21tbbst/+mnn3pcD6KhbvTo0Rg3bpzJotFopJhTp07h5z//Ob777jv8+c9/lk6kXnvtNQD/6SfadexPgKt9yrVxP/74I7RarUlcx7KzZ88CwHX7vNjYWGRmZuLJJ5/ERx99hCNHjqCsrAy33367Sf062097Wce+kWioGDduHJ599lm8++67+P777/HMM8/gm2++6fShGA899BB8fX2xYcMGbN26FU888cR1H3vem+1fT8dzkTNnzkAIAU9PT9jY2MiW0tJSKcHrSX/SfotiSkqKybYSEhIAoNuE8ZFHHsGwYcOk2wINBgN27tyJ3//+99Igd/s+Hn30UZN9LF++HEIInDt3DgaDAVeuXOlRX0l82iFdx1tvvQUhBP7617/ir3/9q8n6LVu24E9/+pPsalS722+/XXZvcmfc3NxQW1trUv79998DgHTPcU+1n0x1tc1bbrkFLi4uvdrmL3/5S/zyl79Ec3MzSktLkZ6ejtjYWIwcORI6na5X2yKivvP+++/j4sWLeO+99zBixAipvLKy8oa36ebmhrq6OpPyjmW33347AHTb5xmNRuzatQsvvPAClixZIpU3Nzfj3Llznb6nq33/7Gc/61H9iQYzGxsbvPDCC1izZg2qqqo6jXn88cfxP//zP1CpVJg9e7bZt9+djomeu7s7VCoVPv30U2nw+FrtZT3pT9rPj5YuXYoZM2Z0GuPn59fl+62srKDX6/Hqq6/i/PnzyM3NRXNzMx5//HGTfaxbt67LpyV6enqitbUVKpWqR30l8coXdaOtrQ1btmzB3Xffjf3795ssycnJqK2txZ49ezp9/yOPPIL9+/d3e9k7LCwMx48fx+effy4rf/vtt6FSqfDwww/3qs5+fn644447kJubK5uIf/HiRezYsUN6AuKNUKvVmDhxIpYvXw7g6u1NRDRwtJ/oXHtSI4QweaR0bzz88MM4duwYvvjiC1l5bm6u7HVISAg0Gg3eeOONTn/Ytb1+QgiTk64333wTbW1tnb5n27ZtstcHDx7Et99+i0mTJvWyJUSWrbNBVQDS7brtd8x0NHv2bEydOhV//OMfcccdd5h9+70RHR0NIQS+++67Tq/kjxkzBkDP+hM/Pz/4+vriiy++6HRb48aNg5OTU7f1efzxx/HTTz9h+/btyM7Ohk6nw6hRo6T1oaGhuO2223D8+PEu92FrawtHR0c88MADeO+992R39TQ2NuLDDz+86c9tsOGVL+rSnj178P3332P58uWd/kcfEBCAzMxMbNq0yWS+EwC89NJL2LNnDx566CE899xzGDNmDM6fP4+CggIsWrQIo0aNwjPPPIO3334bUVFReOmllzBixAjs3r0br7/+Ov7whz/gv/7rv3pV51tuuQUrVqzAzJkzER0djXnz5qG5uRkrV67E+fPn8corr/Rqe88//zxOnz6NsLAw3HnnnTh//jz+/Oc/w8bGBhMnTuzVtoiob02ePBm2trb43e9+h8WLF+Onn37C+vXre3WrcUdJSUl46623EBUVhT/96U/w9PTEtm3b8M9//lMWd+uttyIjIwNPPvkkwsPDER8fD09PT3z11Vf44osvkJmZCWdnZzz00ENYuXIl3N3dMXLkSBQXF2PTpk247bbbOt3/Z599hieffBK/+c1vUFNTg2XLluGOO+6QbisiGioiIyNx5513YurUqRg1ahSuXLmCyspKZGRk4NZbb8XChQs7fZ+Xlxfef//9Ptt+b4SGhmLu3Ll4/PHH8dlnn+Ghhx6Co6MjamtrUVJSgjFjxuAPf/hDj/oTANiwYQMeeeQRREZGIi4uDnfccQfOnTuHEydO4PPPP7/uU6lHjRoFnU6H9PR01NTUYOPGjbL1t956K9atW4fZs2fj3LlzePTRR+Hh4YGzZ8/iiy++wNmzZ7F+/XoAwP/93/9hypQpmDx5MpKTk9HW1obly5fD0dGxyyv7Q1Y/PeiDLMD06dOFra2tqK+v7zLmscceE9bW1qKurs7kaYdCXH2CzxNPPCG0Wq2wsbERXl5eIiYmRpw5c0aK+fbbb0VsbKxwc3MTNjY2ws/PT6xcuVL2BJ72px2uXLnSpA6d7ff9998XwcHBws7OTjg6OoqwsDDx97//XRbTk6cd7tq1SzzyyCPijjvuELa2tsLDw0P84he/EJ9++qkU0/4UoXfffbfT7ZeVlcnK25+CdPbs2U4/UyL6j66Oo3ZRUVGyY/bDDz8U9957r7CzsxN33HGH+OMf/yj27Nlj8qSva5+Idq2OfYAQQhw/flxMnjxZ2NnZCVdXVzFnzhzxwQcfmGxTiKtPC5s4caJwdHQUDg4Owt/fXyxfvlxaf/r0afHrX/9auLi4CCcnJzFlyhRRVVUlRowYIWbPnm3S7sLCQqHX68Vtt90m7O3txS9+8Qvxr3/9q8efH9Fg8c4774jY2Fjh6+srbr31VmFjYyOGDx8u9Hq97KnKXR3b1+rsaYc93X5PXO//+bfeeksEBwcLR0dHYW9vL+6++27x+9//Xnz22WeyuOv1J0II8cUXX4iYmBjh4eEhbGxshFarFf/93/8tPaFaiM6fdthu48aNAoCwt7cXRqOx0/oWFxeLqKgo4erqKmxsbMQdd9whoqKiTM57du7cKcaOHStsbW3F8OHDxSuvvCJ9FvQfKiG6uJ5JRERE/SI7OxuPP/44ysrKMG7cuP6uDhERmQnnfBERERERESmAc76IiIiIiK7jypUruHLlSrcx1tY8tabu8bZDIiIiIqLrSE1NxYsvvthtzMmTJzFy5EhlKkQWickXEREREdF1fP/999LvkHZl7NixsLW1VahGZImYfBERERERESmAD9wgIiIiIiJSAGcFXseVK1fw/fffw8nJCSqVqr+rQzRoCCHQ2NgILy8v3HLL0BwHYv9C1DfYv7B/IeorN9u/MPm6ju+//x7e3t79XQ2iQaumpgZ33nlnf1ejX7B/Iepb7F/YvxD1lRvtX5h8XYeTkxOAqx+ws7NzP9eGaPBoaGiAt7e3dIwNRexfiPoG+xf2L0R95Wb7FyZf19F+qd7Z2ZmdF1EfGMq3w7B/Iepb7F/YvxD1lRvtX4bmjdBEREREREQKY/JFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAJl9EREREREQKYPJFRERERESkAP7IshmNXLK7R3HfvBLVxzUhosGmJ/0L+xYi6i2euxApi1e+iIiIiIiIFMDki4iIiIiISAFMvoiIiIiIiBTA5IuIiIiIiEgBTL6IiIiIiIgUwOSLiIiIiIhIAUy+iIiIiIiIFMDki4iIiIiISAG9Tr4++eQTTJ06FV5eXlCpVHj//fdl64UQSE1NhZeXF+zt7TFp0iQcO3ZMFtPc3IwFCxbA3d0djo6OmDZtGk6fPi2LMRgM0Ov10Gg00Gg00Ov1OH/+vCzm1KlTmDp1KhwdHeHu7o7ExES0tLTIYo4ePYqJEyfC3t4ed9xxB1566SUIIXrbbCIiIiIiopvS6+Tr4sWLuPfee5GZmdnp+hUrVmD16tXIzMxEWVkZtFotJk+ejMbGRikmKSkJ+fn5yMvLQ0lJCS5cuIDo6Gi0tbVJMbGxsaisrERBQQEKCgpQWVkJvV4vrW9ra0NUVBQuXryIkpIS5OXlYceOHUhOTpZiGhoaMHnyZHh5eaGsrAzr1q3DqlWrsHr16t42m4iIiIiI6Kb0Ovl65JFH8Kc//QkzZswwWSeEwNq1a7Fs2TLMmDEDAQEB2LJlCy5duoTc3FwAgNFoxKZNm5CRkYHw8HAEBgYiJycHR48exd69ewEAJ06cQEFBAd58803odDrodDpkZWVh165dqK6uBgAUFhbi+PHjyMnJQWBgIMLDw5GRkYGsrCw0NDQAALZt24affvoJ2dnZCAgIwIwZM/Dcc89h9erVvPpFNMDwqjoRERENdmad83Xy5EnU1dUhIiJCKlOr1Zg4cSIOHjwIACgvL0dra6ssxsvLCwEBAVLMoUOHoNFoEBwcLMVMmDABGo1GFhMQEAAvLy8pJjIyEs3NzSgvL5diJk6cCLVaLYv5/vvv8c0333TahubmZjQ0NMgWIup7vKpOREREg521OTdWV1cHAPD09JSVe3p64ttvv5VibG1t4eLiYhLT/v66ujp4eHiYbN/Dw0MW03E/Li4usLW1lcWMHDnSZD/t63x8fEz2kZ6ejhdffLFH7SUi83nkkUfwyCOPdLqu41V1ANiyZQs8PT2Rm5uLefPmSVfVt27divDwcABATk4OvL29sXfvXkRGRkpX1UtLS6XBnaysLOh0OlRXV8PPz0+6ql5TUyMN7mRkZCAuLg4vv/wynJ2dZVfV1Wo1AgIC8OWXX2L16tVYtGgRVCqVAp8YERERWZo+edphxxMPIcR1T0Y6xnQWb46Y9tuCuqrP0qVLYTQapaWmpqbbehNR3xsMV9UBXlknIiIa6syafGm1WgD/uQLWrr6+XrripNVq0dLSAoPB0G3MmTNnTLZ/9uxZWUzH/RgMBrS2tnYbU19fD8D06lw7tVoNZ2dn2UJE/au7q+rXXulW8qp6Z3W5tq6dSU9Pl+aaaTQaeHt7d99wIiIiGlTMmnz5+PhAq9WiqKhIKmtpaUFxcTFCQkIAAEFBQbCxsZHF1NbWoqqqSorR6XQwGo04cuSIFHP48GEYjUZZTFVVFWpra6WYwsJCqNVqBAUFSTGffPKJbKJ8YWEhvLy8TG5HJKKBz5KvqgO8sk5ERDTU9Tr5unDhAiorK1FZWQng6u1AlZWVOHXqFFQqFZKSkpCWlob8/HxUVVUhLi4ODg4OiI2NBQBoNBrMmTMHycnJ2LdvHyoqKjBr1iyMGTNGmqcxevRoTJkyBfHx8SgtLUVpaSni4+MRHR0NPz8/AEBERAT8/f2h1+tRUVGBffv2ISUlBfHx8dLVqtjYWKjVasTFxaGqqgr5+flIS0vjnAwiCzMYrqoDvLJOREQ01PU6+frss88QGBiIwMBAAMCiRYsQGBiI559/HgCwePFiJCUlISEhAePGjcN3332HwsJCODk5SdtYs2YNpk+fjpiYGISGhsLBwQEffvghrKyspJht27ZhzJgxiIiIQEREBMaOHYutW7dK662srLB7927Y2dkhNDQUMTExmD59OlatWiXFaDQaFBUV4fTp0xg3bhwSEhKwaNEiLFq0qPefFBH1G15VJyIiosFAJfjDNN1qaGiARqOB0Wi87ij1yCW7e7TNb16JMkfViCxax2PrwoUL+OqrrwAAgYGBWL16NR5++GG4urpi+PDhWL58OdLT07F582b4+voiLS0NBw4cQHV1tTS484c//AG7du1CdnY2XF1dkZKSgh9//BHl5eXS4M4jjzyC77//Hhs2bAAAzJ07FyNGjMCHH34I4Oqj5u+77z54enpi5cqVOHfuHOLi4jB9+nSsW7cOwNXfK/Tz88N///d/47nnnsO//vUvxMXF4fnnn5c9kr63n0F3etK/sG8huqo3x9Zg1dPPgOcuRL1zs/2LWR81T0R0oz777DM8/PDD0uv2K9SzZ89GdnY2Fi9ejKamJiQkJMBgMCA4OLjTq+rW1taIiYlBU1MTwsLCkJ2dbXJVPTExUXoq4rRp02S/LdZ+VT0hIQGhoaGwt7dHbGxsp1fV58+fj3HjxsHFxYVX1YmIiOi6eOXrOnjli6hvcGSaV76I+gr7F175IuorN9u/9MnvfBEREREREZEcky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiIiJSAJMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+SIiIiIiIlIAky8iIiIiIiIFMPkiIiIiAvDdd99h1qxZcHNzg4ODA+677z6Ul5dL64UQSE1NhZeXF+zt7TFp0iQcO3ZMto3m5mYsWLAA7u7ucHR0xLRp03D69GlZjMFggF6vh0ajgUajgV6vx/nz52Uxp06dwtSpU+Ho6Ah3d3ckJiaipaWlz9pORMpg8kVERERDnsFgQGhoKGxsbLBnzx4cP34cGRkZuO2226SYFStWYPXq1cjMzERZWRm0Wi0mT56MxsZGKSYpKQn5+fnIy8tDSUkJLly4gOjoaLS1tUkxsbGxqKysREFBAQoKClBZWQm9Xi+tb2trQ1RUFC5evIiSkhLk5eVhx44dSE5OVuSzIKK+Y93fFSAiIiLqb8uXL4e3tzc2b94slY0cOVL6txACa9euxbJlyzBjxgwAwJYtW+Dp6Ync3FzMmzcPRqMRmzZtwtatWxEeHg4AyMnJgbe3N/bu3YvIyEicOHECBQUFKC0tRXBwMAAgKysLOp0O1dXV8PPzQ2FhIY4fP46amhp4eXkBADIyMhAXF4eXX34Zzs7OCn0qRGRuvPJFREREQ97OnTsxbtw4/OY3v4GHhwcCAwORlZUlrT958iTq6uoQEREhlanVakycOBEHDx4EAJSXl6O1tVUW4+XlhYCAACnm0KFD0Gg0UuIFABMmTIBGo5HFBAQESIkXAERGRqK5uVl2G+S1mpub0dDQIFuIaOBh8kVERERD3tdff43169fD19cXH330EZ566ikkJibi7bffBgDU1dUBADw9PWXv8/T0lNbV1dXB1tYWLi4u3cZ4eHiY7N/Dw0MW03E/Li4usLW1lWI6Sk9Pl+aQaTQaeHt79/YjICIFMPkiIiKiIe/KlSu4//77kZaWhsDAQMybNw/x8fFYv369LE6lUsleCyFMyjrqGNNZ/I3EXGvp0qUwGo3SUlNT022diKh/MPkiIiKiIW/YsGHw9/eXlY0ePRqnTp0CAGi1WgAwufJUX18vXaXSarVoaWmBwWDoNubMmTMm+z979qwspuN+DAYDWltbTa6ItVOr1XB2dpYtRDTwMPkiIiKiIS80NBTV1dWysi+//BIjRowAAPj4+ECr1aKoqEha39LSguLiYoSEhAAAgoKCYGNjI4upra1FVVWVFKPT6WA0GnHkyBEp5vDhwzAajbKYqqoq1NbWSjGFhYVQq9UICgoyc8uJSEl82iERERENec888wxCQkKQlpaGmJgYHDlyBBs3bsTGjRsBXL0NMCkpCWlpafD19YWvry/S0tLg4OCA2NhYAIBGo8GcOXOQnJwMNzc3uLq6IiUlBWPGjJGefjh69GhMmTIF8fHx2LBhAwBg7ty5iI6Ohp+fHwAgIiIC/v7+0Ov1WLlyJc6dO4eUlBTEx8fzihaRhWPyRUREREPe+PHjkZ+fj6VLl+Kll16Cj48P1q5di5kzZ0oxixcvRlNTExISEmAwGBAcHIzCwkI4OTlJMWvWrIG1tTViYmLQ1NSEsLAwZGdnw8rKSorZtm0bEhMTpaciTps2DZmZmdJ6Kysr7N69GwkJCQgNDYW9vT1iY2OxatUqBT4JIupLKiGE6O9KDGQNDQ3QaDQwGo3XHW0auWR3j7b5zStR5qgakUXrzbE1WJm7f2HfQnQV+5eefwY8dyHqnZvtX8w+5+vy5cv4n//5H/j4+MDe3h533XUXXnrpJVy5ckWKEUIgNTUVXl5esLe3x6RJk3Ds2DHZdpqbm7FgwQK4u7vD0dER06ZNw+nTp2UxBoMBer1eeqyqXq/H+fPnZTGnTp3C1KlT4ejoCHd3dyQmJqKlpcXczSYiIiIiIuqW2ZOv5cuX44033kBmZiZOnDiBFStWYOXKlVi3bp0Us2LFCqxevRqZmZkoKyuDVqvF5MmT0djYKMUkJSUhPz8feXl5KCkpwYULFxAdHY22tjYpJjY2FpWVlSgoKEBBQQEqKyuh1+ul9W1tbYiKisLFixdRUlKCvLw87NixA8nJyeZuNhEpgIM7REREZMnMnnwdOnQIv/zlLxEVFYWRI0fi0UcfRUREBD777DMAV0+M1q5di2XLlmHGjBkICAjAli1bcOnSJeTm5gIAjEYjNm3ahIyMDISHhyMwMBA5OTk4evQo9u7dCwA4ceIECgoK8Oabb0Kn00Gn0yErKwu7du2SnlZUWFiI48ePIycnB4GBgQgPD0dGRgaysrL4y+9EFoiDO0RERGTJzJ58Pfjgg9i3bx++/PJLAMAXX3yBkpIS/OIXvwAAnDx5EnV1ddIkU+Dqb1NMnDgRBw8eBACUl5ejtbVVFuPl5YWAgAAp5tChQ9BoNAgODpZiJkyYAI1GI4sJCAiAl5eXFBMZGYnm5maUl5d3Wv/m5mY0NDTIFiIaGDi4Q0RERJbM7MnXs88+i9/97ncYNWoUbGxsEBgYiKSkJPzud78D8J8fJ+z4I4Genp7Surq6Otja2sLFxaXbGA8PD5P9e3h4yGI67sfFxQW2trYmP17YLj09XbrNSKPRwNvbu7cfARH1EUsf3CEiIqKhzeyPmn/nnXeQk5OD3Nxc3HPPPaisrERSUhK8vLwwe/ZsKU6lUsneJ4QwKeuoY0xn8TcSc62lS5di0aJF0uuGhgYmYEQDxLPPPguj0YhRo0bBysoKbW1tePnll3s0uPPtt99KMf01uNPc3Izm5mbpNa+QERERDS1mv/L1xz/+EUuWLMFjjz2GMWPGQK/X45lnnkF6ejoAQKvVAoDJyUl9fb10IqPVatHS0gKDwdBtzJkzZ0z2f/bsWVlMx/0YDAa0traanDS1U6vVcHZ2li1ENDBcO7jz+eefY8uWLVi1ahW2bNkiixuogzu8sk5ERDS0mT35unTpEm65Rb5ZKysr6WlkPj4+0Gq1KCoqkta3tLSguLgYISEhAICgoCDY2NjIYmpra1FVVSXF6HQ6GI1GHDlyRIo5fPgwjEajLKaqqgq1tbVSTGFhIdRqNYKCgszcciLqa5Y+uLN06VIYjUZpqamp6e1HQERERBbM7MnX1KlT8fLLL2P37t345ptvkJ+fj9WrV+NXv/oVgKsjxUlJSUhLS0N+fj6qqqoQFxcHBwcHxMbGAgA0Gg3mzJmD5ORk7Nu3DxUVFZg1axbGjBmD8PBwAMDo0aMxZcoUxMfHo7S0FKWlpYiPj0d0dDT8/PwAABEREfD394der0dFRQX27duHlJQUxMfH84oWkQWy9MEdXlknIiIa2sw+52vdunX43//9XyQkJKC+vh5eXl6YN28enn/+eSlm8eLFaGpqQkJCAgwGA4KDg1FYWAgnJycpZs2aNbC2tkZMTAyampoQFhaG7OxsWFlZSTHbtm1DYmKiNHF+2rRpyMzMlNZbWVlh9+7dSEhIQGhoKOzt7REbG4tVq1aZu9lEpID2wZ3hw4fjnnvuQUVFBVavXo0nnngCgHxwx9fXF76+vkhLS+tycMfNzQ2urq5ISUnpcnBnw4YNAIC5c+d2ObizcuVKnDt3joM7RERE1C2VEEL0dyUGsoaGBmg0GhiNxuueUI1csrtH2/zmlShzVI3IovXm2GrX2NiI//3f/0V+fr40uPO73/0Ozz//PGxtbQFcnXP14osvYsOGDdLgzmuvvYaAgABpOz/99BP++Mc/Ijc3Vxrcef3112VzsM6dO4fExETs3LkTwH8Gd2677TYp5tSpU0hISMDHH38sG9xRq9Vm/wx60r+wbyG66kb6l8Gmp58Bz12Ieudm+xcmX9fB5Iuob/DkiMkXUV9h/8Lki6iv3Gz/YvY5X0RERERERGSKyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZEC+iT5+u677zBr1iy4ubnBwcEB9913H8rLy6X1QgikpqbCy8sL9vb2mDRpEo4dOybbRnNzMxYsWAB3d3c4Ojpi2rRpOH36tCzGYDBAr9dDo9FAo9FAr9fj/PnzsphTp05h6tSpcHR0hLu7OxITE9HS0tIXzSYiIiIiIuqS2ZMvg8GA0NBQ2NjYYM+ePTh+/DgyMjJw2223STErVqzA6tWrkZmZibKyMmi1WkyePBmNjY1STFJSEvLz85GXl4eSkhJcuHAB0dHRaGtrk2JiY2NRWVmJgoICFBQUoLKyEnq9Xlrf1taGqKgoXLx4ESUlJcjLy8OOHTuQnJxs7mYTkUI4uENERESWytrcG1y+fDm8vb2xefNmqWzkyJHSv4UQWLt2LZYtW4YZM2YAALZs2QJPT0/k5uZi3rx5MBqN2LRpE7Zu3Yrw8HAAQE5ODry9vbF3715ERkbixIkTKCgoQGlpKYKDgwEAWVlZ0Ol0qK6uhp+fHwoLC3H8+HHU1NTAy8sLAJCRkYG4uDi8/PLLcHZ2NnfziagPtQ/uPPzww9izZw88PDzw73//u9PBnezsbPzXf/0X/vSnP2Hy5Mmorq6Gk5MTgKuDOx9++CHy8vLg5uaG5ORkREdHo7y8HFZWVgCuDu6cPn0aBQUFAIC5c+dCr9fjww8/BPCfwZ3bb78dJSUl+PHHHzF79mwIIbBu3TplPxgiIiKyCGa/8rVz506MGzcOv/nNb+Dh4YHAwEBkZWVJ60+ePIm6ujpERERIZWq1GhMnTsTBgwcBAOXl5WhtbZXFeHl5ISAgQIo5dOgQNBqNlHgBwIQJE6DRaGQxAQEBUuIFAJGRkWhubpaNlF+rubkZDQ0NsoWIBoZrB3ceeOABjBw5EmFhYbj77rsBmA7uBAQEYMuWLbh06RJyc3MBQBrcycjIQHh4OAIDA5GTk4OjR49i7969ACAN7rz55pvQ6XTQ6XTIysrCrl27UF1dDQDS4E5OTg4CAwMRHh6OjIwMZGVlsd8gIiKiTpk9+fr666+xfv16+Pr64qOPPsJTTz2FxMREvP322wCAuro6AICnp6fsfZ6entK6uro62NrawsXFpdsYDw8Pk/17eHjIYjrux8XFBba2tlJMR+np6dJtRhqNBt7e3r39CIioj3Bwh4iIiCyZ2ZOvK1eu4P7770daWhoCAwMxb948xMfHY/369bI4lUoley2EMCnrqGNMZ/E3EnOtpUuXwmg0SktNTU23dSIi5XBwh4iUkJ6eDpVKhaSkJKmM80mJyBzMnnwNGzYM/v7+srLRo0fj1KlTAACtVgsAJicn9fX10omMVqtFS0sLDAZDtzFnzpwx2f/Zs2dlMR33YzAY0NraanLS1E6tVsPZ2Vm2ENHAwMEdIuprZWVl2LhxI8aOHSsr58PCiMgczJ58hYaGSnMi2n355ZcYMWIEAMDHxwdarRZFRUXS+paWFhQXFyMkJAQAEBQUBBsbG1lMbW0tqqqqpBidTgej0YgjR45IMYcPH4bRaJTFVFVVoba2VoopLCyEWq1GUFCQmVtORH2NgztE1JcuXLiAmTNnIisrS3Z1nPNJichczJ58PfPMMygtLUVaWhq++uor5ObmYuPGjZg/fz4ASJfx09LSkJ+fj6qqKsTFxcHBwQGxsbEAAI1Ggzlz5iA5ORn79u1DRUUFZs2ahTFjxkhPPxw9ejSmTJmC+Ph4lJaWorS0FPHx8YiOjoafnx8AICIiAv7+/tDr9aioqMC+ffuQkpKC+Ph4nvQQWSAO7hBRX5o/fz6ioqKkc412A30+KRFZDrM/an78+PHIz8/H0qVL8dJLL8HHxwdr167FzJkzpZjFixejqakJCQkJMBgMCA4ORmFhofQYaABYs2YNrK2tERMTg6amJoSFhSE7O1t6DDQAbNu2DYmJiVJHN23aNGRmZkrrrayssHv3biQkJCA0NBT29vaIjY3FqlWrzN1sIlLAM888g5CQEKSlpSEmJgZHjhzBxo0bsXHjRgDywR1fX1/4+voiLS2ty8EdNzc3uLq6IiUlpcvBnQ0bNgC4+qj5rgZ3Vq5ciXPnznFwh8iC5eXl4fPPP0dZWZnJuu7mk3777bdSTH/NJwWuzjdrbm6WXvMqGdHAZPbkCwCio6MRHR3d5XqVSoXU1FSkpqZ2GWNnZ4d169Z1+3s5rq6uyMnJ6bYuw4cPx65du65bZyIa+Di4Q0R9oaamBgsXLkRhYSHs7Oy6jBuo80mBqw8JefHFF7utCxH1vz5JvoiI+goHd4jI3MrLy1FfXy+7ZbitrQ2ffPIJMjMzpdud6+rqMGzYMCmmq/mk1179qq+vl25X7ul80sOHD8vWX28+KXD1gT6LFi2SXjc0NPCJqkQDkNnnfBERERFZkrCwMBw9ehSVlZXSMm7cOMycOROVlZW46667Bvx8Uj7Qh8gy8MoXERERDWlOTk4ICAiQlTk6OsLNzU0q53xSIjIHJl9ERERE18H5pERkDky+iIiIiDo4cOCA7DXnkxKROXDOFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYCPmiciIiIiokFr5JLdPYr75pWoPq4Jr3wREREREREpgskXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKaDPk6/09HSoVCokJSVJZUIIpKamwsvLC/b29pg0aRKOHTsme19zczMWLFgAd3d3ODo6Ytq0aTh9+rQsxmAwQK/XQ6PRQKPRQK/X4/z587KYU6dOYerUqXB0dIS7uzsSExPR0tLSV80lIiIiIiLqVJ8mX2VlZdi4cSPGjh0rK1+xYgVWr16NzMxMlJWVQavVYvLkyWhsbJRikpKSkJ+fj7y8PJSUlODChQuIjo5GW1ubFBMbG4vKykoUFBSgoKAAlZWV0Ov10vq2tjZERUXh4sWLKCkpQV5eHnbs2IHk5OS+bDYRKYADO0RERGRp+iz5unDhAmbOnImsrCy4uLhI5UIIrF27FsuWLcOMGTMQEBCALVu24NKlS8jNzQUAGI1GbNq0CRkZGQgPD0dgYCBycnJw9OhR7N27FwBw4sQJFBQU4M0334ROp4NOp0NWVhZ27dqF6upqAEBhYSGOHz+OnJwcBAYGIjw8HBkZGcjKykJDQ0NfNZ2I+hgHdoiIiMgS9VnyNX/+fERFRSE8PFxWfvLkSdTV1SEiIkIqU6vVmDhxIg4ePAgAKC8vR2trqyzGy8sLAQEBUsyhQ4eg0WgQHBwsxUyYMAEajUYWExAQAC8vLykmMjISzc3NKC8v77Tezc3NaGhokC1ENHBwYIeIiIgsVZ8kX3l5efj888+Rnp5usq6urg4A4OnpKSv39PSU1tXV1cHW1lZ2YtVZjIeHh8n2PTw8ZDEd9+Pi4gJbW1sppqP09HTpViONRgNvb++eNJmIFGKpAzsAB3eIiIiGOrMnXzU1NVi4cCFycnJgZ2fXZZxKpZK9FkKYlHXUMaaz+BuJudbSpUthNBqlpaampts6EZFyLHlgB+DgDhER0VBn9uSrvLwc9fX1CAoKgrW1NaytrVFcXIxXX30V1tbW0glLxxOU+vp6aZ1Wq0VLSwsMBkO3MWfOnDHZ/9mzZ2UxHfdjMBjQ2tpqcuLUTq1Ww9nZWbYQUf+z9IEdgIM7REREQ53Zk6+wsDAcPXoUlZWV0jJu3DjMnDkTlZWVuOuuu6DValFUVCS9p6WlBcXFxQgJCQEABAUFwcbGRhZTW1uLqqoqKUan08FoNOLIkSNSzOHDh2E0GmUxVVVVqK2tlWIKCwuhVqsRFBRk7qYTUR+y9IEdgIM7REREQ53Zky8nJycEBATIFkdHR7i5uSEgIEB6NHRaWhry8/NRVVWFuLg4ODg4IDY2FgCg0WgwZ84cJCcnY9++faioqMCsWbMwZswYaZ7H6NGjMWXKFMTHx6O0tBSlpaWIj49HdHQ0/Pz8AAARERHw9/eHXq9HRUUF9u3bh5SUFMTHx/Okh8jCcGCHiIiILJ11f+x08eLFaGpqQkJCAgwGA4KDg1FYWAgnJycpZs2aNbC2tkZMTAyampoQFhaG7OxsWFlZSTHbtm1DYmKiNHl+2rRpyMzMlNZbWVlh9+7dSEhIQGhoKOzt7REbG4tVq1Yp11giMov2gZ1rXTuwA0Aa2PH19YWvry/S0tK6HNhxc3ODq6srUlJSuhzY2bBhAwBg7ty5XQ7srFy5EufOnePADhEREV2XIsnXgQMHZK9VKhVSU1ORmpra5Xvs7Oywbt06rFu3rssYV1dX5OTkdLvv4cOHY9euXb2pLhFZKA7sEBER0UDWL1e+iIjMgQM7REREZEn67EeWiYiIiIiI6D+YfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERDXnp6ekYP348nJyc4OHhgenTp6O6uloWI4RAamoqvLy8YG9vj0mTJuHYsWOymObmZixYsADu7u5wdHTEtGnTcPr0aVmMwWCAXq+HRqOBRqOBXq/H+fPnZTGnTp3C1KlT4ejoCHd3dyQmJqKlpaVP2k5EymHyRURERENecXEx5s+fj9LSUhQVFeHy5cuIiIjAxYsXpZgVK1Zg9erVyMzMRFlZGbRaLSZPnozGxkYpJikpCfn5+cjLy0NJSQkuXLiA6OhotLW1STGxsbGorKxEQUEBCgoKUFlZCb1eL61va2tDVFQULl68iJKSEuTl5WHHjh1ITk5W5sMgoj5j3d8VICIiIupvBQUFstebN2+Gh4cHysvL8dBDD0EIgbVr12LZsmWYMWMGAGDLli3w9PREbm4u5s2bB6PRiE2bNmHr1q0IDw8HAOTk5MDb2xt79+5FZGQkTpw4gYKCApSWliI4OBgAkJWVBZ1Oh+rqavj5+aGwsBDHjx9HTU0NvLy8AAAZGRmIi4vDyy+/DGdnZwU/GSIyJ175IiIiIurAaDQCAFxdXQEAJ0+eRF1dHSIiIqQYtVqNiRMn4uDBgwCA8vJytLa2ymK8vLwQEBAgxRw6dAgajUZKvABgwoQJ0Gg0spiAgAAp8QKAyMhINDc3o7y8vNP6Njc3o6GhQbYQ0cDD5IuIiIjoGkIILFq0CA8++CACAgIAAHV1dQAAT09PWaynp6e0rq6uDra2tnBxcek2xsPDw2SfHh4espiO+3FxcYGtra0U01F6ero0h0yj0cDb27u3zSYiBTD5IiIiIrrG008/jX/84x/Yvn27yTqVSiV7LYQwKeuoY0xn8TcSc62lS5fCaDRKS01NTbd1IqL+weSLiIiI6P9bsGABdu7cif379+POO++UyrVaLQCYXHmqr6+XrlJptVq0tLTAYDB0G3PmzBmT/Z49e1YW03E/BoMBra2tJlfE2qnVajg7O8sWIhp4mHwRERHRkCeEwNNPP4333nsPH3/8MXx8fGTrfXx8oNVqUVRUJJW1tLSguLgYISEhAICgoCDY2NjIYmpra1FVVSXF6HQ6GI1GHDlyRIo5fPgwjEajLKaqqgq1tbVSTGFhIdRqNYKCgszfeCJSDJ92SEREREPe/PnzkZubiw8++ABOTk7SlSeNRgN7e3uoVCokJSUhLS0Nvr6+8PX1RVpaGhwcHBAbGyvFzpkzB8nJyXBzc4OrqytSUlIwZswY6emHo0ePxpQpUxAfH48NGzYAAObOnYvo6Gj4+fkBACIiIuDv7w+9Xo+VK1fi3LlzSElJQXx8PK9oEVk4Jl9EREQ05K1fvx4AMGnSJFn55s2bERcXBwBYvHgxmpqakJCQAIPBgODgYBQWFsLJyUmKX7NmDaytrRETE4OmpiaEhYUhOzsbVlZWUsy2bduQmJgoPRVx2rRpyMzMlNZbWVlh9+7dSEhIQGhoKOzt7REbG4tVq1b1UeuJSClMvoiIiGjIE0JcN0alUiE1NRWpqaldxtjZ2WHdunVYt25dlzGurq7Iycnpdl/Dhw/Hrl27rlsnIrIsnPNFRERERESkACZfRERERERECmDyRUREREREpAAmX0RERERERApg8kVERERERKQAsydf6enpGD9+PJycnODh4YHp06ejurpaFiOEQGpqKry8vGBvb49Jkybh2LFjspjm5mYsWLAA7u7ucHR0xLRp03D69GlZjMFggF6vh0ajgUajgV6vx/nz52Uxp06dwtSpU+Ho6Ah3d3ckJiaipaXF3M0mIgWwfyEiIiJLZvbkq7i4GPPnz0dpaSmKiopw+fJlRERE4OLFi1LMihUrsHr1amRmZqKsrAxarRaTJ09GY2OjFJOUlIT8/Hzk5eWhpKQEFy5cQHR0NNra2qSY2NhYVFZWoqCgAAUFBaisrIRer5fWt7W1ISoqChcvXkRJSQny8vKwY8cOJCcnm7vZRKQA9i9ERERkyVSiJz9scRPOnj0LDw8PFBcX46GHHoIQAl5eXkhKSsKzzz4L4OootKenJ5YvX4558+bBaDTi9ttvx9atW/Hb3/4WAPD999/D29sbf/vb3xAZGYkTJ07A398fpaWlCA4OBgCUlpZCp9Phn//8J/z8/LBnzx5ER0ejpqYGXl5eAIC8vDzExcWhvr6+R78S39DQAI1GA6PReN34kUt29+gz+eaVqB7FEQ1mvTm2usL+RY59C9FV5uhfLF1PPwOeu9BQYM6/85vtX/p8zpfRaARw9QcFAeDkyZOoq6uTftUdANRqNSZOnIiDBw8CAMrLy9Ha2iqL8fLyQkBAgBRz6NAhaDQa6cQIACZMmACNRiOLCQgIkE6MACAyMhLNzc0oLy/vtL7Nzc1oaGiQLUQ0MFla/0JERERDW58mX0IILFq0CA8++CACAgIAAHV1dQAAT09PWaynp6e0rq6uDra2tnBxcek2xsPDw2SfHh4espiO+3FxcYGtra0U01F6ero0x0Oj0cDb27u3zSYiBVhi/8LBHSIioqGtT5Ovp59+Gv/4xz+wfft2k3UqlUr2WghhUtZRx5jO4m8k5lpLly6F0WiUlpqamm7rRET9wxL7Fw7uEBERDW19lnwtWLAAO3fuxP79+3HnnXdK5VqtFgBMRobr6+ulUWStVouWlhYYDIZuY86cOWOy37Nnz8piOu7HYDCgtbXVZMS6nVqthrOzs2whooHFUvsXDu4QERENbWZPvoQQePrpp/Hee+/h448/ho+Pj2y9j48PtFotioqKpLKWlhYUFxcjJCQEABAUFAQbGxtZTG1tLaqqqqQYnU4Ho9GII0eOSDGHDx+G0WiUxVRVVaG2tlaKKSwshFqtRlBQkLmbTkR9zNL7Fw7uEBERDW3W5t7g/PnzkZubiw8++ABOTk7SyLBGo4G9vT1UKhWSkpKQlpYGX19f+Pr6Ii0tDQ4ODoiNjZVi58yZg+TkZLi5ucHV1RUpKSkYM2YMwsPDAQCjR4/GlClTEB8fjw0bNgAA5s6di+joaPj5+QEAIiIi4O/vD71ej5UrV+LcuXNISUlBfHw8T3qILBD7FyIiIrJkZk++1q9fDwCYNGmSrHzz5s2Ii4sDACxevBhNTU1ISEiAwWBAcHAwCgsL4eTkJMWvWbMG1tbWiImJQVNTE8LCwpCdnQ0rKyspZtu2bUhMTJSeWjZt2jRkZmZK662srLB7924kJCQgNDQU9vb2iI2NxapVq8zdbCJSAPsXIiIismR9/jtflo6/80XUN/g7PPydL6K+wv6Fv/NFdK0h9TtfRERERERExOSLiIiIiIhIEUy+iIiIiIiIFMDki4iIiIiISAFMvoiIiIiIiBTA5IuIiIiIiEgBTL6IiIiIiIgUwOSLiIiIiIhIAUy+iIiIiIiIFMDki4iIiIiISAFMvoiIiIiIiBTA5IuIiIiIiEgBTL6IiIiIiIgUwOSLiIiIiIhIAUy+iIiIiIiIFMDki4iIiIiISAFMvoiIiIiIiBTA5IuIiIiIiEgBTL6IiIiIiIgUwOSLiIiIiIhIAUy+iIiIiIiIFMDki4iIiIiISAFMvoiIiIiIiBTA5IuIiIiIiEgBTL6IiIiIiIgUYN3fFSAiyzJyye7rxnzzSpQCNSEiIiKyLLzyRUREREREpIAhkXy9/vrr8PHxgZ2dHYKCgvDpp5/2d5WIaJBg/9JzI5fsvu5CRP/B/oVo8Bn0ydc777yDpKQkLFu2DBUVFfj5z3+ORx55BKdOnervqhGRhWP/QkR9hf0L0eA06JOv1atXY86cOXjyyScxevRorF27Ft7e3li/fn1/V42ILBz7FyLqK+xfiAanQf3AjZaWFpSXl2PJkiWy8oiICBw8eLDT9zQ3N6O5uVl6bTQaAQANDQ3X3d+V5ks9qldPtkU0UPXk77wnf+PtMUKIm65TfxiI/ctA71sGQxvIMrB/6Xn/wnMXGgrM+Xd+s/3LoE6+fvjhB7S1tcHT01NW7unpibq6uk7fk56ejhdffNGk3Nvb22z10qw126aIBqTe/I03NjZCo9H0WV36ykDsXwZD3zIY2kADB/sX9i9EvaHE+cugTr7aqVQq2WshhElZu6VLl2LRokXS6ytXruDcuXNwc3Pr8j3A1SzY29sbNTU1cHZ2Nk/FFcY2DAyW3oae1l8IgcbGRnh5eSlYO/Nj/9Izlt4GS68/MLTawP6lZ/3LUPqbGMjYhoFBqf5lUCdf7u7usLKyMhklqq+vNxlNaqdWq6FWq2Vlt912W4/36ezsbLF/dO3YhoHB0tvQk/pb4oh0O/YvN8bS22Dp9QeGThvYv/S8fxkqfxMDHdswMPR1/zKoH7hha2uLoKAgFBUVycqLiooQEhLST7UiosGA/QsR9RX2L0SD16C+8gUAixYtgl6vx7hx46DT6bBx40acOnUKTz31VH9XjYgsHPsXIuor7F+IBqdBn3z99re/xY8//oiXXnoJtbW1CAgIwN/+9jeMGDHCrPtRq9V44YUXTC75WxK2YWCw9DZYev17g/1Lz1l6Gyy9/gDbYGmU6F8Gw+fJNgwMbEPPqYSlPoeViIiIiIjIggzqOV9EREREREQDBZMvIiIiIiIiBTD5IiIiIiIiUgCTLyIiIiIiIgUw+erC66+/Dh8fH9jZ2SEoKAiffvppt/HFxcUICgqCnZ0d7rrrLrzxxhsmMTt27IC/vz/UajX8/f2Rn5/fV9UH0Ls2vPfee5g8eTJuv/12ODs7Q6fT4aOPPpLFZGdnQ6VSmSw//fTTgGjDgQMHOq3fP//5T1ncQP4e4uLiOm3DPffcI8Uo/T188sknmDp1Kry8vKBSqfD+++9f9z0D8XiwVL3tiwaaG/n7GUjS09Mxfvx4ODk5wcPDA9OnT0d1dXV/V6tX1q9fj7Fjx0o/HKrT6bBnz57+rtZNSU9Ph0qlQlJSUn9XZcDh+QvPX/qjDQPt/GVAn7sIMpGXlydsbGxEVlaWOH78uFi4cKFwdHQU3377bafxX3/9tXBwcBALFy4Ux48fF1lZWcLGxkb89a9/lWIOHjworKysRFpamjhx4oRIS0sT1tbWorS0dEC0YeHChWL58uXiyJEj4ssvvxRLly4VNjY24vPPP5diNm/eLJydnUVtba1s6Su9bcP+/fsFAFFdXS2r3+XLl6WYgf49nD9/Xlb3mpoa4erqKl544QUpRunv4W9/+5tYtmyZ2LFjhwAg8vPzu40fiMeDpert389A1Nu/n4EmMjJSbN68WVRVVYnKykoRFRUlhg8fLi5cuNDfVeuxnTt3it27d4vq6mpRXV0tnnvuOWFjYyOqqqr6u2o35MiRI2LkyJFi7NixYuHChf1dnQGF5y88f+mvNgy085eBfO7C5KsTDzzwgHjqqadkZaNGjRJLlizpNH7x4sVi1KhRsrJ58+aJCRMmSK9jYmLElClTZDGRkZHiscceM1Ot5Xrbhs74+/uLF198UXq9efNmodFozFXF6+ptG9o7L4PB0OU2Le17yM/PFyqVSnzzzTdSmdLfw7V60oENxOPBUpnjOB5ILDH56qi+vl4AEMXFxf1dlZvi4uIi3nzzzf6uRq81NjYKX19fUVRUJCZOnMjkqwOev1zF85ebN5jOXwbauQtvO+ygpaUF5eXliIiIkJVHRETg4MGDnb7n0KFDJvGRkZH47LPP0Nra2m1MV9u8GTfSho6uXLmCxsZGuLq6ysovXLiAESNG4M4770R0dDQqKirMVu9r3UwbAgMDMWzYMISFhWH//v2ydZb2PWzatAnh4eEmP6qp1PdwIwba8WCpzPH3Q+ZnNBoBwKRvtBRtbW3Iy8vDxYsXodPp+rs6vTZ//nxERUUhPDy8v6sy4PD85Sqev9y8oXj+ouSxwOSrgx9++AFtbW3w9PSUlXt6eqKurq7T99TV1XUaf/nyZfzwww/dxnS1zZtxI23oKCMjAxcvXkRMTIxUNmrUKGRnZ2Pnzp3Yvn077OzsEBoain/9619mrT9wY20YNmwYNm7ciB07duC9996Dn58fwsLC8Mknn0gxlvQ91NbWYs+ePXjyySdl5Up+DzdioB0PlsocxzGZlxACixYtwoMPPoiAgID+rk6vHD16FLfeeivUajWeeuop5Ofnw9/fv7+r1St5eXn4/PPPkZ6e3t9VGZB4/nIVz1/6pw3XssTzFyWPBeubq+rgpVKpZK+FECZl14vvWN7bbd6sG93f9u3bkZqaig8++AAeHh5S+YQJEzBhwgTpdWhoKO6//36sW7cOr776qvkqfo3etMHPzw9+fn7Sa51Oh5qaGqxatQoPPfTQDW3THG50f9nZ2bjtttswffp0WXl/fA+9NRCPB0vFz2ngePrpp/GPf/wDJSUl/V2VXvPz80NlZSXOnz+PHTt2YPbs2SguLraYBKympgYLFy5EYWEh7Ozs+rs6AxrPX3j+Yi5D7fxFqWOBV746cHd3h5WVlUkWW19fb5LtttNqtZ3GW1tbw83NrduYrrZ5M26kDe3eeecdzJkzB3/5y1+ue1vHLbfcgvHjx/fJiMXNtOFaEyZMkNXPUr4HIQTeeust6PV62Nradhvbl9/DjRhox4OlMtcxQOaxYMEC7Ny5E/v378edd97Z39XpNVtbW/zsZz/DuHHjkJ6ejnvvvRd//vOf+7taPVZeXo76+noEBQXB2toa1tbWKC4uxquvvgpra2u0tbX1dxX7Hc9feP5iLkPx/EXJY4HJVwe2trYICgpCUVGRrLyoqAghISGdvken05nEFxYWYty4cbCxsek2pqtt3owbaQNwdcQoLi4Oubm5iIqKuu5+hBCorKzEsGHDbrrOHd1oGzqqqKiQ1c8Svgfg6uNOv/rqK8yZM+e6++nL7+FGDLTjwVKZ6xigmyOEwNNPP4333nsPH3/8MXx8fPq7SmYhhEBzc3N/V6PHwsLCcPToUVRWVkrLuHHjMHPmTFRWVsLKyqq/q9jveP7C8xdzGYrnL4oeC716PMcQ0f54zU2bNonjx4+LpKQk4ejoKD2xZcmSJUKv10vx7Y+nfOaZZ8Tx48fFpk2bTB5P+fe//11YWVmJV155RZw4cUK88sorijwitKdtyM3NFdbW1uK1116TPf7z/PnzUkxqaqooKCgQ//73v0VFRYV4/PHHhbW1tTh8+PCAaMOaNWtEfn6++PLLL0VVVZVYsmSJACB27NghxQz076HdrFmzRHBwcKfbVPp7aGxsFBUVFaKiokIAEKtXrxYVFRXS42Yt4XiwVNf7+7EE1/v7Gej+8Ic/CI1GIw4cOCDrGy9dutTfVeuxpUuXik8++UScPHlS/OMf/xDPPfecuOWWW0RhYWF/V+2m8GmHpnj+wvOX/mpDu4Fy/jKQz12YfHXhtddeEyNGjBC2trbi/vvvlz1WePbs2WLixImy+AMHDojAwEBha2srRo4cKdavX2+yzXfffVf4+fkJGxsbMWrUKNlB1d9tmDhxogBgssyePVuKSUpKEsOHDxe2trbi9ttvFxEREeLgwYMDpg3Lly8Xd999t7CzsxMuLi7iwQcfFLt37zbZ5kD+HoS4+lsZ9vb2YuPGjZ1uT+nvof0RuF39bVjK8WCpuvv7sQTX+/sZ6DqrOwCxefPm/q5ajz3xxBPS39Dtt98uwsLCLD7xEoLJV1d4/sLzl/5ogxAD6/xlIJ+7qIT4/7PJiIiIiIiIqM9wzhcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESmAyRcREREREZECmHwREREREREpgMkXERERERGRAph8ERERERERKYDJFxERERERkQKYfBERERERESng/wFfw0/wjqsW2AAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x1000 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df_raw.hist(figsize=(10, 10), bins=20, grid=False)"]}, {"cell_type": "code", "execution_count": 13, "id": "116df1214de1420a", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.371477Z", "start_time": "2025-07-05T22:12:14.223182Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Duplicate rows in the dataset: 0\n"]}], "source": ["# Display duplicates in the dataset\n", "print(f\"Duplicate rows in the dataset: {df_raw.duplicated().sum()}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "256a5debfc8cc3db", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.485152Z", "start_time": "2025-07-05T22:12:14.469421Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique Patients in the dataset: 62299\n"]}], "source": ["# Display the unique patient's in the dataset\n", "print(f\"Unique Patients in the dataset: {df_raw['PatientId'].nunique()}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "1c2f132bdc4342fe", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.563996Z", "start_time": "2025-07-05T22:12:14.550110Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique Appointments in the dataset: 110527\n"]}], "source": ["# Display the unique appointments in the dataset\n", "print(f\"Unique Appointments in the dataset: {df_raw['AppointmentID'].nunique()}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "8655bc80e4636376", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.645069Z", "start_time": "2025-07-05T22:12:14.630639Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Value count Age\n", " 0      3539\n", " 1      2273\n", " 52     1746\n", " 49     1652\n", " 53     1651\n", "        ... \n", " 115       5\n", " 100       4\n", " 102       2\n", " 99        1\n", "-1         1\n", "Name: count, Length: 104, dtype: int64\n"]}], "source": ["# Display `Age` column explore\n", "print(\"Value count\", df_raw['Age'].value_counts())"]}, {"cell_type": "code", "execution_count": 17, "id": "a0826a68d72814bd", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.791093Z", "start_time": "2025-07-05T22:12:14.715296Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "('Age', 'PatientId')", "rawType": "object", "type": "unknown"}, {"name": "Age", "rawType": "int64", "type": "integer"}], "ref": "4f1d4c14-231d-41ab-9b1c-cc368fd83805", "rows": [["(-1, 465943158731293.0)", "1"], ["(0, 269919126.0)", "2"], ["(0, 342478778.0)", "1"], ["(0, 427962175.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "2"], ["(0, **********.0)", "1"], ["(0, **********.0)", "2"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "3"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "2"], ["(0, **********.0)", "2"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "2"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "1"], ["(0, **********.0)", "2"], ["(0, **********.0)", "2"], ["(0, 11919882439.0)", "3"], ["(0, 11939987322.0)", "2"], ["(0, 11957316286.0)", "2"], ["(0, 12457258714.0)", "3"], ["(0, 14817536338.0)", "2"], ["(0, 16893874933.0)", "2"], ["(0, 18668413134.0)", "2"], ["(0, 19946368469.0)", "1"], ["(0, 21234594283.0)", "1"], ["(0, 21848361678.0)", "1"], ["(0, 21877729295.0)", "2"], ["(0, 22556198456.0)", "1"], ["(0, 23144198351.0)", "1"], ["(0, 25675813831.0)", "1"], ["(0, 27737444456.0)", "3"], ["(0, 27891283143.0)", "2"], ["(0, 31124277979.0)", "2"], ["(0, 31824245816.0)", "2"], ["(0, 33167198526.0)", "1"], ["(0, 33438311349.0)", "1"], ["(0, 35961793175.0)", "2"], ["(0, 36986195138.0)", "2"], ["(0, 37962752846.0)", "1"], ["(0, 41631427582.0)", "4"]], "shape": {"columns": 1, "rows": 63467}}, "text/plain": ["Age   PatientId   \n", "-1    4.659432e+14    1\n", " 0    2.699191e+08    2\n", "      3.424788e+08    1\n", "      4.279622e+08    1\n", "      1.192462e+09    1\n", "                     ..\n", " 100  5.578313e+13    1\n", " 102  2.342836e+11    1\n", "      9.762948e+14    1\n", " 115  3.196321e+13    4\n", "      7.482346<PERSON>+14    1\n", "Name: Age, Length: 63467, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw.groupby(['Age', 'PatientId'])['Age'].count()"]}, {"cell_type": "markdown", "id": "b6883afc68c0863e", "metadata": {}, "source": ["There are actually just two patients aged 115, which might be plausible as the maximum age of a human is 122. Let's keep it as it is."]}, {"cell_type": "code", "execution_count": 18, "id": "f4fa722f5314bb9b", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:14.886956Z", "start_time": "2025-07-05T22:12:14.875549Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "Handcap", "rawType": "int64", "type": "integer"}, {"name": "count", "rawType": "int64", "type": "integer"}], "ref": "d48e40a2-164d-4613-89b6-7eb7647a4d73", "rows": [["0", "108286"], ["1", "2042"], ["2", "183"], ["3", "13"], ["4", "3"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["Handcap\n", "0    108286\n", "1      2042\n", "2       183\n", "3        13\n", "4         3\n", "Name: count, dtype: int64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw['Handcap'].value_counts()"]}, {"cell_type": "code", "execution_count": 19, "id": "88091f0f050d533e", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:15.061153Z", "start_time": "2025-07-05T22:12:15.035639Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "Neighbourhood", "rawType": "object", "type": "string"}, {"name": "count", "rawType": "int64", "type": "integer"}], "ref": "05f9a7a3-f0ab-42c2-a973-b37f7eba9fa0", "rows": [["JARDIM CAMBURI", "7717"], ["MARIA ORTIZ", "5805"], ["RESISTÊNCIA", "4431"], ["JARDIM DA PENHA", "3877"], ["ITARARÉ", "3514"], ["CENTRO", "3334"], ["TABUAZEIRO", "3132"], ["SANTA MARTHA", "3131"], ["JESUS DE NAZARETH", "2853"], ["BONFIM", "2773"], ["SANTO ANTÔNIO", "2746"], ["SANTO ANDRÉ", "2571"], ["CARATOÍRA", "2565"], ["JABOUR", "2509"], ["SÃO PEDRO", "2448"], ["ILHA DO PRÍNCIPE", "2266"], ["NOVA PALESTINA", "2264"], ["ANDORINHAS", "2262"], ["DA PENHA", "2217"], ["ROMÃO", "2215"], ["GURIGICA", "2018"], ["SÃO JOSÉ", "1977"], ["BELA VISTA", "1907"], ["MARUÍPE", "1902"], ["FORTE SÃO JOÃO", "1889"], ["ILHA DE SANTA MARIA", "1885"], ["SÃO CRISTÓVÃO", "1836"], ["REDENÇÃO", "1553"], ["SÃO BENEDITO", "1439"], ["JOANA D´ARC", "1427"], ["CRUZAMENTO", "1398"], ["CONSOLAÇÃO", "1376"], ["SANTA TEREZA", "1332"], ["PRAIA DO SUÁ", "1288"], ["SANTOS DUMONT", "1276"], ["ILHA DAS CAIEIRAS", "1071"], ["GRANDE VITÓRIA", "1071"], ["INHANGUETÁ", "1057"], ["PRAIA DO CANTO", "1035"], ["BENTO FERREIRA", "858"], ["VILA RUBIM", "851"], ["DO QUADRO", "849"], ["CONQUISTA", "849"], ["REPÚBLICA", "835"], ["MONTE BELO", "824"], ["PARQUE MOSCOSO", "802"], ["GOIABEIRAS", "700"], ["JUCUTUQUARA", "694"], ["FONTE GRANDE", "682"], ["MATA DA PRAIA", "644"]], "shape": {"columns": 1, "rows": 81}}, "text/plain": ["Neighbourhood\n", "JARDIM CAMBURI                 7717\n", "MARIA ORTIZ                    5805\n", "RESISTÊNCIA                    4431\n", "JARDIM DA PENHA                3877\n", "ITARARÉ                        3514\n", "                               ... \n", "ILHA DO BOI                      35\n", "ILHA DO FRADE                    10\n", "AEROPORTO                         8\n", "ILHAS OCEÂNICAS DE TRINDADE       2\n", "PARQUE INDUSTRIAL                 1\n", "Name: count, Length: 81, dtype: int64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw['Neighbourhood'].value_counts()"]}, {"cell_type": "code", "execution_count": 20, "id": "26ab81b866f9a563", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:15.305451Z", "start_time": "2025-07-05T22:12:15.254197Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "8f791600-accd-4335-be37-31c21956e1c7", "rows": [["PatientId", "0"], ["AppointmentID", "0"], ["Gender", "0"], ["ScheduledDay", "0"], ["AppointmentDay", "0"], ["Age", "0"], ["Neighbourhood", "0"], ["Scholarship", "0"], ["Hipertension", "0"], ["Diabetes", "0"], ["Alcoholism", "0"], ["Handcap", "0"], ["SMS_received", "0"], ["No-show", "0"]], "shape": {"columns": 1, "rows": 14}}, "text/plain": ["PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw.isnull().sum()"]}, {"cell_type": "code", "execution_count": 21, "id": "7d927fa8b2008dd9", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:15.582673Z", "start_time": "2025-07-05T22:12:15.533569Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "f5db1b32-5e2c-4da7-85f6-4dba41727cd0", "rows": [["PatientId", "0"], ["AppointmentID", "0"], ["Gender", "0"], ["ScheduledDay", "0"], ["AppointmentDay", "0"], ["Age", "0"], ["Neighbourhood", "0"], ["Scholarship", "0"], ["Hipertension", "0"], ["Diabetes", "0"], ["Alcoholism", "0"], ["Handcap", "0"], ["SMS_received", "0"], ["No-show", "0"]], "shape": {"columns": 1, "rows": 14}}, "text/plain": ["PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw.isna().sum()"]}, {"cell_type": "code", "execution_count": 22, "id": "e040a65f592f0824", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:15.952530Z", "start_time": "2025-07-05T22:12:15.946571Z"}}, "outputs": [], "source": ["cols = df_raw.columns"]}, {"cell_type": "code", "execution_count": 23, "id": "89d4ef9d4d76a4d9", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:16.967975Z", "start_time": "2025-07-05T22:12:16.096287Z"}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAh0AAAHFCAYAAABW5Us4AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALC5JREFUeJzt3Xl8lNWh//HvhIQkkAUTIAtLApWl7KsQsARENosK1IssKiiIFSKb1ipwDbgUpQJKgXKpLJWtqMBtLkKFK5siKmK4LCK0hUAhhLUkIUAgyfn9wW/GDAmQhHCSTD7v1ysvZeaZZ845DJlPnnlm4jDGGAEAANxlXiU9AAAAUD4QHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHUAJ+eabb9S3b1/Vrl1bvr6+CgsLU0xMjF588cVC76tz585q0qTJXRhlXg6HQ5MnTy7SbaOjozV06FDr40lKSpLD4XB9+fj4KDQ0VG3bttW4ceO0f//+Yh0TgPwRHUAJ+PTTT9WhQwelpaVp2rRp2rBhg95//3117NhRK1euLOnheawXXnhBO3bs0NatW7VkyRL16dNHCQkJat68uX7/+9+X9PAAj+dd0gMAyqNp06apTp06+uyzz+Tt/dM/wwEDBmjatGklODLPVrt2bbVv397154ceekjjx49Xv3799PLLL6tJkybq1atXCY4Q8Gwc6QBKwLlz51S1alW34HDy8sr7z3L58uWKiYlRQECAAgIC1KJFCy1YsCDPdjt37tQvfvELVapUSXXr1tXbb7+tnJwct23S0tL00ksvqU6dOqpYsaJq1KihsWPHKiMjI892zz77rEJDQxUQEKCePXvq0KFDee5z6NChio6OznP55MmT5XA4brcUxT6ewvL399eCBQvk4+PjdrTjzJkzGjlypBo1aqSAgABVr15dDzzwgL744gvXNsYY1atXTz169Miz34sXLyo4OFijRo264zECnoLoAEpATEyMvvnmG40ePVrffPONrl27dtNtX3vtNQ0ePFiRkZFavHix1qxZoyFDhujo0aNu26WkpGjw4MF64oknlJCQoF69eunVV1/V0qVLXdtcunRJsbGx+vOf/6zRo0dr/fr1+u1vf6vFixfrkUcekfOXThtj1KdPHy1ZskQvvvii1qxZo/bt2xf7UYDSMp7IyEi1bt1aX331lbKysiRJ58+flyTFx8fr008/1aJFi1S3bl117txZW7ZskXT9fJIXXnhBGzdu1N///ne3fX744YdKS0sjOoDcDADrzp49a+6//34jyUgyPj4+pkOHDmbq1KkmPT3dtd3hw4dNhQoVzODBg2+5v9jYWCPJfPPNN26XN2rUyPTo0cP156lTpxovLy+zc+dOt+0++eQTI8msW7fOGGPM+vXrjSTz/vvvu2331ltvGUkmPj7eddmQIUNMVFRUnjHFx8ebG7/FREVFmSFDhtzV8eTnyJEjRpL5/e9/f9NtHn/8cSPJnDp1Kt/rs7KyzLVr10zXrl1N3759XZenpaWZwMBAM2bMGLftGzVqZLp06XLLcQHlDUc6gBIQGhqqL774Qjt37tTbb7+tRx99VIcOHdKrr76qpk2b6uzZs5KkjRs3Kjs7u0A/LYeHh+u+++5zu6xZs2ZuR0TWrl2rJk2aqEWLFsrKynJ99ejRQw6Hw/UT/ObNmyVJgwcPdtvfoEGD7mTaeZSm8Zj/f1Qlt3nz5qlVq1by8/OTt7e3fHx89Pnnn+vAgQOubQIDA/X0009r8eLFrpeENm3apB9++EFxcXHFNj7AExAdQAlq06aNfvvb3+rjjz9WcnKyxo0bp6SkJNfJpGfOnJEk1axZ87b7Cg0NzXOZr6+vLl++7PrzqVOntGfPHvn4+Lh9BQYGyhjjip1z587J29s7zz7Dw8OLPNf8lKbxHD16VL6+vgoJCZEkzZgxQ88//7zatWunVatW6euvv9bOnTvVs2dPtzWVrr8rJj09XcuWLZMkzZ49WzVr1tSjjz5abOMDPAHvXgFKCR8fH8XHx2vmzJnat2+fJKlatWqSpOPHj6tWrVp3fB9Vq1aVv7+/Fi5ceNPrpesBk5WVpXPnzrk90aekpOS5jZ+fnzIzM/Nc7gwG2+MpihMnTmjXrl2KjY11ndy7dOlSde7cWX/84x/dtk1PT89z+3vvvVe9evXSnDlz1KtXLyUkJGjKlCmqUKFCsYwP8BQc6QBKwMmTJ/O93HnYPjIyUpLUvXt3VahQIc8TX1H17t1b//znPxUaGqo2bdrk+XK+C6VLly6S5PrJ3Wn58uV59hkdHa3Tp0/r1KlTrsuuXr2qzz77rETGU1iXL1/W8OHDlZWVpZdfftl1ucPhkK+vr9u2e/bs0Y4dO/Ldz5gxY7Rnzx4NGTJEFSpU0LPPPnvHYwM8DUc6gBLQo0cP1axZUw8//LAaNmyonJwc7d69W9OnT1dAQIDGjBkj6foT+oQJE/TGG2/o8uXLGjhwoIKDg/XDDz/o7NmzmjJlSqHud+zYsVq1apU6deqkcePGqVmzZsrJydGxY8e0YcMGvfjii2rXrp26d++uTp066eWXX1ZGRobatGmj7du3a8mSJXn2+fjjj+u1117TgAED9Jvf/EZXrlzRrFmzlJ2dXSLjuZVjx47p66+/Vk5OjlJTU5WYmKiFCxfq6NGjmj59urp37+7atnfv3nrjjTcUHx+v2NhYHTx4UK+//rrq1KnjeodLbt26dVOjRo20efNmPfHEE6pevXqhxgaUCyV8IitQLq1cudIMGjTI1KtXzwQEBBgfHx9Tu3Zt8+STT5offvghz/Yffvihadu2rfHz8zMBAQGmZcuWZtGiRa7rY2NjTePGjfPcLr93lly8eNFMmjTJNGjQwFSsWNEEBwebpk2bmnHjxpmUlBTXdhcuXDDPPPOMqVKliqlUqZLp1q2b+fHHH/N9t8i6detMixYtjL+/v6lbt66ZPXt2gd69crfGcyPnu1ecXxUqVDD33HOPad26tRk7dqzZv39/nttkZmaal156ydSoUcP4+fmZVq1amf/+7/++6bt1jDFm8uTJRpL5+uuvbzkeoLxyGJPPKdsAgEJr06aNHA6Hdu7cWdJDAUolXl4BgDuQlpamffv2ae3atdq1a5fWrFlT0kMCSi2iAwDuwPfff68uXbooNDRU8fHx6tOnT0kPCSi1eHkFAABYwVtmAQCAFUQHAACwgugAAABWlKoTSXNycpScnKzAwEA5HI6SHg4AACgAY4zS09MVGRkpL6+bH88oVdGRnJxcLL9fAgAA2Pevf/3rlr+gslRFR2BgoKTrgw4KCirh0QAAgIJIS0tTrVq1XM/jN1OqosP5kkpQUBDRAQBAGXO7UyM4kRQAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFjhXdIDAAAgP6dOnVJqampJD6NQgoODFRYWVtLDKLWIDgBAqXPq1Ck98eRTunY1s6SHUig+FX21dMmHhMdNEB0AgFInNTVV165m6nLdWOX4BRfrvr0uX5D/kW26XKeTcvyrFN9+r6RKh7cqNTWV6LgJogMAUGrl+AUrp3LVu7Nv/yp3bd/IHyeSAgAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQBQSly5ckWHDh3SlStXSnoo8ECl4fFFdABAKXHs2DGNGDFCx44dK+mhwAOVhscX0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVniX9ADutuzsbO3Zs0fnz59XSEiImjVrpgoVKpT0sAqlIHM4fvy4hg8fritXrrhdPnr0aPXr1++W+z9//rzGjx+v5ORkZWVlycfHR1WqVNHs2bNVrVq1Yp9PcXCuSXJystavX6+MjAxFRkZq8ODBiouLU05Ojry8vPTBBx+obt26brc9c+aM4uLilJqaquDgYL3//vs6efKkUlJS9OWXXyozM1M1a9bUc889J39//1ve/+nTp3XgwAFlZWUpKSlJXl5eOnz4sBwOh0JDQzVjxgyFhIRIklJTUzVx4kSlpKQoICBAffv21dWrVxUUFKS0tDRdvXpVCxcuVHZ2tnx8fLRgwQLVrl1bknTs2DENGzZM165dkyRVrVpVFSpUUFhYmNLS0uTr66uDBw+6xjd//nzVr1//lmvoXId///vfunr1quvyefPmqWHDhm7z3LVrl5YuXXrbv5fct929e7fGjh1729s4vfTSS+rdu7ck6X//93/15ptvFvi2oaGhmjRpksaNG1fg2zh17txZkydP1sCBA3Xy5MlC337mzJkFvt8tW7bke/9AeeEwxpiSHoRTWlqagoODlZqaqqCgoDve37Zt2zR37lylpKS4LgsPD9fIkSPVqVOnO96/DQWZQ9euXZWdnX3L/eT3zU6S+vXrp/Pnz9/0dgEBAVq7dm3hB34X5bcmt+Ocf+/evXXx4sUC365jx45666237uj+Q0JC5O/vrxMnThT4fp28vK4fjMzJySn0baWb/70XZB1ef/31Qq8zbi/338mtguNmf3flxaFDhzRixAhlNHpEOZWrFuu+vTLOqvIPCcW+b+d+CxL9JcG5pndjfAV9/vbYl1e2bdum+Ph41a1bV3PmzNG6des0Z84c1a1bV/Hx8dq2bVtJD/G2CjKHggSHlP83t/yCw8fHx+3PFy9edP30WRo41yQ9PV2S5HA4dN999+XZLjQ01G0unTt3dnuijY6O1qBBg/Lcrnv37mrZsqUkqUKFCtq+fbsmTpyY5/6dMVCQo2bnz593Cw7n0YubqVr1p2+COTk5RQ4OKf+/94KG12uvvUZw3AXOv5PbHeHgCAg8kUdGR3Z2tubOnauYmBi9+eabaty4sSpVqqTGjRvrzTffVExMjP74xz8W6Mm6pBRkDtOnT88zhwkTJmjLli365JNP8uxz9erVrv8/f/58nuBISEjQxo0b8xzZuHjxos6cOVOMsysa55q0bdtWGRkZcjgcWr9+vX7961+7tgkJCZHD4dC5c+e0Zs0aLV++3HWd84k2ISFBCxYs0KZNm9SuXTu3+3j22Wc1ffp0dejQQVWrVpW3t7e2b9+uy5cvu+6/Xbt2SklJUZUqVZSdnS2Hw+G6fbVq1VSlShV5eXnJ29v91ctq1aopJiZGmZmZiomJcb3s4lS9enXFxMTIx8dHMTExeW7vdLvLb7z+0KFDrv8/c+ZMnuBYvHixtmzZooSEhHz3i+J3Y1Bs2bJFW7Zs0fz582+5HVDWleg5HZmZmcrMzHT9OS0trVj2u2fPHqWkpOg///M/XT+ROnl5eWnw4MEaNWqU9uzZ4/qptrQp6Bxu1L17d0nXf1oODAx0HRGQpFmzZrnO7xg/fryk6z+pZ2dnKzo62nVILCAgQD//+c914MAB1/VxcXFauXLlXZlrQTnXpHr16pKkbt26yc/PT8OHD5d0fS7nz59X69attWvXLk2dOlVvvfWWfHx8XOdCOOeZmJiolJQU1yFG51o55+lc3wceeECbNm3Sf/3Xfyk2NlYpKSnq2LGjcnJyFBUVpQsXLigwMFBpaWmKjo5WUlKS+vfvr48++kjNmzdXYmKia/xnzpzRoEGDtGPHDg0YMEA7duxwm9/p06c1cOBA7dixQ/37989zfc2aNXX8+HFFR0frH//4R571CQsL04kTJ/JcP2LECNeh+ri4OLfbOBwORUdHS5KCgoIUFRWlo0ePFvavBnfgdi+j5I7G8qQsPw5L69hLw7hKNDqmTp2qKVOmFPt+nT/B16lTJ9/rnZff6lyGklbQOeQWHh7u9uenn35as2bNyvf2586dk3Q9YLKzszVixIg8t3355Zdd0ZGamlroORQ355o447R///6SfjrfYeDAgVq6dKlatmypXbt2KTk5WZI0YMAALVmyRJJc83Tuy/lT/zPPPKP333/fNU/n+jZs2FCbNm3S8ePHXbfJfdKlJNfRphEjRmjChAmKjIyUdP0lnhv5+fm5/depUqVKunTpknx9fSXJ9d/cnFF4421v5LxtSEhInsf4jX+PN55kPGzYML322mu33L9TlSpVdOHChQJti6K78d8mSr8bzwPDT0o0Ol599VXXT9zS9SeTWrVq3fF+nYetjxw5osaNG+e5/siRI27blUYFnUNuN77+vmjRopvuPzQ0VOnp6a4n7Pnz56tDhw55but8Qg0ODi7kDIqfc02cT74fffSRJkyYIC8vL+Xk5GjFihWS5Dq64Hzy/8tf/uLah3Oezn0FBARIkhYuXCjpp3k61/fHH3+UdP0og/M2FStWdBuX87wO56FxZ+w4wy4357uLbnyX0aVLlyTJdeQv9xFAJ2ds3XjbGzlvm19UBwcHu91+9erVeuGFF1x/XrBgwS33nRvBYceNL7mUF0ePHi2zT94TJ05UVFRUSQ8jj9KwpiUaHb6+vvn+RHenmjVrpvDwcC1btkxvvvmm28sTOTk5WrZsmSIiItSsWbNiv+/iUpA5OM8Uzm3Dhg3q3r27zp496/bSinT97bNOM2bMUL9+/VxRkZSUpLS0NAUFBenixYs6cOCApJ+iY/bs2XdlnoXhXBPnT/obN27U+PHj9cEHH+iZZ55Rdna2QkJC9P3330u6HrXJycmul1akn+bp3JfzCdq5VrNnz3atb1hYmOuE4+eee04VK1ZUeHi4Tpw4IS8vL9ehSudtk5KSVK1aNW3YsEFeXl7au3ev2/irVaumb7/9VmFhYfr222/zHImoXr26vv32W0VEROjbb7+Vt7e3srKyXNcfP37cdT/5OXXqVL7X537Smj17tv7jP/7D9WdjjJKSkhQdHa20tLRScfi1vOncufMtX2Ipje+CwK1FRUXx93YTHvk5HRUqVNDIkSMVHx+vSZMmafDgwapTp46OHDmiZcuWaceOHZoyZUqp/ryOgs5hypQpbieT/u53v9Pvfve7fPeZ+1B6SEhInie9Rx55xO38B6eAgIBS8XkdudekcuXKysjIUK9evdSmTRvXNs75hISEqG/fvm5zCQgI0MWLF/XII48oKipKnTt3djsKIl1/gj579qwSExNdLy117NjR9XkdzvuPiIhQcnKyaxun3Cfc3viukzNnzujMmTOqVatWnvM1pOvndJw+fVohISH5Xu+UO0Tyu/zG63N/86tWrZprHZyGDh160/vC3bFlyxa3k0RvdsJoeX/bLDxPufucjoiICD3//PNl+nM6bpwDn9Nxe3xOhzs+p6Pk8DkdBcPndBS/0vA5HR55pMOpU6dO6tixY5n+RNKCzOHzzz8v8ieSrl69usx9ImnuNSnsJ5KuXbv2jj+RNPf9l9VPJM29Drf6RFLnPPlE0pu7k08kvfGIh1N5PY8Dns+jj3QAQFlyN38SLWs40lH8SsORDo/8cDAAAFD6EB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAAYAXRAQAArCA6AACAFUQHAACwgugAAABWEB0AAMAKogMAAFhBdAAAACuIDgAoJWrXrq358+erdu3aJT0UeKDS8PjyLrF7BgC48fPzU/369Ut6GPBQpeHxxZEOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAAwArvkh4AAAA343Ultfj3efmC23+Lbb93YayehugAAJQ6wcHB8qnoKx3eetfuw//ItmLfp09FXwUHBxf7fj0F0QEAKHXCwsK0dMmHSk0tW0cPgoODFRYWVtLDKLWIDgBAqRQWFsYTuIfhRFIAAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAriA4AAGCFd0kPIDdjjCQpLS2thEcCAAAKyvm87Xwev5lSFR3p6emSpFq1apXwSAAAQGGlp6crODj4ptc7zO2yxKKcnBwlJycrMDBQ6enpqlWrlv71r38pKCiopIdmXVpaWrmevxPr8JPyuhbldd43Ku/rUN7n71Ra18EYo/T0dEVGRsrL6+ZnbpSqIx1eXl6qWbOmJMnhcEiSgoKCStXC2lbe5+/EOvykvK5FeZ33jcr7OpT3+TuVxnW41REOJ04kBQAAVhAdAADAilIbHb6+voqPj5evr29JD6VElPf5O7EOPymva1Fe532j8r4O5X3+TmV9HUrViaQAAMBzldojHQAAwLMQHQAAwAqiAwAAWEF0AAAAKwoVHVOnTlXbtm0VGBio6tWrq0+fPjp48KDbNsYYTZ48WZGRkfL391fnzp21f/9+t23mz5+vzp07KygoSA6HQxcuXMhzX9HR0XI4HG5fr7zyyi3Hd+XKFQ0dOlRNmzaVt7e3+vTpk2ebkydPatCgQWrQoIG8vLw0duzYcjX/1atXq1u3bqpWrZqCgoIUExOjzz77rMBrYHsdJOnTTz9Vu3bt5O/vr6pVq6pfv363HePevXsVGxsrf39/1ahRQ6+//rrb7wS4k8dBbp6wFkV5THjCvL/88kt17NhRoaGh8vf3V8OGDTVz5szb7tfT1iG37du3y9vbWy1atCg389+yZUue77UOh0M//vhjgdbAU9ZBkjIzMzVx4kRFRUXJ19dXP/vZz7Rw4cICr0NBFCo6tm7dqlGjRunrr7/Wxo0blZWVpe7duysjI8O1zbRp0zRjxgzNnj1bO3fuVHh4uLp16+b6vSqSdOnSJfXs2VMTJky45f29/vrrOnnypOtr0qRJt9w+Oztb/v7+Gj16tB588MF8t8nMzFS1atU0ceJENW/evBCz94z5b9u2Td26ddO6deu0a9cudenSRQ8//LASExNL5TqsWrVKTz75pJ5++mn93//9n7Zv365BgwbdcnxpaWnq1q2bIiMjtXPnTv3hD3/Qu+++qxkzZri2uZPHgaetRVEeE54w78qVKysuLk7btm3TgQMHNGnSJE2aNEnz58+/5b49bR2cUlNT9dRTT6lr167lcv4HDx50+35br169crcO/fv31+eff64FCxbo4MGDWrFihRo2bFjgdSgQcwdOnz5tJJmtW7caY4zJyckx4eHh5u2333Ztc+XKFRMcHGzmzZuX5/abN282ksy///3vPNdFRUWZmTNnFnlsQ4YMMY8++ugtt4mNjTVjxowp8n2U9fk7NWrUyEyZMqXI93W31uHatWumRo0a5oMPPijUeObOnWuCg4PNlStXXJdNnTrVREZGmpycnDzb3+njILeyvhZOhX1MeMq8+/bta5544olC3VduZXkdHn/8cTNp0iQTHx9vmjdvXqj7cSqL87/V9+GiKovrsH79ehMcHGzOnTtXqH0X1h2d05GamipJCgkJkSQdOXJEKSkp6t69u2sbX19fxcbG6quvvir0/t955x2FhoaqRYsWeuutt3T16tU7GW6x84T55+TkKD093TWHorhb6/D999/rxIkT8vLyUsuWLRUREaFevXrlOSR5ox07dig2Ntbtw3N69Oih5ORkJSUlFWJmhecJa1GUx4QnzDsxMVFfffWVYmNjCzy+G5XVdVi0aJH++c9/Kj4+vsBjyk9Znb8k1367du2qzZs3F3hs+SmL65CQkKA2bdpo2rRpqlGjhurXr6+XXnpJly9fLvD4CqLI0WGM0fjx43X//ferSZMmkqSUlBRJUlhYmNu2YWFhrusKasyYMfrLX/6izZs3Ky4uTu+9955GjhxZ1OEWO0+Z//Tp05WRkaH+/fsX6fZ3cx0OHz4sSZo8ebImTZqktWvX6p577lFsbKzOnz9/09ulpKTke9+5x3Y3eMpaFPYxUdbnXbNmTfn6+qpNmzYaNWqUhg8fXuDx5VZW1+Hvf/+7XnnlFS1btkze3kX/HaBldf4RERGaP3++Vq1apdWrV6tBgwbq2rWrtm3bVuDx5VZW1+Hw4cP68ssvtW/fPq1Zs0bvvfeePvnkE40aNarA4yuIIkdHXFyc9uzZoxUrVuS5zvkbYp2MMXkuu51x48YpNjZWzZo10/DhwzVv3jwtWLBA586dkyQ1btxYAQEBCggIUK9evYo6jSLzhPmvWLFCkydP1sqVK1W9evUi7eNurkNOTo4kaeLEifrVr36l1q1ba9GiRXI4HPr4448l3Xwd8rvv/C4vTp6wFkV5TJT1eX/xxRf67rvvNG/ePL333nv5zqMgyuI6ZGdna9CgQZoyZYrq169f4PHkpyzOX5IaNGigZ599Vq1atVJMTIzmzp2rX/7yl3r33XcLPL7cyuo65OTkyOFwaNmyZbrvvvv00EMPacaMGVq8eHGxHu0oUta+8MILSkhI0LZt21y/il6SwsPDJV0vp4iICNflp0+fzlNZhdW+fXtJ0j/+8Q+FhoZq3bp1unbtmiTJ39//jvZdWJ4w/5UrV2rYsGH6+OOPb3rS6e3c7XVw3rZRo0auy3x9fVW3bl0dO3ZMkvJdh/Dw8Dw/PZw+fVpS3p80iosnrEVRHhOeMO86depIkpo2bapTp05p8uTJGjhwYIHHKJXddUhPT9d3332nxMRExcXFSbr+5GOMkbe3tzZs2KAHHnjAY+d/M+3bt9fSpUsLPD6nsrwOERERqlGjhtuvp//5z38uY4yOHz9eqBNrb6VQRzqMMYqLi9Pq1au1adMm1z9Wpzp16ig8PFwbN250XXb16lVt3bpVHTp0uKOBOs+kdy56VFSU7r33Xt17772qUaPGHe27oDxl/itWrNDQoUO1fPly/fKXvyz0WGytQ+vWreXr6+v21rNr164pKSlJUVFRkvJfh5iYGG3bts3tHJgNGzYoMjJS0dHRhZ7vrXjKWhT2MeEp885vXpmZmQUeX1lfh6CgIO3du1e7d+92ff36179WgwYNtHv3brVr186j538ziYmJbnFwO56wDh07dlRycrIuXrzo2ubQoUPy8vJyC6g7VpizTp9//nkTHBxstmzZYk6ePOn6unTpkmubt99+2wQHB5vVq1ebvXv3moEDB5qIiAiTlpbm2ubkyZMmMTHR/OlPfzKSzLZt20xiYqLrrNmvvvrKzJgxwyQmJprDhw+blStXmsjISPPII4/cdoz79+83iYmJ5uGHHzadO3c2iYmJJjEx0W0b52WtW7c2gwYNMomJiWb//v3lYv7Lly833t7eZs6cOW5zuHDhwm33bXsdjDFmzJgxpkaNGuazzz4zP/74oxk2bJipXr26OX/+/E3Hd+HCBRMWFmYGDhxo9u7da1avXm2CgoLMu+++67ZdUR8HnrYWRXlMeMK8Z8+ebRISEsyhQ4fMoUOHzMKFC01QUJCZOHHiTffrietwo8K8e8UT5j9z5kyzZs0ac+jQIbNv3z7zyiuvGElm1apVBVoDT1mH9PR0U7NmTfPYY4+Z/fv3m61bt5p69eqZ4cOHF3gdCqJQ0SEp369Fixa5tsnJyTHx8fEmPDzc+Pr6mk6dOpm9e/e67Sc+Pv6W+9m1a5dp166dCQ4ONn5+fqZBgwYmPj7eZGRk3HaMUVFR+e77dvOIiooqF/OPjY3N9/ohQ4bcdt+218EYY65evWpefPFFU716dRMYGGgefPBBs2/fvtuOcc+ePeYXv/iF8fX1NeHh4Wby5Ml53iJY1MeBp61FUR4TnjDvWbNmmcaNG5tKlSqZoKAg07JlSzN37lyTnZ1923170jrcqDDR4Qnzf+edd8zPfvYz4+fnZ+655x5z//33m08//bRA8/ekdTDGmAMHDpgHH3zQ+Pv7m5o1a5rx48e7hVNx4FfbAwAAK/jdKwAAwAqiAwAAWEF0AAAAK4gOAABgBdEBAACsIDoAAIAVRAcAALCC6AAAAFYQHQAKbOjQoXI4HHI4HPLx8VFYWJi6deumhQsXun4DJgDcDNEBoFB69uypkydPKikpSevXr1eXLl00ZswY9e7dW1lZWSU9PAClGNEBoFB8fX0VHh6uGjVqqFWrVpowYYL++te/av369Vq8eLEkacaMGWratKkqV66sWrVqaeTIka7fXpmRkaGgoCB98sknbvv9n//5H1WuXFnp6em6evWq4uLiFBERIT8/P0VHR2vq1Km2pwqgmBEdAO7YAw88oObNm2v16tWSJC8vL82aNUv79u3Tn//8Z23atEkvv/yyJKly5coaMGCAFi1a5LaPRYsW6bHHHlNgYKBmzZqlhIQEffTRRzp48KCWLl16y19FDqBs8C7pAQDwDA0bNtSePXskSWPHjnVdXqdOHb3xxht6/vnnNXfuXEnS8OHD1aFDByUnJysyMlJnz57V2rVrtXHjRknSsWPHVK9ePd1///1yOByKioqyPh8AxY8jHQCKhTFGDodDkrR582Z169ZNNWrUUGBgoJ566imdO3dOGRkZkqT77rtPjRs31ocffihJWrJkiWrXrq1OnTpJun7C6u7du9WgQQONHj1aGzZsKJlJAShWRAeAYnHgwAHVqVNHR48e1UMPPaQmTZpo1apV2rVrl+bMmSNJunbtmmv74cOHu15iWbRokZ5++mlXtLRq1UpHjhzRG2+8ocuXL6t///567LHH7E8KQLEiOgDcsU2bNmnv3r361a9+pe+++05ZWVmaPn262rdvr/r16ys5OTnPbZ544gkdO3ZMs2bN0v79+zVkyBC364OCgvT444/rT3/6k1auXKlVq1bp/PnztqYE4C7gnA4AhZKZmamUlBRlZ2fr1KlT+tvf/qapU6eqd+/eeuqpp7R3715lZWXpD3/4gx5++GFt375d8+bNy7Ofe+65R/369dNvfvMbde/eXTVr1nRdN3PmTEVERKhFixby8vLSxx9/rPDwcFWpUsXiTAEUN450ACiUv/3tb4qIiFB0dLR69uypzZs3a9asWfrrX/+qChUqqEWLFpoxY4beeecdNWnSRMuWLbvp212HDRumq1ev6plnnnG7PCAgQO+8847atGmjtm3bKikpSevWrZOXF9+ygLLMYYwxJT0IAOXTsmXLNGbMGCUnJ6tixYolPRwAdxkvrwCw7tKlSzpy5IimTp2q5557juAAygmOVQKwbtq0aWrRooXCwsL06quvlvRwAFjCyysAAMAKjnQAAAAriA4AAGAF0QEAAKwgOgAAgBVEBwAAsILoAAAAVhAdAADACqIDAABYQXQAAAAr/h+iV9nEJDkJRwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sd = pd.DataFrame(df_raw, columns=[cols[3]])\n", "sd['ScheduledDay'] = pd.to_datetime(sd['ScheduledDay'])\n", "sns.boxplot(x=sd['ScheduledDay'].dt.date)\n", "plt.title('Scheduled Day')\n", "plt.xlabel(\"Days\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 24, "id": "e8e857b4290d6691", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:17.639104Z", "start_time": "2025-07-05T22:12:17.008973Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tm =  pd.DataFrame(df_raw, columns=[cols[3], cols[4]])\n", "tm['Scheduled_hour'] = pd.to_datetime(tm['ScheduledDay']).dt.hour\n", "\n", "sns.boxplot(x=tm['Scheduled_hour'])\n", "plt.title('Scheduled Hour')\n", "plt.xlabel(\"Hours\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "id": "8c1bf791aaa32be4", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:18.450850Z", "start_time": "2025-07-05T22:12:17.686535Z"}}, "outputs": [{"data": {"text/plain": ["Text(0.5, 0, 'Appointment Day')"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ad = pd.DataFrame(df_raw, columns=[cols[4]])\n", "ad['AppointmentDay'] = pd.to_datetime(ad['AppointmentDay'])\n", "sns.boxplot(x=ad['AppointmentDay'].dt.date)\n", "plt.title('Appointment Day')\n", "plt.xlabel(\"Appointment Day\")"]}, {"cell_type": "code", "execution_count": 26, "id": "ef3d9a5a30b9fbe9", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:12:18.788517Z", "start_time": "2025-07-05T22:12:18.496413Z"}}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Check any outliers in the dataset under each feature\n", "ag = pd.DataFrame(df_raw, columns=[cols[5]])\n", "ag.boxplot()"]}, {"cell_type": "markdown", "id": "c282ac43611a535a", "metadata": {}, "source": ["## Characteristics of the Dataset\n", "\n", "Column needs to be transformed to appropriate data types.\n", "- Data Quality: All columns are present and without any missing values.\n", "- The columns -> `Scholarship`,`Hypertension`,`Diabetes`,`Alcoholism`, `Alcoholism` can be identified as `boolean` and should be converted to bool type.\n", "- The `Gender` and `Handcap` columns should be converted to a categorical data type. The Handicap column is categorical; for instance, a value of 4 signifies that a patient has four distinct handicaps.\n", "- The `Scheduled Day` and `Appointment Day` column need to be transformed to datetime type. The <i>ScheduledDay</i> and <i>AppointmentDay</i> has `00:00:00:00` in its TimeStamp, we will ignore it.\n", "- `Neighbourhood` needs further investigation to identify the type of data it contains.\n", "- PatientId of type `float`, but they should be `int64`\n", "- Age has a value of -1 which is not valid. This should be corrected.\n", "- Typo corrections and standardisation:\n", "   - `Hipertension` should be corrected to `hypertension`.\n", "   - `Handcap` should be corrected to `handicap`.\n", "   - `SMS_received` should be corrected to `sms_Received`.\n", "   - `No-show` should be corrected to `no_show`.\n", "\n", "From the above information of the DataFrame and the sample data we can see that there are 14 columns in total.\n", "\n", "- There are 13 independent Variables -> [`PatientId`,`AppointmentID`,`Gender`,`ScheduleDay`,`AppointmentDay`,`Age`,`Neighbourhood`,`Scholarship`,`Hipertension`,`Diabetes`,'Alcoholism`,'Handcap`,`SMSReceived`]\n", "- The Dependent Variable is -> [`NoShow`]\n", "- The independent variables -> [`PatientId`,`AppointmentID`]\n", "- `AppointmentID` does not interesting in the context of our analysis, so let's dropped during data cleaning phase.\n", "- Categorical field `Gener`, `Neighbourhood`, `No-show` should be encoded for modelling.\n", "- There are 81 places, some of them with large number of appointments, it makes sense to categorise it"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}}, "nbformat": 4, "nbformat_minor": 5}