activeContentFilterList=*.makefile,makefile,*.<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Makefile.*,*.mk,MANIFEST.MF,.project,*.yml
addNewLine=true
convertActionOnSaave=AnyEdit.CnvrtTabToSpaces
eclipse.preferences.version=1
fixLineDelimiters=false
ignoreBlankLinesWhenTrimming=false
inActiveContentFilterList=
javaTabWidthForJava=true
org.eclipse.jdt.ui.editor.tab.width=2
projectPropsEnabled=false
removeTrailingSpaces=true
replaceAllSpaces=false
replaceAllTabs=false
saveAndAddLine=false
saveAndConvert=false
saveAndFixLineDelimiters=false
saveAndTrim=false
useModulo4Tabs=false
