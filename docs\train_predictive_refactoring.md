# Train Predictive Model Refactoring

## Overview

This document describes the comprehensive refactoring of `train_predictive.py` to improve code quality, maintainability, and functionality.

## Problems Addressed

### Original Issues
1. **Limited model support**: Only Random Forest was supported
2. **Poor error handling**: Minimal validation and error recovery
3. **Inconsistent imports**: Mixed import styles and path handling
4. **Hardcoded configurations**: Fixed parameters and limited flexibility
5. **Basic evaluation**: Limited metrics and no model comparison
6. **Poor code organization**: Mixed concerns and unclear structure
7. **No type hints**: Reduced code clarity and IDE support
8. **Limited testing**: No comprehensive test coverage

## Refactoring Improvements

### 1. Enhanced Model Support
**Before:**
```python
if model_type != 'random_forest':
    raise ValueError(f"Model type '{model_type}' is not supported.")
```

**After:**
```python
SUPPORTED_MODELS = {
    'random_forest': {
        'class': RandomForestClassifier,
        'default_params': {...}
    },
    'logistic_regression': {
        'class': LogisticRegression,
        'default_params': {...}
    },
    'gradient_boosting': {...},
    'svm': {...}
}
```

### 2. Improved Type Hints and Documentation
**Before:**
```python
def preprocess_data(self):
    """Comprehensive data preprocessing with feature selection and encoding."""
```

**After:**
```python
def preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series]:
    """
    Comprehensive data preprocessing with feature selection and encoding.
    
    Returns:
        Tuple of (features_dataframe, target_series)
    """
```

### 3. Enhanced Error Handling and Validation
**Before:**
```python
if target_column not in data.columns:
    raise ValueError(f"Target column '{target_column}' not found in data")
```

**After:**
```python
def _validate_inputs(self, data: pd.DataFrame, target_column: str, test_size: float):
    """Validate initialization inputs."""
    if not isinstance(data, pd.DataFrame):
        raise ValueError("Data must be a pandas DataFrame")
    if data.empty:
        raise ValueError("Data cannot be empty")
    if target_column not in data.columns:
        raise ValueError(f"Target column '{target_column}' not found in data")
    if not 0.0 < test_size < 1.0:
        raise ValueError("test_size must be between 0.0 and 1.0")
    # Additional validations...
```

### 4. Comprehensive Metrics and Evaluation
**Before:**
```python
metrics = utils.evaluate_model(y_test, y_pred, y_proba)
```

**After:**
```python
def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray, y_proba: Optional[np.ndarray] = None) -> Dict[str, float]:
    """Calculate comprehensive evaluation metrics."""
    metrics = {
        'accuracy': accuracy_score(y_true, y_pred),
        'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
        'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
        'f1_score': f1_score(y_true, y_pred, average='weighted', zero_division=0)
    }
    if y_proba is not None:
        try:
            metrics['roc_auc'] = roc_auc_score(y_true, y_proba)
        except ValueError as e:
            self.logger.warning(f"Could not calculate ROC AUC: {e}")
    return metrics
```

### 5. Better Model Persistence
**Before:**
```python
app_base_path = app_settings.get("app_base_path")
model_path = os.path.join(app_base_path, "models/predictive_model.pkl")
utils.save_model(self.model, model_path)
```

**After:**
```python
def _save_model_with_metadata(self):
    """Save model with comprehensive metadata."""
    model_metadata = {
        'model_name': self.model_name,
        'model_type': type(self.model).__name__,
        'best_params': self.best_params,
        'cv_score': self.cv_score,
        'test_metrics': self.test_metrics,
        'feature_names': self.feature_names,
        'target_column': self.target_column,
        # ... more metadata
    }
    model_path = config.app_settings['app_predictive_model_path']
    save_model(self.model, model_path, model_metadata)
```

## New Features Added

### 1. Multiple Model Types Support
- **Random Forest**: Tree-based ensemble method
- **Logistic Regression**: Linear classification with regularization
- **Gradient Boosting**: Advanced boosting algorithm
- **SVM**: Support Vector Machine with probability support

### 2. Advanced Preprocessing Pipeline
- **Categorical encoding**: Automatic detection and encoding
- **Missing value handling**: Robust imputation strategy
- **Feature validation**: Ensures consistency between training and prediction
- **Data quality checks**: Warns about potential issues

### 3. Comprehensive Model Evaluation
- **Multiple metrics**: Accuracy, Precision, Recall, F1-Score, ROC-AUC
- **Feature importance**: For tree-based models
- **Coefficient analysis**: For linear models
- **Confusion matrix**: Detailed classification results
- **Cross-validation scores**: Model reliability assessment

### 4. Robust Prediction Pipeline
- **Consistent preprocessing**: Same pipeline for training and prediction
- **Feature alignment**: Handles missing/extra features in new data
- **Probability predictions**: When supported by the model
- **Error handling**: Graceful failure with informative messages

### 5. Enhanced Configuration Management
- **Flexible parameters**: Customizable hyperparameter grids
- **Model-specific settings**: Tailored configurations per model type
- **Scoring options**: Multiple evaluation metrics for optimization
- **Cross-validation control**: Configurable fold numbers

## Testing and Validation

### Test Coverage
The refactored code includes comprehensive tests covering:

1. **Basic Functionality**
   - Initialization and validation
   - Data preprocessing
   - Train/test splitting

2. **Model Training**
   - Multiple model types
   - Hyperparameter tuning
   - Performance evaluation

3. **Prediction Capabilities**
   - Standard predictions
   - Probability predictions
   - New data preprocessing

4. **Error Handling**
   - Invalid inputs
   - Edge cases
   - Graceful failures

### Test Results
```
✓ Basic functionality: PASSED
✓ Model training: 2/2 models successful
✓ Prediction accuracy: 0.560
✓ Error handling: PASSED
```

## Performance Improvements

### 1. Efficiency Gains
- **Parallel processing**: Grid search with `n_jobs=-1`
- **Optimized cross-validation**: Configurable fold numbers
- **Memory management**: Efficient data copying and processing
- **Warning suppression**: Cleaner output during training

### 2. Scalability Enhancements
- **Modular design**: Easy to add new model types
- **Configurable parameters**: Adaptable to different datasets
- **Flexible preprocessing**: Handles various data types
- **Extensible evaluation**: Easy to add new metrics

## Usage Examples

### Basic Usage
```python
from models.train_predictive import PredictiveModelTrainer

# Load data
data = load_app_feature_data()

# Initialize trainer
trainer = PredictiveModelTrainer(
    data=data,
    target_column='no_show',
    model_name='appointment_predictor'
)

# Train model
model = trainer.train(model_type='random_forest')

# Get summary
summary = trainer.get_model_summary()
print(f"Model accuracy: {summary['test_metrics']['accuracy']}")
```

### Advanced Usage
```python
# Custom hyperparameters
custom_params = {
    'n_estimators': [100, 200, 500],
    'max_depth': [10, 20, None],
    'min_samples_split': [2, 5, 10]
}

# Train with custom configuration
model = trainer.train(
    model_type='random_forest',
    custom_params=custom_params,
    scoring='f1_weighted',
    cv_folds=10
)

# Make predictions on new data
predictions = trainer.predict(new_data)
probabilities = trainer.predict_proba(new_data)
```

### Model Comparison
```python
models_to_compare = ['random_forest', 'logistic_regression', 'gradient_boosting']
results = []

for model_type in models_to_compare:
    trainer.train(model_type=model_type)
    summary = trainer.get_model_summary()
    results.append({
        'model': model_type,
        'accuracy': summary['test_metrics']['accuracy'],
        'cv_score': summary['cv_score']
    })

# Compare results
best_model = max(results, key=lambda x: x['accuracy'])
print(f"Best model: {best_model['model']} with accuracy: {best_model['accuracy']}")
```

## Migration Guide

### For Existing Code
1. **Update imports**: Use the new module structure
2. **Update initialization**: Add new optional parameters
3. **Update training calls**: Use new parameter names
4. **Update evaluation**: Use new metrics structure

### Breaking Changes
- `utils.evaluate_model()` replaced with `_calculate_metrics()`
- Model saving now requires metadata parameter
- Some parameter names changed for consistency

## Future Enhancements

### Planned Improvements
1. **Deep learning support**: Add neural network models
2. **Automated feature selection**: Built-in feature importance filtering
3. **Model ensemble**: Combine multiple models for better performance
4. **Hyperparameter optimization**: Advanced optimization algorithms
5. **Model interpretability**: SHAP values and LIME explanations

### Extension Points
- **Custom models**: Easy integration of new algorithms
- **Custom metrics**: Pluggable evaluation functions
- **Custom preprocessing**: Extensible data transformation pipeline
- **Custom validation**: Flexible cross-validation strategies

## Conclusion

The refactored `train_predictive.py` provides a robust, flexible, and maintainable foundation for predictive modeling. The improvements enhance code quality, extend functionality, and provide better user experience while maintaining backward compatibility where possible.
