import contextlib
import sys, os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer

from econml.dml import CausalForestDML, LinearDML
from econml.metalearners import <PERSON>Learner, SLearner, XLearner

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.utils import save_model, load_model
from data import load_data as ld
from globals.basic_settings import config

class CausalModelTrainer:
    """
    A class for training causal inference models to estimate treatment effects.
    """

    def __init__(self, data: pd.DataFrame, treatment_column: str, outcome_column: str,
                 test_size: float = 0.2, random_state: int = 42, model_name: str = "causal_model"):
        self.data = data.copy(deep=True)
        self.treatment_column = treatment_column
        self.outcome_column = outcome_column
        self.test_size = test_size
        self.random_state = random_state
        self.model_name = model_name

        self.causal_model = None
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_names = None
        self.model_type = None

        self.ate = None
        self.ate_std = None
        self.treatment_effects_test = None

        self._validate_inputs()

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _validate_inputs(self):
        if self.treatment_column not in self.data.columns:
            raise ValueError(f"Treatment column '{self.treatment_column}' not found in data")
        if self.outcome_column not in self.data.columns:
            raise ValueError(f"Outcome column '{self.outcome_column}' not found in data")

    def preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series]:
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or
               col in ['patient_id', 'appointment_id', self.treatment_column, self.outcome_column]
        ]
        X = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
        T = self.data[self.treatment_column]
        Y = self.data[self.outcome_column]

        X_encoded = pd.get_dummies(X, drop_first=True)
        self.feature_names = X_encoded.columns.tolist()

        return X_encoded, T, Y

    def split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series, pd.Series, pd.Series]:
        X, T, Y = self.preprocess_data()
        X_train, X_test, T_train, T_test, Y_train, Y_test = train_test_split(
            X, T, Y, test_size=self.test_size, random_state=self.random_state, stratify=T
        )
        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )
        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test),
            columns=X_test.columns,
            index=X_test.index
        )
        return X_train_imputed, X_test_imputed, T_train, T_test, Y_train, Y_test

    def train(self, model_type: str = 'causal_forest',
              outcome_model: str = 'random_forest',
              treatment_model: str = 'random_forest',
              custom_params: Optional[Dict] = None) -> Any:
        self.model_type = model_type
        X_train, X_test, T_train, T_test, Y_train, Y_test = self.split_data()

        # Always use regression for outcome model in causal inference
        outcome_task = 'regression'
        # Check treatment variable
        unique_treatments = set(T_train.unique())
        self.logger.info(f"Unique treatments in training data: {unique_treatments}")
        
        # Use classification for binary treatment, regression otherwise
        if unique_treatments == {0, 1}:
            treatment_task = 'classification'
            treatment_model_name = treatment_model
        else:
            treatment_task = 'regression'
            # Use a regression for treatment model if not binary
            treatment_model_name = 'random_forest'

        outcome_est = self._get_base_model(outcome_model, task=outcome_task)
        treatment_est = self._get_base_model(treatment_model_name, task=treatment_task)

        if model_type == 'causal_forest':
            self.causal_model = CausalForestDML(
                model_y=outcome_est,
                model_t=treatment_est,
                random_state=self.random_state,
                **(custom_params or {})
            )
        elif model_type == 'linear_dml':
            self.causal_model = LinearDML(
                model_y=outcome_est,
                model_t=treatment_est,
                random_state=self.random_state,
                **(custom_params or {})
            )
        else:
            raise ValueError(f"Model type '{model_type}' is not supported.")

        self.causal_model.fit(Y_train, T_train, X=X_train)
        self._evaluate_model(X_test, T_test, Y_test)

        # Save the model
        model_path = config.app_settings['app_causal_model_path']
        save_model(self.causal_model, model_path, self.model_name)

        self.logger.info(f"Model '{self.model_name}' trained and saved at '{model_path}'.")
        return self.causal_model

    def _get_base_model(self, model_name: str, task: str):
        model_mapping = {
            'classification': {
                'logistic_regression': LogisticRegression,
                'random_forest': RandomForestClassifier
            },
            'regression': {
                'random_forest': RandomForestRegressor,
                'linear': LinearRegression
            }
        }
        try:
            return model_mapping[task][model_name](random_state=self.random_state)
        except KeyError as e:
            raise ValueError(
                f"Model '{model_name}' for task '{task}' is not supported."
            ) from e

    def _evaluate_model(self, X_test: pd.DataFrame, T_test: pd.Series, Y_test: pd.Series):
        treatment_effects = self.causal_model.effect(X_test)
        self.ate = np.mean(treatment_effects)
        self.ate_std = np.std(treatment_effects)
        self.logger.info(f"ATE: {self.ate}, ATE Std: {self.ate_std}")

    def get_model_summary(self) -> Dict[str, Any]:
        return {
            'model_type': self.model_type,
            'ate': self.ate,
            'ate_std': self.ate_std
        }

def main():
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        _extracted_from_main(logger)
    except Exception as e:
        logger.error(f"An error occurred: {e}", exc_info=True)


def _extracted_from_main(logger):
    data = ld.load_app_feature_data()
    trainer = CausalModelTrainer(
        data=data,
        treatment_column='sms_sent',
        outcome_column='no_show',
        model_name='sms_causal_model'
    )
    models_to_train = [
        ('causal_forest', 'random_forest', 'logistic_regression'),
        ('linear_dml', 'linear', 'logistic_regression')
    ]
    results = []
    for model_type, outcome_model, treatment_model in models_to_train:
        trainer.train(model_type=model_type, outcome_model=outcome_model, treatment_model=treatment_model)
        results.append(trainer.get_model_summary())
    logger.info(f"Results: {results}")

if __name__ == "__main__":
    main()