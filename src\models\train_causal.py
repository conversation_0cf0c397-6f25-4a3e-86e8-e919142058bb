import contextlib
import sys, os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LogisticRegression, LinearRegression, Ridge, Lasso
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error, r2_score

from econml.dml import CausalForestDML, LinearDML
from econml.metalearners import TLearner, SLearner, XLearner

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.utils import save_model, load_model
from data import load_data as ld
from globals.basic_settings import config

class CausalModelTrainer:
    """
    A class for training causal inference models to estimate treatment effects.
    """

    def __init__(self, data: pd.DataFrame, treatment_column: str, outcome_column: str,
                 test_size: float = 0.2, random_state: int = 42, model_name: str = "causal_model"):
        self.data = data.copy(deep=True)
        self.treatment_column = treatment_column
        self.outcome_column = outcome_column
        self.test_size = test_size
        self.random_state = random_state
        self.model_name = model_name

        self.causal_model = None
        self.scaler = StandardScaler()
        self.imputer = SimpleImputer(strategy='mean')
        self.feature_names = None
        self.model_type = None

        self.ate = None
        self.ate_std = None
        self.treatment_effects_test = None
        self.model_reliability = None
        self.first_stage_scores = None

        self._validate_inputs()

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def _validate_inputs(self):
        if self.treatment_column not in self.data.columns:
            raise ValueError(f"Treatment column '{self.treatment_column}' not found in data")
        if self.outcome_column not in self.data.columns:
            raise ValueError(f"Outcome column '{self.outcome_column}' not found in data")

    def preprocess_data(self) -> Tuple[pd.DataFrame, pd.Series, pd.Series]:
        exclude_columns = [
            col for col in self.data.columns
            if col.startswith('neighbourhood_') or
               col in ['patient_id', 'appointment_id', self.treatment_column, self.outcome_column]
        ]
        X = self.data.drop(columns=[col for col in exclude_columns if col in self.data.columns])
        T = self.data[self.treatment_column]
        Y = self.data[self.outcome_column]

        X_encoded = pd.get_dummies(X, drop_first=True)
        self.feature_names = X_encoded.columns.tolist()

        return X_encoded, T, Y

    def split_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series, pd.Series, pd.Series]:
        X, T, Y = self.preprocess_data()
        X_train, X_test, T_train, T_test, Y_train, Y_test = train_test_split(
            X, T, Y, test_size=self.test_size, random_state=self.random_state, stratify=T
        )
        X_train_imputed = pd.DataFrame(
            self.imputer.fit_transform(X_train),
            columns=X_train.columns,
            index=X_train.index
        )
        X_test_imputed = pd.DataFrame(
            self.imputer.transform(X_test),
            columns=X_test.columns,
            index=X_test.index
        )
        return X_train_imputed, X_test_imputed, T_train, T_test, Y_train, Y_test

    def train(self, model_type: str = 'causal_forest',
              outcome_model: str = 'random_forest',
              treatment_model: str = 'random_forest',
              custom_params: Optional[Dict] = None) -> Any:
        self.model_type = model_type
        X_train, X_test, T_train, T_test, Y_train, Y_test = self.split_data()

        # Always use regression for both outcome and treatment models in causal inference
        # EconML requires regression models even for binary variables
        outcome_task = 'regression'
        treatment_task = 'regression'

        # Check treatment variable for logging
        unique_treatments = set(T_train.unique())
        self.logger.info(f"Unique treatments in training data: {unique_treatments}")

        # Map model names to ensure we use regression models
        outcome_model_name = self._map_to_regression_model(outcome_model)
        treatment_model_name = self._map_to_regression_model(treatment_model)

        self.logger.info(f"Using outcome model: {outcome_model_name} (regression)")
        self.logger.info(f"Using treatment model: {treatment_model_name} (regression)")

        outcome_est = self._get_base_model(outcome_model_name, task=outcome_task)
        treatment_est = self._get_base_model(treatment_model_name, task=treatment_task)

        if model_type == 'causal_forest':
            self.causal_model = CausalForestDML(
                model_y=outcome_est,
                model_t=treatment_est,
                random_state=self.random_state,
                verbose=True,
                **(custom_params or {})
            )
        elif model_type == 'linear_dml':
            # Capture warnings during LinearDML training
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                self.causal_model = LinearDML(
                    model_y=outcome_est,
                    model_t=treatment_est,
                    random_state=self.random_state,
                    verbose=True,
                    **(custom_params or {})
                )

                # Log any warnings during model initialization
                if w:
                    for warning in w:
                        self.logger.warning(f"LinearDML initialization warning: {warning.message}")
        else:
            raise ValueError(f"Model type '{model_type}' is not supported.")

        # Fit the model with warning capture
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            self.causal_model.fit(Y_train, T_train, X=X_train)

            # Log any warnings during fitting
            if w:
                for warning in w:
                    self.logger.warning(f"Model fitting warning: {warning.message}")
                    if "underdetermined" in str(warning.message).lower():
                        self.logger.warning("This indicates potential numerical instability. Consider:")
                        self.logger.warning("  1. Using regularized models (Ridge/Lasso)")
                        self.logger.warning("  2. Reducing feature dimensionality")
                        self.logger.warning("  3. Using Causal Forest instead")
        self._evaluate_model(X_test, T_test, Y_test)

        # Save the model
        model_path = config.data_paths.model_path
        metadata = {
            'model_name': self.model_name,
            'model_type': self.model_type,
            'treatment_column': self.treatment_column,
            'outcome_column': self.outcome_column,
            'ate': self.ate,
            'ate_std': self.ate_std
        }
        save_model(self.causal_model, model_path, metadata)

        self.logger.info(f"Model '{self.model_name}' trained and saved at '{model_path}'.")
        return self.causal_model

    def _map_to_regression_model(self, model_name: str) -> str:
        """
        Map model names to their regression equivalents for causal inference.
        EconML requires regression models even for binary variables.
        For Linear DML, use regularized models to improve stability.
        """
        model_mapping = {
            'logistic_regression': 'ridge',  # Use Ridge instead of linear for stability
            'random_forest': 'random_forest',  # Random forest regressor
            'linear': 'ridge',  # Use Ridge instead of linear for stability
            'ridge': 'ridge',  # Ridge regression for regularization
            'lasso': 'lasso'   # Lasso regression for feature selection
        }

        if model_name not in model_mapping:
            self.logger.warning(f"Unknown model '{model_name}', defaulting to 'random_forest'")
            return 'random_forest'

        return model_mapping[model_name]

    def _get_base_model(self, model_name: str, task: str):
        model_mapping = {
            'classification': {
                'logistic_regression': LogisticRegression,
                'random_forest': RandomForestClassifier
            },
            'regression': {
                'random_forest': RandomForestRegressor,
                'linear': LinearRegression,
                'ridge': Ridge,
                'lasso': Lasso
            }
        }
        try:
            model_class = model_mapping[task][model_name]
            # Models that don't accept random_state parameter
            if model_class in [LinearRegression, Ridge, Lasso]:
                if model_class == Ridge:
                    return model_class(alpha=1.0)  # Default regularization
                elif model_class == Lasso:
                    return model_class(alpha=0.1)  # Lighter regularization for Lasso
                else:
                    return model_class()
            else:
                return model_class(random_state=self.random_state)
        except KeyError as e:
            raise ValueError(
                f"Model '{model_name}' for task '{task}' is not supported."
            ) from e

    def _evaluate_model(self, X_test: pd.DataFrame, T_test: pd.Series, Y_test: pd.Series):
        """
        Evaluate the causal model and check for reliability issues.
        """
        # Calculate treatment effects
        treatment_effects = self.causal_model.effect(X_test)
        self.ate = np.mean(treatment_effects)
        self.ate_std = np.std(treatment_effects)

        # Check for reliability issues
        self.model_reliability = self._check_model_reliability(treatment_effects, T_test, Y_test)

        # Evaluate first-stage models if available
        self.first_stage_scores = self._evaluate_first_stage_models(X_test, T_test, Y_test)

        # Log results with reliability warnings
        self._log_evaluation_results()

    def _check_model_reliability(self, treatment_effects: np.ndarray, T_test: pd.Series, Y_test: pd.Series) -> Dict[str, Any]:
        """
        Check for various reliability issues in the causal model.
        """
        reliability = {
            'is_reliable': True,
            'warnings': [],
            'metrics': {}
        }

        # Check for extreme values
        ate_magnitude = abs(self.ate)
        std_magnitude = abs(self.ate_std)

        if ate_magnitude > 1e6:
            reliability['is_reliable'] = False
            reliability['warnings'].append(f"Extremely large ATE magnitude: {ate_magnitude:.2e}")

        if std_magnitude > 1e6:
            reliability['is_reliable'] = False
            reliability['warnings'].append(f"Extremely large ATE standard deviation: {std_magnitude:.2e}")

        # Check for numerical instability
        if np.any(np.isnan(treatment_effects)) or np.any(np.isinf(treatment_effects)):
            reliability['is_reliable'] = False
            reliability['warnings'].append("Treatment effects contain NaN or infinite values")

        # Check coefficient of variation
        if self.ate != 0:
            cv = abs(self.ate_std / self.ate)
            reliability['metrics']['coefficient_of_variation'] = cv
            if cv > 10:  # Very high uncertainty relative to effect size
                reliability['warnings'].append(f"High coefficient of variation: {cv:.2f}")

        # Check effect size distribution
        effect_range = np.max(treatment_effects) - np.min(treatment_effects)
        reliability['metrics']['effect_range'] = effect_range
        reliability['metrics']['effect_iqr'] = np.percentile(treatment_effects, 75) - np.percentile(treatment_effects, 25)

        if effect_range > 1e6:
            reliability['warnings'].append(f"Extremely wide effect range: {effect_range:.2e}")

        return reliability

    def _evaluate_first_stage_models(self, X_test: pd.DataFrame, T_test: pd.Series, Y_test: pd.Series) -> Dict[str, Any]:
        """
        Evaluate the performance of first-stage models (outcome and treatment prediction).
        """
        scores = {}

        try:
            # Get first-stage models if available
            if hasattr(self.causal_model, 'models_y') and self.causal_model.models_y:
                # Outcome model evaluation
                y_pred = self.causal_model.models_y[0].predict(X_test)
                scores['outcome_r2'] = r2_score(Y_test, y_pred)
                scores['outcome_mse'] = mean_squared_error(Y_test, y_pred)

            if hasattr(self.causal_model, 'models_t') and self.causal_model.models_t:
                # Treatment model evaluation
                t_pred = self.causal_model.models_t[0].predict(X_test)
                scores['treatment_r2'] = r2_score(T_test, t_pred)
                scores['treatment_mse'] = mean_squared_error(T_test, t_pred)

        except Exception as e:
            scores['error'] = f"Could not evaluate first-stage models: {str(e)}"

        return scores

    def _log_evaluation_results(self):
        """
        Log evaluation results with appropriate warnings for reliability issues.
        """
        self.logger.info(f"ATE: {self.ate}, ATE Std: {self.ate_std}")

        if self.model_reliability and not self.model_reliability['is_reliable']:
            self.logger.warning("MODEL RELIABILITY ISSUES DETECTED:")
            for warning in self.model_reliability['warnings']:
                self.logger.warning(f"  - {warning}")
            self.logger.warning("Consider using a different model or regularization.")

        if self.first_stage_scores:
            self.logger.info("First-stage model performance:")
            for metric, value in self.first_stage_scores.items():
                if isinstance(value, float):
                    self.logger.info(f"  {metric}: {value:.4f}")
                else:
                    self.logger.info(f"  {metric}: {value}")

    def get_model_summary(self) -> Dict[str, Any]:
        return {
            'model_type': self.model_type,
            'ate': self.ate,
            'ate_std': self.ate_std,
            'reliability': self.model_reliability,
            'first_stage_scores': self.first_stage_scores
        }

def main():
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    try:
        _extracted_from_main(logger)
    except Exception as e:
        logger.error(f"An error occurred: {e}", exc_info=True)


def _extracted_from_main(logger):
    data = ld.load_app_feature_data()
    trainer = CausalModelTrainer(
        data=data,
        treatment_column='sms_sent',
        outcome_column='no_show',
        model_name='sms_causal_model'
    )

    # Updated model configurations with better stability for Linear DML
    models_to_train = [
        ('causal_forest', 'random_forest', 'random_forest'),  # Robust baseline
        ('linear_dml', 'ridge', 'ridge'),  # Regularized linear models for stability
        ('linear_dml', 'random_forest', 'ridge'),  # Mixed approach
    ]

    results = []
    for model_type, outcome_model, treatment_model in models_to_train:
        logger.info(f"\n{'='*60}")
        logger.info(f"Training {model_type} with outcome_model={outcome_model}, treatment_model={treatment_model}")
        logger.info(f"{'='*60}")

        try:
            trainer.train(model_type=model_type, outcome_model=outcome_model, treatment_model=treatment_model)
            results.append(trainer.get_model_summary())
        except Exception as e:
            logger.error(f"Failed to train {model_type}: {e}")
            results.append({
                'model_type': model_type,
                'error': str(e),
                'ate': None,
                'ate_std': None,
                'reliability': {'is_reliable': False, 'warnings': [f"Training failed: {e}"]}
            })

    # Summary of all results
    logger.info(f"\n{'='*60}")
    logger.info("FINAL RESULTS SUMMARY")
    logger.info(f"{'='*60}")

    for i, result in enumerate(results):
        logger.info(f"\nModel {i+1}: {result['model_type']}")
        if result.get('error'):
            logger.error(f"  Error: {result['error']}")
        else:
            logger.info(f"  ATE: {result['ate']}")
            logger.info(f"  ATE Std: {result['ate_std']}")
            if result.get('reliability'):
                reliability = result['reliability']
                logger.info(f"  Reliable: {reliability.get('is_reliable', 'Unknown')}")
                if reliability.get('warnings'):
                    logger.warning(f"  Warnings: {len(reliability['warnings'])} issues detected")

    return results

if __name__ == "__main__":
    main()