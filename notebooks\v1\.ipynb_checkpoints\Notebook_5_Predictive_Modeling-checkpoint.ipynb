{"cells": [{"cell_type": "markdown", "id": "a06cf889925d266f", "metadata": {}, "source": ["# 5. Predictive Modeling"]}, {"cell_type": "code", "execution_count": 1, "id": "d885463da1cd2c48", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:50:05.383300Z", "start_time": "2025-07-05T22:50:05.377271Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "from imblearn.over_sampling import SMOTE\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 2, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:50:08.186480Z", "start_time": "2025-07-05T22:50:07.946102Z"}, "collapsed": true}, "outputs": [], "source": ["final_dt = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/data/final/medical_appointment_no_show_features.csv\")"]}, {"cell_type": "code", "execution_count": 3, "id": "203f19ecf5ae3099", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:50:09.699728Z", "start_time": "2025-07-05T22:50:09.642795Z"}}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "age", "rawType": "int64", "type": "integer"}, {"name": "scholarship", "rawType": "int64", "type": "integer"}, {"name": "hypertension", "rawType": "int64", "type": "integer"}, {"name": "diabetes", "rawType": "int64", "type": "integer"}, {"name": "alcoholism", "rawType": "int64", "type": "integer"}, {"name": "handicap", "rawType": "int64", "type": "integer"}, {"name": "sms_received", "rawType": "bool", "type": "boolean"}, {"name": "no_show", "rawType": "bool", "type": "boolean"}, {"name": "lead_time", "rawType": "int64", "type": "integer"}, {"name": "scheduled_hours", "rawType": "int64", "type": "integer"}, {"name": "is_weekend", "rawType": "int64", "type": "integer"}, {"name": "days_until_next_appointment", "rawType": "int64", "type": "integer"}, {"name": "previous_no_show", "rawType": "int64", "type": "integer"}, {"name": "total_appointments", "rawType": "int64", "type": "integer"}, {"name": "gender_M", "rawType": "bool", "type": "boolean"}, {"name": "age_group_19-35", "rawType": "bool", "type": "boolean"}, {"name": "age_group_36-60", "rawType": "bool", "type": "boolean"}, {"name": "age_group_51-65", "rawType": "bool", "type": "boolean"}, {"name": "age_group_66+", "rawType": "bool", "type": "boolean"}, {"name": "received_sms_Yes", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Monday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Saturday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Thursday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Tuesday", "rawType": "bool", "type": "boolean"}, {"name": "appointment_day_of_week_Wednesday", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CARATOÍRA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_CENTRO", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_ITARARÉ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM CAMBURI", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JARDIM DA PENHA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_JESUS DE NAZARETH", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_MARIA ORTIZ", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_Other", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_RESISTÊNCIA", "rawType": "bool", "type": "boolean"}, {"name": "neighbourhood_TABUAZEIRO", "rawType": "bool", "type": "boolean"}], "ref": "c993fa70-2c2d-4731-9e73-90d397d37f15", "rows": [["0", "76", "0", "1", "0", "0", "0", "False", "False", "2", "18", "0", "33", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["1", "23", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["2", "39", "0", "0", "0", "0", "0", "False", "True", "2", "16", "0", "20", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["3", "19", "0", "0", "0", "0", "0", "False", "False", "2", "17", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["4", "30", "0", "0", "0", "0", "0", "False", "False", "2", "16", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["5", "29", "0", "0", "0", "0", "0", "True", "True", "3", "8", "0", "0", "0", "1", "True", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["6", "22", "1", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["7", "28", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["8", "54", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["9", "15", "0", "0", "0", "0", "0", "True", "False", "3", "12", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["10", "50", "0", "0", "0", "0", "0", "False", "False", "1", "14", "0", "0", "0", "1", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["11", "40", "1", "0", "0", "0", "0", "False", "True", "1", "8", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["12", "30", "1", "0", "0", "0", "0", "True", "False", "3", "11", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["13", "30", "0", "0", "0", "0", "0", "False", "True", "2", "14", "0", "32", "0", "2", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["14", "4", "0", "0", "0", "0", "0", "False", "True", "2", "10", "0", "0", "0", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["15", "13", "0", "0", "0", "0", "0", "True", "True", "4", "8", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["16", "46", "0", "0", "0", "0", "0", "False", "False", "1", "8", "0", "31", "0", "2", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["17", "46", "0", "1", "0", "0", "0", "True", "False", "3", "9", "0", "0", "0", "1", "True", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["18", "4", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "6", "1", "6", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["19", "46", "0", "0", "0", "0", "0", "False", "False", "2", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["20", "12", "1", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["21", "38", "1", "0", "0", "0", "0", "True", "False", "4", "10", "0", "0", "0", "1", "False", "False", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["22", "85", "0", "1", "0", "0", "0", "True", "False", "9", "13", "0", "14", "0", "2", "True", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["23", "55", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "33", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True"], ["24", "71", "0", "0", "1", "0", "0", "False", "False", "1", "14", "0", "40", "0", "2", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["25", "50", "0", "0", "0", "0", "0", "False", "False", "1", "15", "0", "0", "0", "1", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["26", "78", "0", "1", "1", "0", "0", "False", "True", "1", "14", "0", "0", "0", "1", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["27", "31", "0", "0", "0", "0", "0", "False", "False", "1", "10", "0", "0", "0", "1", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["28", "58", "0", "1", "0", "1", "0", "True", "False", "4", "15", "0", "13", "0", "3", "True", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["29", "39", "0", "1", "1", "0", "0", "False", "False", "1", "15", "0", "32", "0", "7", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["30", "58", "0", "0", "0", "0", "0", "True", "True", "3", "10", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["31", "27", "0", "0", "0", "0", "0", "True", "True", "29", "12", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["32", "19", "0", "0", "0", "0", "0", "True", "True", "29", "7", "0", "0", "0", "1", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["33", "23", "1", "0", "0", "0", "0", "True", "True", "29", "15", "0", "38", "0", "2", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["34", "23", "1", "0", "0", "0", "0", "True", "True", "29", "7", "0", "6", "0", "5", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["35", "12", "0", "0", "0", "0", "0", "True", "True", "10", "7", "0", "0", "0", "1", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["36", "8", "1", "0", "0", "0", "0", "False", "False", "2", "7", "0", "10", "0", "7", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["37", "2", "0", "0", "0", "0", "0", "False", "True", "2", "7", "0", "0", "0", "1", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["38", "3", "1", "0", "0", "0", "0", "True", "False", "4", "15", "0", "0", "0", "1", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["39", "0", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["40", "0", "0", "0", "0", "0", "0", "True", "False", "4", "14", "0", "32", "0", "2", "True", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["41", "0", "0", "0", "0", "0", "0", "False", "False", "2", "9", "0", "0", "0", "1", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["42", "69", "0", "1", "0", "0", "0", "True", "False", "23", "8", "0", "32", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False", "False", "False"], ["43", "58", "0", "0", "0", "0", "0", "True", "False", "23", "14", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["44", "62", "0", "0", "0", "0", "0", "False", "False", "23", "13", "0", "0", "0", "1", "True", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["45", "30", "1", "0", "0", "0", "0", "True", "False", "23", "14", "0", "20", "0", "3", "False", "True", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False"], ["46", "68", "0", "1", "1", "0", "0", "True", "False", "23", "12", "0", "35", "0", "2", "False", "False", "False", "False", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["47", "64", "0", "0", "0", "0", "0", "True", "False", "11", "8", "0", "0", "0", "1", "False", "False", "False", "True", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False", "False"], ["48", "60", "0", "0", "0", "0", "0", "False", "False", "11", "7", "0", "26", "0", "2", "False", "False", "False", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"], ["49", "28", "0", "0", "0", "0", "0", "False", "True", "11", "17", "0", "0", "0", "1", "True", "True", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "False", "True", "False", "False"]], "shape": {"columns": 35, "rows": 71959}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>scheduled_hours</th>\n", "      <th>...</th>\n", "      <th>neighbourhood_CARATOÍRA</th>\n", "      <th>neighbourhood_CENTRO</th>\n", "      <th>neighbourhood_ITARARÉ</th>\n", "      <th>neighbourhood_JARDIM CAMBURI</th>\n", "      <th>neighbourhood_JARDIM DA PENHA</th>\n", "      <th>neighbourhood_JESUS DE NAZARETH</th>\n", "      <th>neighbourhood_MARIA ORTIZ</th>\n", "      <th>neighbourhood_Other</th>\n", "      <th>neighbourhood_RESISTÊNCIA</th>\n", "      <th>neighbourhood_TABUAZEIRO</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>18</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>17</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>13</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>51</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>16</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>21</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>38</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>54</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>7</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 35 columns</p>\n", "</div>"], "text/plain": ["       age  scholarship  hypertension  diabetes  alcoholism  handicap  \\\n", "0       76            0             1         0           0         0   \n", "1       23            0             0         0           0         0   \n", "2       39            0             0         0           0         0   \n", "3       19            0             0         0           0         0   \n", "4       30            0             0         0           0         0   \n", "...    ...          ...           ...       ...         ...       ...   \n", "71954   56            0             0         0           0         0   \n", "71955   51            0             0         0           0         0   \n", "71956   21            0             0         0           0         0   \n", "71957   38            0             0         0           0         0   \n", "71958   54            0             0         0           0         0   \n", "\n", "       sms_received  no_show  lead_time  scheduled_hours  ...  \\\n", "0             False    False          2               18  ...   \n", "1             False     True          2               16  ...   \n", "2             False     True          2               16  ...   \n", "3             False    False          2               17  ...   \n", "4             False    False          2               16  ...   \n", "...             ...      ...        ...              ...  ...   \n", "71954          True    False         35               13  ...   \n", "71955          True    False         35               16  ...   \n", "71956          True    False         41                7  ...   \n", "71957          True    False         41                7  ...   \n", "71958          True    False         41                7  ...   \n", "\n", "       neighbourhood_CARATOÍRA  neighbourhood_CENTRO  neighbourhood_ITARARÉ  \\\n", "0                        False                 False                  False   \n", "1                        False                 False                  False   \n", "2                        False                 False                  False   \n", "3                        False                 False                  False   \n", "4                        False                 False                  False   \n", "...                        ...                   ...                    ...   \n", "71954                    False                 False                  False   \n", "71955                    False                 False                  False   \n", "71956                    False                 False                  False   \n", "71957                    False                 False                  False   \n", "71958                    False                 False                  False   \n", "\n", "       neighbourhood_JARDIM CAMBURI  neighbourhood_JARDIM DA PENHA  \\\n", "0                             False                          False   \n", "1                             False                          False   \n", "2                             False                          False   \n", "3                             False                          False   \n", "4                             False                          False   \n", "...                             ...                            ...   \n", "71954                         False                          False   \n", "71955                         False                          False   \n", "71956                         False                          False   \n", "71957                         False                          False   \n", "71958                         False                          False   \n", "\n", "       neighbourhood_JESUS DE NAZARETH  neighbourhood_MARIA ORTIZ  \\\n", "0                                False                      False   \n", "1                                False                      False   \n", "2                                False                      False   \n", "3                                False                      False   \n", "4                                False                      False   \n", "...                                ...                        ...   \n", "71954                            False                       True   \n", "71955                            False                       True   \n", "71956                            False                       True   \n", "71957                            False                       True   \n", "71958                            False                       True   \n", "\n", "       neighbourhood_Other  neighbourhood_RESISTÊNCIA  \\\n", "0                     True                      False   \n", "1                     True                      False   \n", "2                     True                      False   \n", "3                     True                      False   \n", "4                     True                      False   \n", "...                    ...                        ...   \n", "71954                False                      False   \n", "71955                False                      False   \n", "71956                False                      False   \n", "71957                False                      False   \n", "71958                False                      False   \n", "\n", "       neighbourhood_TABUAZEIRO  \n", "0                         False  \n", "1                         False  \n", "2                         False  \n", "3                         False  \n", "4                         False  \n", "...                         ...  \n", "71954                     False  \n", "71955                     False  \n", "71956                     False  \n", "71957                     False  \n", "71958                     False  \n", "\n", "[71959 rows x 35 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["final_dt"]}, {"cell_type": "code", "execution_count": 4, "id": "92f89aa9cace03aa", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:50:22.965764Z", "start_time": "2025-07-05T22:50:22.936721Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 35 columns):\n", " #   Column                             Non-Null Count  Dtype\n", "---  ------                             --------------  -----\n", " 0   age                                71959 non-null  int64\n", " 1   scholarship                        71959 non-null  int64\n", " 2   hypertension                       71959 non-null  int64\n", " 3   diabetes                           71959 non-null  int64\n", " 4   alcoholism                         71959 non-null  int64\n", " 5   handicap                           71959 non-null  int64\n", " 6   sms_received                       71959 non-null  bool \n", " 7   no_show                            71959 non-null  bool \n", " 8   lead_time                          71959 non-null  int64\n", " 9   scheduled_hours                    71959 non-null  int64\n", " 10  is_weekend                         71959 non-null  int64\n", " 11  days_until_next_appointment        71959 non-null  int64\n", " 12  previous_no_show                   71959 non-null  int64\n", " 13  total_appointments                 71959 non-null  int64\n", " 14  gender_M                           71959 non-null  bool \n", " 15  age_group_19-35                    71959 non-null  bool \n", " 16  age_group_36-60                    71959 non-null  bool \n", " 17  age_group_51-65                    71959 non-null  bool \n", " 18  age_group_66+                      71959 non-null  bool \n", " 19  received_sms_Yes                   71959 non-null  bool \n", " 20  appointment_day_of_week_Monday     71959 non-null  bool \n", " 21  appointment_day_of_week_Saturday   71959 non-null  bool \n", " 22  appointment_day_of_week_Thursday   71959 non-null  bool \n", " 23  appointment_day_of_week_Tuesday    71959 non-null  bool \n", " 24  appointment_day_of_week_Wednesday  71959 non-null  bool \n", " 25  neighbourhood_CARATOÍRA            71959 non-null  bool \n", " 26  neighbourhood_CENTRO               71959 non-null  bool \n", " 27  neighbourhood_ITARARÉ              71959 non-null  bool \n", " 28  neighbourhood_JARDIM CAMBURI       71959 non-null  bool \n", " 29  neighbourhood_JARDIM DA PENHA      71959 non-null  bool \n", " 30  neighbourhood_JESUS DE NAZARETH    71959 non-null  bool \n", " 31  neighbourhood_MARIA ORTIZ          71959 non-null  bool \n", " 32  neighbourhood_Other                71959 non-null  bool \n", " 33  neighbourhood_RESISTÊNCIA          71959 non-null  bool \n", " 34  neighbourhood_TABUAZEIRO           71959 non-null  bool \n", "dtypes: bool(23), int64(12)\n", "memory usage: 8.2 MB\n"]}], "source": ["final_dt.info()"]}, {"cell_type": "markdown", "id": "c1ff9d1dbe1c56dd", "metadata": {}, "source": ["## 5.1 Split the Dataset"]}, {"cell_type": "code", "execution_count": 5, "id": "f2eaf90576f4fd55", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.562865500Z", "start_time": "2025-06-22T20:23:39.000660Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training set shape: (57567, 24)\n", "Test set shape: (14392, 24)\n"]}], "source": ["# Define features (x) and target (y)\n", "x_reduced = final_dt.drop([col for col in final_dt.columns if col.startswith('neighbourhood_') or col == 'no_show'], axis=1)\n", "y_reduced = final_dt['no_show']\n", "\n", "# Convert categorical variables to numerical using one-hot encoding\n", "X_reduced_encoded = pd.get_dummies(x_reduced, drop_first=True)\n", "\n", "# Impute missing values with the mean (or median/mode)\n", "impute = SimpleImputer(strategy='mean')\n", "X_reduced_imputed = pd.DataFrame(impute.fit_transform(X_reduced_encoded), columns=X_reduced_encoded.columns)\n", "\n", "# Split the dataset (80% train, 20% test)\n", "X_train_reduced, X_test_reduced, y_train_reduced, y_test_reduced = train_test_split(X_reduced_imputed, y_reduced, test_size=0.2, random_state=42, stratify=y_reduced)\n", "\n", "# Print the shapes of the resulting datasets\n", "print(f\"Training set shape: {X_train_reduced.shape}\")\n", "print(f\"Test set shape: {X_test_reduced.shape}\")"]}, {"cell_type": "markdown", "id": "16aede62226b6c17", "metadata": {}, "source": ["## 5.2 Train and Evaluate Models\n", "<p>\n", "  Train and evaluate three models\n", "    <ol>\n", "       <li>Logistic Regression</li>\n", "       <li>Random Forest</li>\n", "       <li>XGBoost</li>\n", "    </ol>\n", "</p>"]}, {"cell_type": "code", "execution_count": 6, "id": "3e5ff1c690322fe1", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.565974500Z", "start_time": "2025-06-22T20:23:39.357488Z"}}, "outputs": [], "source": ["from xgboost import XGBClassifier\n", "from collections import Counter\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, classification_report"]}, {"cell_type": "code", "execution_count": 7, "id": "e927863e596ab3e7", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.566981900Z", "start_time": "2025-06-22T20:23:39.503464Z"}}, "outputs": [], "source": ["# Function to handle class imbalance\n", "def handle_class_imbalance(X_train, y_train):\n", "    # Apply SMOTE to handle class imbalance\n", "    smote = SMOTE(random_state=RANDOM_STATE, sampling_strategy='auto', k_neighbors=5)\n", "    X_resampled, y_resampled = smote.fit_resample(X_train, y_train)\n", "\n", "    # Print the class distribution before and after SMOTE\n", "    print(\"Class Imbalance handling SMOTE\")\n", "    print(\"------------------------------\")\n", "    print(f\"Before SMOTE: {Counter(y_train)}\")\n", "    print(f\"After SMOTE: {Counter(y_resampled)}\")\n", "\n", "    return X_resampled, y_resampled\n", "\n", "# Function to evaluate model performance\n", "def evaluate_model(model, X_train_resampled, y_train_resampled, X_test, y_test):\n", "\n", "    # Train the model\n", "    model.fit(X_train_resampled, y_train_resampled)\n", "\n", "    # Make predictions\n", "    y_pred = model.predict(X_test)\n", "    y_prob = model.predict_proba(X_test)[:, 1]  # Get probabilities for the positive class\n", "\n", "    # Evaluate to model\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    precision = precision_score(y_test, y_pred)\n", "    recall = recall_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred)\n", "    roc_auc = roc_auc_score(y_test, y_prob)  # ROC AUC score\n", "\n", "    cm = confusion_matrix(y_test, y_pred)\n", "\n", "    graph_report = classification_report(y_test, y_pred, output_dict=True)\n", "    class_report = pd.DataFrame(graph_report).transpose()\n", "\n", "    # Print evaluation metrics\n", "    print(\"\\n\")\n", "    print(f\"Evaluation Metrics:\")\n", "    print(\"--------------------\")\n", "    print(f\"Accuracy: {accuracy:.4f}\")\n", "    print(f\"Precision: {precision:.4f}\")\n", "    print(f\"Recall: {recall:.4f}\")\n", "    print(f\"F1 Score: {f1:.4f}\")\n", "    print(f\"ROC AUC Score: {roc_auc:.4f}\")\n", "\n", "    # Plot confusion matrix\n", "    print(\"\\n\")\n", "    print(\"Evaluation:\")\n", "    print(\"-----------------\")\n", "    plt.figure(figsize=(8, 4))\n", "\n", "    plt.subplot(1, 2, 1)\n", "    sns.heatmap(class_report, annot=True, fmt='.2f', cmap='crest', cbar=False)\n", "    plt.title('Classification Report')\n", "    plt.xlabel('Metrics')\n", "    plt.ylabel('Classes')\n", "\n", "    plt.subplot(1, 2, 2)\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Greens', cbar=False)\n", "    plt.title('Confusion Matrix')\n", "    plt.xlabel('Predicted')\n", "    plt.ylabel('Actual')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    return model\n", "\n", "RANDOM_STATE = 42  # Random state for reproducibility\n"]}, {"cell_type": "code", "execution_count": 8, "id": "9aa43b08870b7caa", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.578592400Z", "start_time": "2025-06-22T20:23:39.577638Z"}}, "outputs": [], "source": ["# Initialize models\n", "logistic_regression_model = LogisticRegression(random_state=RANDOM_STATE, max_iter=10000)\n", "random_forest_classifier_model = RandomForestClassifier(n_estimators=200, random_state=RANDOM_STATE)\n", "XGBClassifier_model = XGBClassifier(n_estimators=200, random_state=RANDOM_STATE)"]}, {"cell_type": "markdown", "id": "9629cba221e97205", "metadata": {}, "source": ["### 5.2.1 Model Evaluation"]}, {"cell_type": "markdown", "id": "a585a9e890d68175", "metadata": {}, "source": ["#### i. Evaluate Logistic Regression"]}, {"cell_type": "code", "execution_count": 9, "id": "cfaa33bacef78484", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.581103900Z", "start_time": "2025-07-05T22:06:59.581959Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.5630\n", "Precision: 0.3497\n", "Recall: 0.6192\n", "F1 Score: 0.4469\n", "ROC AUC Score: 0.6115\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxYAAAGGCAYAAADmRxfNAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAkctJREFUeJzs3XVYFdkbB/Dvpbsb6RIUuwuwu9futbtdO9buWGN17e5Ya+0uJAQVFUWwQAWkmzu/P/h53SsgcUn3+3keHpkzZ2beOVzn3vfOOWdEgiAIICIiIiIikoFccQdARERERESlHxMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLoiz4+fmhf//+sLGxgYqKCjQ0NFClShUsXboUkZGRknru7u5wd3cvtjivXbsGkUiEa9euSZWvW7cO9vb2UFJSgkgkQlRUFPr16wdra+tCi+Xs2bOYM2dOluusra3Rr1+/Qjt2dr62z9cfeXl5GBoaok2bNnj48GGRx5MfP2pXIsost9fvwuDj4wM3Nzdoa2tDJBJh9erVBX4MkUhULNeEHTt2SK6l37/nAIAgCLC3t4dIJMr3++KGDRuwY8eOPG2T3fsgFQ+F4g6AqKTZsmULhg8fDicnJ0yaNAkuLi5ITU3Fw4cPsWnTJty9exfHjx8v7jABAFWqVMHdu3fh4uIiKfP19cXo0aMxcOBA9O3bFwoKCtDU1MTMmTMxZsyYQovl7NmzWL9+fZZveMePH4eWllahHTsnCxcuhIeHB1JTU+Hj44O5c+fCzc0Nvr6+cHBwKLa4cuNH7UpE0or7+j1gwADEx8fjwIED0NXVLZQvc+7evYsyZcoU+H5zS1NTE1u3bs2UPFy/fh2vXr2CpqZmvve9YcMGGBgY5OmLqKzeB6n4MLEg+pe7d+9i2LBhaNKkCU6cOAFlZWXJuiZNmmDChAk4f/58MUYoTUtLC7Vq1ZIqe/LkCQBg0KBBqFGjhqTczs6uSGP7t8qVKxfbsQHAwcFB0k7169eHjo4O+vbtiz179mDu3LnFGlt2EhISoKamVtxhEJUaJeH6/fjxYwwaNAgtWrQotGN8f80val27dsXevXuxfv16qS+Mtm7ditq1ayMmJqZI4khNTYVIJMryfZCKD7tCEf3LwoULIRKJsHnzZqk3pa+UlJTQtm3bH+5j7ty5qFmzJvT09KClpYUqVapg69atEARBqt6VK1fg7u4OfX19qKqqwtLSEp06dUJCQoKkzsaNG1GxYkVoaGhAU1MTZcuWxbRp0yTrv78F7O7ujl69egEAatasCZFIJPnmJ6uuUGKxGOvWrUOlSpWgqqoKHR0d1KpVC6dOnZLUOXjwIJo2bQpTU1OoqqrC2dkZv/32G+Lj4yV1+vXrh/Xr1wOAVNej4OBgAFl3hXrz5g169eoFIyMjKCsrw9nZGStWrIBYLJbUCQ4OhkgkwvLly7Fy5UrY2NhAQ0MDtWvXxr179374d/iRatWqAQA+fvwoVR4YGIgePXpIxfT1vL762uZ79uzB+PHjYWJiAlVVVbi5ucHHxyfTsU6dOoXatWtDTU0NmpqaaNKkCe7evStVZ86cORCJRPD29kbnzp2hq6sLOzu7HNuViL7J6/VbLBZj6dKlKFu2LJSVlWFkZIQ+ffrg3bt3Utu5u7ujfPny8PT0RP369aGmpgZbW1ssXrxYcr362k0oLS0NGzdulPxfBb79//7e123+/f85N+8LWXWFevz4Mdq1awddXV2oqKigUqVK2Llzp1Sdr9eu/fv3Y/r06TAzM4OWlhYaN26M58+f566RAXTv3h0AsH//fklZdHQ0jh49igEDBmS5TW7eF62trfHkyRNcv35d0n5f37O+xr57925MmDAB5ubmUFZWxsuXLzO9D4aHh8PCwgJ16tRBamqqZP9Pnz6Furo6evfunetzpbzjHQui/0tPT8eVK1dQtWpVWFhY5Hs/wcHBGDJkCCwtLQEA9+7dw6hRo/D+/XvMmjVLUqdVq1aoX78+tm3bBh0dHbx//x7nz59HSkoK1NTUcODAAQwfPhyjRo3C8uXLIScnh5cvX+Lp06fZHnvDhg3Yv38/5s+fj+3bt6Ns2bIwNDTMtn6/fv2wZ88e/Prrr5g3bx6UlJTg7e0t9UYXGBiIli1bYuzYsVBXV8ezZ8+wZMkSPHjwAFeuXAEAzJw5E/Hx8Thy5IjUh2ZTU9Msj/v582fUqVMHKSkp+P3332FtbY3Tp09j4sSJePXqFTZs2CBVf/369Shbtqykv/LMmTPRsmVLvH79Gtra2tn/MbLx+vVrAICjo6Ok7OnTp6hTpw4sLS2xYsUKmJiY4J9//sHo0aMRHh6O2bNnS+1j2rRpqFKlCv766y9ER0djzpw5cHd3h4+PD2xtbQEA+/btQ8+ePdG0aVPs378fycnJWLp0Kdzd3XH58mXUq1dPap8dO3ZEt27dMHToUMTHx6N8+fJ5alei/6r8XL+HDRuGzZs3Y+TIkWjdujWCg4Mxc+ZMXLt2Dd7e3jAwMJDUDQsLQ8+ePTFhwgTMnj0bx48fx9SpU2FmZoY+ffqgVatWuHv3LmrXro3OnTtjwoQJeT6H3LwvZOX58+eoU6cOjIyMsHbtWujr62PPnj3o168fPn78iMmTJ0vVnzZtGurWrYu//voLMTExmDJlCtq0aYOAgADIy8vnGKeWlhY6d+6Mbdu2YciQIQAykgw5OTl07do1y3EluXlfPH78ODp37gxtbW3Je8D3CeLUqVNRu3ZtbNq0CXJycjAyMkJYWJhUHQMDAxw4cADu7u6YMmUKVq5ciYSEBPzyyy+wtLTEpk2bcjxHkoFARIIgCEJYWJgAQOjWrVuut3FzcxPc3NyyXZ+eni6kpqYK8+bNE/T19QWxWCwIgiAcOXJEACD4+vpmu+3IkSMFHR2dHx7/6tWrAgDh6tWrkrLt27cLAARPT0+pun379hWsrKwkyzdu3BAACNOnT//hMf5NLBYLqampwvXr1wUAwqNHjyTrRowYIWR3SbGyshL69u0rWf7tt98EAML9+/el6g0bNkwQiUTC8+fPBUEQhNevXwsABFdXVyEtLU1S78GDBwIAYf/+/T+M92v7HDx4UEhNTRUSEhKE27dvC05OToKLi4vw5csXSd1mzZoJZcqUEaKjo6X2MXLkSEFFRUWIjIyU2meVKlUkf09BEITg4GBBUVFRGDhwoCAIGX97MzMzwdXVVUhPT5fUi42NFYyMjIQ6depIymbPni0AEGbNmpXpHH7UrkSUIa/X74CAAAGAMHz4cKny+/fvCwCEadOmScrc3NyyvF65uLgIzZo1kyoDIIwYMUKq7Ov/7+99vVa/fv1aEITcvS98Pcbs2bMly926dROUlZWFN2/eSNVr0aKFoKamJkRFRQmC8O3a1bJlS6l6hw4dEgAId+/e/eFx//3e8nVfjx8/FgRBEKpXry7069dPEARBKFeuXL7eF3+07dfjNWjQINt1/34fFARBWLJkiQBAOH78uNC3b19BVVVV8PPz++E5kuzYFYqogF25cgWNGzeGtrY25OXloaioiFmzZiEiIgKfPn0CAFSqVAlKSkoYPHgwdu7ciaCgoEz7qVGjBqKiotC9e3ecPHkS4eHhBRrnuXPnAAAjRoz4Yb2goCD06NEDJiYmkvNxc3MDAAQEBOTr2FeuXIGLi4vUGBAg4w6KIAiSOyFftWrVSuqbtAoVKgAAQkJCcnW8rl27QlFREWpqaqhbty5iYmJw5swZ6OjoAACSkpJw+fJldOjQAWpqakhLS5P8tGzZEklJSZm6XvXo0UOqe4OVlRXq1KmDq1evAsj4FvHDhw/o3bs35OS+XWo1NDTQqVMn3Lt3T6p7AwB06tQpV+dDRLL5+v/0+y6aNWrUgLOzMy5fvixVbmJikul6VaFChVxfg3IjN+8LWbly5QoaNWqU6U5Nv379kJCQkKnr5ffdefN6PQUANzc32NnZYdu2bfD394enp2e23aC+xpjT+2Ju5OUaOWnSJLRq1Qrdu3fHzp07sW7dOri6uuZ6e8ofJhZE/2dgYAA1NTVJN5n8ePDgAZo2bQogY3aS27dvw9PTE9OnTwcAJCYmAsgYSH3p0iUYGRlhxIgRsLOzg52dHdasWSPZV+/evbFt2zaEhISgU6dOMDIyQs2aNXHx4kUZzvKbz58/Q15eHiYmJtnWiYuLQ/369XH//n3Mnz8f165dg6enJ44dOyZ1PnkVERGRZXceMzMzyfp/09fXl1r+ens8t8dfsmQJPD09cf36dUyfPh0fP35E+/btkZycLDleWloa1q1bB0VFRamfli1bAkCmxC6rdjMxMZHE/vXf7M5TLBbjy5cvUuXs4kSUP3m9fuf0/zOnaxCQcR3K7zUwK7l5X8hKUV9PgYxxHv3798eePXuwadMmODo6on79+lnWze37Ym7k5Rr5dYxhUlISTExMOLaiiHCMBdH/ycvLo1GjRjh37hzevXuXr+n8Dhw4AEVFRZw+fRoqKiqS8hMnTmSqW79+fdSvXx/p6el4+PAh1q1bh7Fjx8LY2BjdunUDAPTv3x/9+/dHfHw8bty4gdmzZ6N169Z48eIFrKys8n2uAGBoaIj09HSEhYVle7G+cuUKPnz4gGvXrknuUgBAVFSUTMfW19dHaGhopvIPHz4AgFTf5oJga2srGbDdoEEDqKqqYsaMGVi3bh0mTpwIXV1dyMvLo3fv3tnewbGxsZFa/r5f79eyr2/aX//N7jzl5OSgq6srVZ7VAE8iyller9///v/5fd0PHz4U6DXo63tBcnKy1JiBrO5C5+Z94XtFfT39ql+/fpg1axY2bdqEBQsWZFsvL++LOcnLNTI0NBQjRoxApUqV8OTJE0ycOBFr167N8zEpb3jHguhfpk6dCkEQMGjQIKSkpGRan5qair///jvb7UUiERQUFKS67SQmJmL37t3ZbiMvL4+aNWtKZv/x9vbOVEddXR0tWrTA9OnTkZKSIplSVhZfp0PcuHFjtnW+XsS/H0D3559/Zqqbl2+9GjVqhKdPn2Y61127dkEkEsHDwyPHfchi8uTJsLe3x+LFixEbGws1NTV4eHjAx8cHFSpUQLVq1TL9fP8t3/79+6VmNAkJCcGdO3ckc7s7OTnB3Nwc+/btk6oXHx+Po0ePSmaKykl+vk0k+i/Ky/W7YcOGAIA9e/ZI1fH09ERAQAAaNWpUYHF9ndnIz89PqvxH7yW5eV/4qlGjRpIvgf5t165dUFNTK7SpWM3NzTFp0iS0adMGffv2zbZeXt4XC+ouUHp6Orp37w6RSIRz585h0aJFWLduneRuOxUe3rEg+pfatWtj48aNGD58OKpWrYphw4ahXLlykgerbd68GeXLl0ebNm2y3L5Vq1ZYuXIlevTogcGDByMiIgLLly/P9MF806ZNuHLlClq1agVLS0skJSVh27ZtAIDGjRsDyHgOhaqqKurWrQtTU1OEhYVh0aJF0NbWRvXq1WU+1/r166N3796YP38+Pn78iNatW0NZWRk+Pj5QU1PDqFGjUKdOHejq6mLo0KGYPXs2FBUVsXfvXjx69CjT/r72XV2yZAlatGgBeXl5VKhQAUpKSpnqjhs3Drt27UKrVq0wb948WFlZ4cyZM9iwYQOGDRsmNVtTYVBUVMTChQvRpUsXrFmzBjNmzMCaNWtQr1491K9fH8OGDYO1tTViY2Px8uVL/P3335nGfXz69AkdOnTAoEGDEB0djdmzZ0NFRQVTp04FAMjJyWHp0qXo2bMnWrdujSFDhiA5ORnLli1DVFQUFi9enKtY89KuRP9lebl+Ozk5YfDgwVi3bh3k5OTQokULyaxQFhYWGDduXIHF1bJlS+jp6Ulm31NQUMCOHTvw9u1bqXq5eV/IyuzZs3H69Gl4eHhg1qxZ0NPTw969e3HmzBksXbo0XzPn5VZurmO5fV8EMq53Bw4cwMGDB2FrawsVFZV8jYuYPXs2bt68iQsXLsDExAQTJkzA9evX8euvv6Jy5cqZ7kBTASrWoeNEJZSvr6/Qt29fwdLSUlBSUhLU1dWFypUrC7NmzRI+ffokqZfVrFDbtm0TnJycBGVlZcHW1lZYtGiRsHXrVqnZP+7evSt06NBBsLKyEpSVlQV9fX3Bzc1NOHXqlGQ/O3fuFDw8PARjY2NBSUlJMDMzE7p06SI1q4Uss0IJQsbsHKtWrRLKly8vKCkpCdra2kLt2rWFv//+W1Lnzp07Qu3atQU1NTXB0NBQGDhwoODt7S0AELZv3y6pl5ycLAwcOFAwNDQURCKR1Pl+PyuUIAhCSEiI0KNHD0FfX19QVFQUnJychGXLlknNoPR1Vqhly5Zl+hvhu5lRsvK1fQ4fPpzl+po1awq6urqSWVNev34tDBgwQDA3NxcUFRUFQ0NDoU6dOsL8+fMz7XP37t3C6NGjBUNDQ0FZWVmoX7++8PDhw0zHOHHihFCzZk1BRUVFUFdXFxo1aiTcvn1bqs7XWWM+f/6cafsftSsRZZbb63d6erqwZMkSwdHRUVBUVBQMDAyEXr16CW/fvpXan5ubm1CuXLlMx8nqmoosZoUShIyZ7OrUqSOoq6sL5ubmwuzZs4W//vorz+8LX4/x/bXP399faNOmjaCtrS0oKSkJFStWlLo+C0L218Ov19nv638vu/eW72U1s1Nu3hcFIWN2vaZNmwqampoCAEn7/uha/v374IULFwQ5OblMbRQRESFYWloK1atXF5KTk394DpR/IkH47qldRESUrWvXrsHDwwOHDx9G586dizscIiKiEoNjLIiIiIiISGZMLIiIiIiISGbsCkVERERERDLjHQsiIiIiIpIZEwsiIiIiIpIZEwsiIiIiIpIZEwsiIiIiIpIZn7xNWdrnvaa4QyD6z5iyMam4QygV3m6ZUtwhFAlRkzLFHQL9JIzr2xZ3CPSTCJt1I1f1eMeCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSCiIiIiIhkxsSihNuxYwd0dHSKOwwiIiIioh9SKO4A/iv69euHnTt3ZioPDAyEvb19MURUvDwvPMad0z6IjUqAURk9NOtTF1ZlzbKse2LjZTy68TxTuaG5LoYv7y5Zvnf2ER5eeoLo8FioaarAuaYdGnerBQWl0vkyz0sbBT99j52/n8xUPmJ5dxiY62Yqf3wnEEfXXYRTNRt0m9CiwGMvSnlpJwBIS03H9WOe8L/1AnFRCdDS00D99lVR2cM5U93ibKeaDmUwpFlNVLAyhrGOJgauP4Z/fAOl6oxrUxc9G1SEtpoKfF6HYsa+i3jxIRwAoKOmgvHt6qGBizXMdLUQGZeIf3xfYPnJm4hNTJHsw8ZYFzM6e6CanTkUFeTx7P1nLDtxE3efv5HUqWhtgt86usHVygSCAPgFh2LB0Wt4+vZTtvErKchjxi8eaFfdGSpKCrgdEIJp+y4i7EuspI62mjLmdmuMJhUdAAAXHwVi1v5LiElMLpA2JCKiolU6P3GVUs2bN8f27dulygwNDYspmuLz+G4gzu+6hVYDGsDCyQRel55i7+LTGLG8O7QNNDPVb963Hhp3ry1ZFqeLsem3g3CpZScp87v1ApcO3EO7IR6wcDRBRGgUTmy8krF9n3qFf1IFLK9t9NXIlT2grKokWVbTUslUJ+pzLC7svQPLsqaFEntRyk87HVnzD+KiE9F2sAf0TLQRH50IsVicqV5xt5OqshIC3n3Codv+2DK8Q6b1w5rXxKAm1TF++1m8/hiJ0a3qYN+4LnCb8Rfik1NgrKMBY20NzD98FYGhETDX18KiXs1grKOJoZtOSPazc1RnBH38gq4rDiApNQ0DG1fDjlGdUG/aZnyOiYe6shL2jO2CC74vMX3vRSjIy2F823rYM7YLakzegLT0zG0HAHO6NkLjivYYseUUvsQlYmaXhtgxqhNa/r4TYkEAAKwb2BamuproveYQAGBJ7+ZY/WtrDPjjaME3KBERFTp2hSpCysrKMDExkfpZs2YNXF1doa6uDgsLCwwfPhxxcXHZ7uPRo0fw8PCApqYmtLS0ULVqVTx8+FCy/s6dO2jQoAFUVVVhYWGB0aNHIz4+vihOL9funXmEyh7OqNLQBYbmemjetx609TXgefFxlvVV1JShoaMm+fkQ9AmJ8cmo5PbtG+Z3gWGwdDSBa11H6Bhqwa6CJcrXcUBo0OeiOq0Cldc2+kpdS1WqreTkpP+Li8ViHFt/Ee6dq0PXSKswT6FI5LWdXvq+QXDAB/Sc0gq2rhbQMdSCub0xLBylk4eS0E7XHgdh2YmbOO/zIsv1vzaqhnVn7+K8zws8/xCOcdvPQEVJEe1rZvy/eP4hHEM2ncAlv1cI+RyFO8/eYOnxG2hcwQ7yciIAgK6GKmyM9bDh/D08e/8ZwZ++YNHR61BTVoKjmQEAwM5EDzrqqlh+8iaCPkbixYdwrP77Fgy11GGul3XbaKoqoWu9Cvj90BXcCgjBk7efMOav0yhrboj6LtYAAHsTfXi42mLSrnPwDvoA76APmLz7PJpUtIetsV4BtyYRERUFJhbFTE5ODmvXrsXjx4+xc+dOXLlyBZMnT862fs+ePVGmTBl4enrCy8sLv/32GxQVFQEA/v7+aNasGTp27Ag/Pz8cPHgQt27dwsiRI4vqdHKUnpaOD68/w66ChVS5bQULvHvxMVf78LkWANvyZaBj+O0baUsnU3x4/RnvX2bs48vHaLz0DYFDZauCC76IyNJGf049hBXDdmDX/JN4/eR9pvXXjz6EuqYqqni4FGjMxSE/7fTc6zXMbI1w+28frBy+E+vG7cWFPbeRmpImVa+kt5OlgTaMdTRw48lrSVlKWjruv3iLqnbm2W6nqaqMuKQUpIsz7hh8iUvEiw/h6FSrPFSVFCEvJ0Ivt0r4FB0H/5AwAMCrsEhExCagW70KUJSXg4qiArrWq4jn7z/jXUR0lsdxtTKBkoI8bjz9Ft/H6Dg8fx8uia+qnRmiE5Lg+zpUUscn6AOiE5JQ7QfnQEREJRe7QhWh06dPQ0NDQ7LcokULHD58WLJsY2OD33//HcOGDcOGDRuy3MebN28wadIklC1bFgDg4OAgWbds2TL06NEDY8eOlaxbu3Yt3NzcsHHjRqioZO4WU9QSYpIgiAVoaKtKlWtoq+FV9Nsct4/9Eo9A3zfoNLKJVHn5Og6Ij0nEtjnHAWR0l6rWuBzqtatScMEXkfy0kYaOGloPdIeZrSHSUtPhd/M5di04iX4z28PKOWO8wZvnofC5FoChi7oU+jkUhfy005dPMXjzPBQKivLoOr45EmKTcGbbDSTGJaPd0IYASkc7GWpnXEfCYxKkyj/HxKOMvnaW2+ioq2BM6zrYe8NXqrznqoPYOqIjnq0bB7EgIDwmHr3XHJaMc4hPTkGX5fuxdURHjGldBwAQ9PELeq0+KElQvmekpY7k1DREJ0iPlQiPiYehlvr/z0EdEbEJmbaNiE2AobZ6Di1AREQlEROLIuTh4YGNGzdKltXV1XH16lUsXLgQT58+RUxMDNLS0pCUlIT4+Hioq2d+cx0/fjwGDhyI3bt3o3Hjxvjll19gZ5cx1sDLywsvX77E3r17JfUFQYBYLMbr16/h7Jx5cCoAJCcnIzlZ+gNAakoaFAt10LNIakkQhO+LsuR7/RlU1JRRtrqNVHnw0/e4ecILrQY0gLm9MSI/RuP8zlvQOPYQbh2rFWTgRSj3bWRgpgsDs2+DtC0cTRATGYc7p31h5WyG5MQUHF9/CW0GuUNNSzXrnZRauW8nQRAgAtBxZGOoqCkDAJr1roNDq/9BywENIE4Xl6p2EiD9wV4EUcb5f0dDRQk7R/+CwA8RWPX3bal1C3o2RXhsAjot3Yuk1DR0r1cBO0Z1RusFO/EpOh4qigpY3rcFPF++w8gtpyAnJ4chTWtg1+hf0HrBLiSlpmU6XrZEIqmYs4pVlE05ERGVfOwKVYTU1dVhb28v+UlJSUHLli1Rvnx5HD16FF5eXli/fj0AIDU1Nct9zJkzB0+ePEGrVq1w5coVuLi44Pjx/39LLxZjyJAh8PX1lfw8evQIgYGBkuQjK4sWLYK2trbUz6ntFwu+AZAxmFgkJ0JctPQ3lfExidDQUvvhtoIgwPf6M1So7wh5BXmpdVcPPUCF+k6o0tAFxpb6cK5ui0Zda+LWSW8I2XyrWlLJ0kb/Zm5vjMiwKADAl48xiPoci/3LzmJez42Y13MjHt18juderzGv50ZEfsy6S0tJlp920tRRh6aeuiSpAJAxa5YAxETElZp2+hydMQ7r67f/XxloqeFzjPSYKnVlJewe0wXxSSkYtOGY1GDrumWt0KiCHUZsPoWHr97j8ZuPmL7vIpJSUtG5dnkAQLuaLihjoI0JO87iUXAYfII+YNSWU7Aw0EbTSg7IyqeYeCgrKkD7X+0MAAaaapK7LJ+j42GglfnLE71/1SEiotKFdyyK0cOHD5GWloYVK1ZIBtkeOnQox+0cHR3h6OiIcePGoXv37ti+fTs6dOiAKlWq4MmTJ3mevnbq1KkYP368VNnxp1vytI/ckleQh5mNIYL83sK5uq2kPMj/HZyqWv9w25CAD4gMi0aVLKYFTU1Jg0gk/TW1SE4OEAQIECDKze2QEkKWNvq3sOBwaOhkfHAzMNPBsKVdpdZfOfQAKYkpkgHPpU1+2snCyQRP7r9CSlIqlFQyxiZFhEZDJBJBS18DIqBUtNOb8Gh8jIpDfRdrPPn/lK+K8nKo6WiBRUevSeppqGTM6JSSlo4B648iOS1daj+q/78rKf7uDoFYECD3/wHeqkoKEIsF/LuKWMhYlsvmv5V/SBhS0tJR38UGpx8+AwAYaavDydwAC/8fn9erD9BWU0Ela1P4BmeMs6hkYwptNRU8fJV5fBAREZV8vGNRjOzs7JCWloZ169YhKCgIu3fvxqZNm7Ktn5iYiJEjR+LatWsICQnB7du34enpKeniNGXKFNy9excjRoyAr68vAgMDcerUKYwaNeqHcSgrK0NLS0vqpzC7QdVqVRHeVwPgczUAn99H4vyuW4gOj0W1xhnfkF7afxfHN1zKtJ3P1QCY2xvDyEI/0zrHKlZ4eOkxHt8JxJdPMXjl9xZXD9+HU1XrTDMjlQZ5baN7Zx/hmWcQIkKj8OltJC7tv4uAB0Go0SyjvoKSAows9KV+VNSUoKSqBCML/Ux3gEqLvLaTa11HqGko4+SmK/j8LhIhAR9wce8dVHIvC0UlhRLVTmrKinCxMIKLhREAwMJAGy4WRjDTy5i0YOvlhxjZsjaaV3aAk5kBVvZvhaSUVJy4HwAg407F3nFdoaasiEk7z0FTRRmGWuow1FKH3P+TcK+gD4iOT8Kq/q3gXMYQNsa6mN7ZHRYGOrjs9woAcPNpMLTVVbCgRxPYm+jD0cwAK/q1RJpYjDv/f9aFiY4Grs4biErWGbNrxSam4OAtP8z8xQN1y1qhnIUR1vzaGs/ef8bNp8EAgJdhEbjqH4QlfZqjsq0ZKtuaYWmf5rj46CWCPkYWWTsTEVHB4R2LYlSpUiWsXLkSS5YswdSpU9GgQQMsWrQIffr0ybK+vLw8IiIi0KdPH3z8+BEGBgbo2LEj5s6dCwCoUKECrl+/junTp6N+/foQBAF2dnbo2rVrlvsrLuVrOyAxNhnXjz1EXFQ8jCz00XNKa8ksT3FRCYgOl55yNykhGU8fBGX7TIoGHaoBEOHKofuIjYyHmpYqHKtYo1HXmoV9OoUir22UnibGhb13EBsZn/HhuIweekxuVSpnxcqLvLaTkooiek9ri3M7bmLz9CNQ01CGSy17NCyBr5MKViY4PKmHZHl210YAgMN3/DF++1lsPH8fKooKmN+jKbTVVeAb9AE9Vx1CfHLGw+9crYxRxTZj4P6thUOk9l37t414FxGDL3GJ6L3mMCZ3aICDE7pDQV4OLz6E49f1xxDwLmOq5ldhkRiw7ijGtqmLE1N7QRAEPH7zEb3XHMan6IxuVwrycrA31YfKv76QmHvwMtLEYmwc0g4qigq49SwE49cdlbo7MvqvvzG3e2PsHZsxUP7io5eYua9wumESEVHhEwkcJUdZ2Oe9prhDIPrPmLIxqbhDKBXebplS3CEUCVGTMsUdAv0kjOvb5lyJKBfCZt3IVb3S10eEiIiIiIhKHCYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkMyYWREREREQkM4XiDoCI6L+uQUOV4g6BiIhIZrxjQUREREREMmNiQUREREREMmNiQUREREREMmNiQUREREREMmNiQUREREREMmNiQUREREREMmNiQUREREREMmNiQUREREREMuMD8oiIiKhIze49HnP6jJcqC4v8BNOuVQAA2yetRL+mXaTW3wvwRu3RbaXKajlXwYL+U1CzbGWkpqfC99VTtJjWG0kpSbAyLoOZPceiYaU6MNEzwoeIMOy5fBwL9q1Falpq4Z4gFYtRdXtieqMh2HzvMGZdWAcACJt1I8u68y5uwIa7B6CjoolJ7gPgZlsdZtpGiEyIxvlnN7Hk2lbEJsdLbdPYoRbGN+gHZyM7JKQm4l6IH349PKPQz6s0YWJBRERERe7x62doPKW7ZDldnC61/tyDq+i//FvykfJdMlDLuQrOL9qDRfvXY9T6mUhJS0VFWxeIBTEAoKyFPeTkRBiy5je8fB+M8jZO2DJuKdRVVDFp8/xCPDMqDpXMyqJ3lbZ4EvZSqtx1RXup5Ub2NbGy7RScDrgOADDRNICxpgHmXtqAF5+DUUbbBEtbTYCJpgEGHpkl2a5VWTcsbzMJi65sxq3X3hCJRHA2si308yptmFgQERFRkUsTp+Pjl8/Zrk9OTf7h+lXD5mDt8W1YcnC9pOzl+9eS3/95eA3/PLwmWX4d9gbLy/yJYW16M7H4yagpqmJ9h5mYcHopxtXvI7Xuc3yk1HIzp3q4HeyDN1GhAIBnn19j4OGZkvUhXz5g8ZUt+KPDDMiL5JEupENeJI/fm4/CvIsbsd/3jKTuq4i3hXhWpRPHWBAREeXDu3fvMH36dHh4eMDZ2RkuLi7w8PDA9OnT8fYtP3DkxMHMBu8PPETQrjvYP209bEwspda7V6yNj4d88Xz7DWwetxSGOvqSdYY6+qjlXAWfoiJwe/UJhB3ywbUVR1C3XPUfHlNbXRORsVGFcTpUjBa3HIdLgXdx87XXD+sZqOuisUNt7PM588N6mirqiEtOQLqQcRetgqkjzLSMIAhiXBz0Fx6NO459PZbCydC6oE7hp8HEgoiIKI9u3boFZ2dnHD9+HBUrVkSfPn3Qq1cvVKxYESdOnEC5cuVw+/bt4g6zxLr/zAd9lo5Fs996YdCqyTDRM8KdNSegp6kDIKMbVM9Fo9BwcldM+HMeqjtVxJWlB6GkqAQAsDW1AgDM6TMeW87tQ/OpveAd6I/LSw/A3twmy2PamlphVPv+2HR6T5GcIxWNduUawtXEEQsvb86xbteKzRGXkoCzAVmPuwAAXVUtjK/fF7u8T0nKLHVNAQAT3fpj9c3d6H1gCqISY3Gs71roqGjKfhI/EXaFIiIiyqNx48Zh4MCBWLVqVbbrx44dC09Pzx/uJzk5GcnJydKFYgGQExVUqCXSec+rkt8fBwN3A7zwaudt9G36C1Yd3YJD1/+WrH8S/BwPX/ghZM89tKrZCMdvnYOcKKN9/jyzBzv+OQQA8H31BI0q18OAZl0xbdtiqeOZ6hvj/KI9OHzjDLae21/4J0hFwkzLCPObjUbXvROQnJ6SY/1ulVrimP/FbOtqKKlhT/cleBEejBXXt0vK5UQZ38OvvrUbZ55ljM0Ye2oxfMYeRRsXD+z+VxLyX8c7FkRERHn0+PFjDB06NNv1Q4YMwePHj3Pcz6JFi6CtrS31g9exBRlqqZCQlAj/18/gkM3dhrDITwj59F6yPjTyEwDgaUigVL2AN4GwNDKXKjPVN8bVZYdw96kXBq+aXAjRU3GpYOoIQw09XBi0Be9mXMG7GVdQx7oyBtbshHczrkgSAgCoaVkBDgZW2OtzOst9qSupYn/P5YhPSUT/gzOQ9q/JBD7FRQAAXnwOlpSlpKciJOoDzLWNCufkSinesSAiIsojU1NT3LlzB05OTlmuv3v3LkxNTXPcz9SpUzF+vPS0q9odnAskxtJESVEJzpYOuPn4QZbr9TR1YGFoitCIjwCA4LC3eB8eBqcy0rPyOJaxxbl/3Q0x0zfB1eWH4BXoh/7Lx0MQhMI7CSpyN197wX1jX6my1W1/Q2DEG6y/vU8yQxgA9KjUCo8+PMPTj68y7UdDSQ0Hei1HSloq+h6YmumOxqMPz5GUlgw7fUs8eOsPAFCQk4eFtgneRX8shDMrvZhYEBER5dHEiRMxdOhQeHl5oUmTJjA2NoZIJEJYWBguXryIv/76C6tXr85xP8rKylBWVpYu/Mm7QQHAssEz8Pe9S3jz6T2MdAwwo8doaKlpYOeFw1BXUcOcPuNx9OZZhEZ+grWxBRYOmILw6C84fvv8t30c2oi5fSfgUVAAfF89Qd8mnVHWwh6d5w0BkHGn4tqKw3jz6T0m/jkfhtrfBn//aLYpKj3iUxLx7PNrqbKE1CR8SYiRKtdQUkMbF3fMubj++11AXUkVB3utgKqiCkYcnw8NZXVoKKsDACISoiAWxIhLScCuh6cwyb0/PsR8wrvoMAyvnTFV8t9Pr2ba538ZEwsiIqI8Gj58OPT19bFq1Sr8+eefSE/P6DYhLy+PqlWrYteuXejSpUsOe/nvKmNgiv3T/oCBlh4+R0fiXoA3ao1uizef3kNFSQWuNmXRp3Fn6GhoITTyE64+uoOuC4YhLvHbA8vWHN8KFSUVrBo6G3qaOngU9BRNpnRHUGgIAKBp1QZwMLeBg3nG7FP/JmpSpkjPl4pX+/KNAJEIxx9fzrSuoqkTqpYpBwC4P+qA1Lrqa7rgbXQYAGDepQ1IF9LxR/vpUFFUhvf7p+i8eyyik+IK/wRKEZHA+4KUhX3ea4o7BKL/jDPPizuC0mFv9zHFHUKWUlNTER4eDgAwMDCAoqKiTPvjh14qKMb1+QA3KhjZPcH8e7xjQUREJANFRcVcjacgIvrZcVYoIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGcdYFBOR6MfTCfbt2xc7duwommCKgeeFx7hz2gexUQkwKqOHZn3qwqqsWZZ13zwLxaX9dxH+4QtSk9OgbaiJqo3KoXbLipI6vtef4eSmK5m2nb5zMBSUSufLPC9tBABpqem4fswT/rdeIC4qAVp6Gqjfvioqe2TMie91+Sn8bj7Hp3eRAABTG0M06loT5vbGRXI+hSWv7fTVm+eh2DHvBIws9DB0cVdJeWl5LbV1qYauFevi3HMf7PHOGFRXrYwdGtm7wkbPCJrKqph2bi9CosKltlOQk0ePyvVQx8oJivIKeBL2FjseXkVkYuaZTRTk5DGvaVdY6Rpmua/vdSxfEw3tykNdSQUvI8Kw4+FVvI+JzNexiYio9Ck575L/MaGhoZLfDx48iFmzZuH5829Tw6iqqkrVT01NlXmmkZLi8d1AnN91C60GNICFkwm8Lj3F3sWnMWJ5d2gbaGaqr6isgOpNXWFsqQ8lFQW8eRaK01uvQ0lZAVUblZPUU1ZVwsiVPaS2LUkfBPMir20EAEfW/IO46ES0HewBPRNtxEcnQiz+9nCgkID3KF/HARaOJlBQlMftv32we9HfGL6sG7T0NIrq1ApUftoJAJISknFiw2XYli+DuOiETOtL+mvJVs8YHnblEfLdXPwqCop48fkD7r8JxKCajbPctneVBqhiboN1t88hLiUJPSvXx0S3tpj+z/5MDw/rXqkuviTGw0rXMMeYWjtXRcuylbHp3kWExUahfbnqmOrRARPP7EJSWmqej01ERKUPu0IVExMTE8mPtrY2RCKRZDkpKQk6Ojo4dOgQ3N3doaKigj179mDOnDmoVKmS1H5Wr14Na2trqbLt27fD2dkZKioqKFu2LDZs2FB0J5YL9848QmUPZ1Rp6AJDcz0071sP2voa8Lz4OMv6pjaGcK3rACMLPegYaqFCfSfYVbDAm2eh0hVFgIaOmtRPaZXXNnrp+wbBAR/Qc0or2LpaQMdQC+b2xrBw/DZTTceRTVC9aXmYWBvAwFwXbQa7QxAEvH78rqhOq8DltZ2+Ov3XdZSv64AyDtncrSnBryVlBUUMr90Mfz24jPiUZKl1t4Kf4fiTB3j88U2W26oqKsHdthz2+tzEk49vEfLlMzbc/QcW2voob2whVbeiqRVcTaywz+dmruJq7lQZJ5544uG7V3gXHYFN9y5CSUERdayc8nxsIiIqnZhYlGBTpkzB6NGjERAQgGbNmuVqmy1btmD69OlYsGABAgICsHDhQsycORM7d+4s5GhzJz0tHR9ef4ZdBekPErYVLPDuxcdc7SP09We8fREGK2fp7i4pSalYPWoXVo7YiX1LzyD0del8smp+2ui512uY2Rrh9t8+WDl8J9aN24sLe24jNSUt2+OkJqdBnCaGqoZKgcZfVPL7WvK5FoAvH6Ph3ql6tnVK8mupXzV3+H4IxpOPb/O8rY2eERTk5eEX+i3xiEqMx9voCDgafPv/pKWihoE1GmHjvX+QnJ6a434N1bWgq6oO/7Bv+00Tp+PZp3dwMDTN07GJiKj0Kjn39imTsWPHomPHjnna5vfff8eKFSsk29nY2ODp06f4888/0bdv38IIM08SYpIgiAVoaEt39dLQVsOr6B9/UFo5YicSYhIhThfg1rk6qjR0kawzMNNB+6ENYWSpj+TEFNw/54dtc45j6OIu0DfVKYxTKTT5aaMvn2Lw5nkoFBTl0XV8cyTEJuHMthtIjEtGu6ENs9zm0v570NRTh2350vkwrvy0U0RoFC7vv4f+czpATj7r71VK8muplqUjbHSNMPOfAzlXzoKOijpS09OQkCp9pyMmKQHaqt/uygyt2QSXX/rjdeQnGKhn36VMsl9VdQBAdJJ0t7LopAQYqGvl6dhERFR6MbEowapVq5an+p8/f8bbt2/x66+/YtCgQZLytLQ0aGtrZ7tdcnIykpOl3+xTU9KgWKh9yqUHrwuC8H1RJv1nd0BKUireBX7E5QN3oWesDde6DgCAMg4mKONgIqlr6WiKP6cdwoN//NGiX/0Cj75o5L6NBEGACEDHkY2hoqYMAGjWuw4Orf4HLQc0yPS3vH3KB4/vBKLfzHYlauxA/uSuncRiMY79cRHunav/MEEoqa8lPTUN9KnqhsVXjyNVnF6wOxeJgP8PcWjmWBGqiko4+fRh3vfz3TgJEUQQkMPYiX8dm4iISrfS/onip6auri61LCcnl2mAY2rqt24KXwfqbtmyBTVr1pSqJy8vn+1xFi1ahLlz50qVdRzcDJ2GtMhX3D+ipqUCkZwo04DZ+JhEaGj9+FtLXaOMbz6NLfURH52A60c9JYnF90RyIpjZGiEyLLpgAi9C+WkjTR11aOqpS5IKADAw1wUEICYiTuqD9J3TPrh50gt9prWFsZVBoZxDUchrO6UkpuJD0GeEBofj7I6McQOCIAACMK/nRvSe2gY2Wdy9KSmvJRtdI2irqGF+s+6SMnk5OZQ1MkdTh4roe+iPHAdARyXFQ1FeAWqKylJ3DrSUVRH4OWPMkouxBez1TbCzy0ipbX9v1h23Q57hz3sXM+83MR4AoK2qjqh/3bXQUlGV3MXIzbGJiKh0Y2JRihgaGiIsLCzj2+n/T1fr6+srWW9sbAxzc3MEBQWhZ8+eud7v1KlTMX78eKmy40+3FEjM35NXkIeZjSGC/N7CubqtpDzI/x2cqlrnej8CMqZXzXa9IOBjSDiMLPRliLZ45KeNLJxM8OT+K6QkpUJJJWP2sIjQaIhEImjpf5vx6fbfPrh53Au9praGmZ1RoZ5HYctrOymrKmHY0q5SZZ4XHuP10/foMrYZdAy1sjxOSXktPfn4FlPO7pEqG1yzCUJjIvF3gFeuZlV6HfkJaenpcDWxxP23gQAAHRU1WGjrY7/vLQDALq/rOOx3V7KNrqo6fvPogHW3z+FVRFiW+/0cH4MvifFwNbGUzFSVkfSUwYH/7zc3xyYiotKNiUUp4u7ujs+fP2Pp0qXo3Lkzzp8/j3PnzkFL69sHojlz5mD06NHQ0tJCixYtkJycjIcPH+LLly+ZkoevlJWVoaysLFVWmN2garWqiOPrL8PM1ghlHI3hdfkposNjUa1xeQDApf13EfslHh2GZ0yX+eCCP7T1NWFgpgMg4/kDd0/7okYzV8k+rx3xRBkHY+ibaCM5MRX3z/shLCQCLfs3KLTzKEx5bSPXuo64cewhTm66AvfO1ZEQm4SLe++gkntZyd/y9ikfXD18Hx1HNoGOoRbiojK+SVZSUZQkI6VNXtpJJCfKlByoa6tCQVFeqrykvpaS0lLxLjpCqiw5LRWxKUmScnUlZRioaUJHNSOZNNXSBQBEJSUgOikBiakpuBb0BD0r10dcShLiUpLQo1J9vI2OwOP/DwaPSIj97rgpAIBPcVFSz5tY1qo3Dj66g4fvXgEAzj/3QVuX6giLjUJYbBTauVRHSloq7oRkTKOdm2MTEVHpxsSiFHF2dsaGDRuwcOFC/P777+jUqRMmTpyIzZs3S+oMHDgQampqWLZsGSZPngx1dXW4urpi7NixxRf4d8rXdkBibDKuH3uIuKh4GFnoo+eU1tAxzBgkGheVgOjwbx9gBLGAywfuIepzDOTk5KBrrIVG3Wuh2r+eYZGUkIzTf11DXFQClNWUYWptgH6z2pfah7/ltY2UVBTRe1pbnNtxE5unH4GahjJcatmjYddvXeI8Lz5GepoYh1f/I3Ust07V4N65RtGcWAHLazvlRml+LVU1t8WQWk0ly6PqtgQAHPW/h2OP7wMA9njfQLogxqi6LaAkr4AnH99i+Y0LeX6OhJmWHtQUlSTLpwO8oCSvgH7VPKCupIxXEWFYfO2E5BkWBXlsIiIqmUQCr+iUhX3ea4o7BKL/jDPPc65DwN7uY4o7hCIhalI6Z2qjkse4vm3OlYhyIWzWjVzV43MsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZkwsiIiIiIhIZgrFHQAR0X9dK6fijoCIiEh2vGNBREREREQyY2JBREREREQyY2JBREREREQyY2JBREREREQyY2JBREREREQyY2JBREREREQyy1dikZiYiISEBMlySEgIVq9ejQsXLhRYYEREREREVHrkK7Fo164ddu3aBQCIiopCzZo1sWLFCrRr1w4bN24s0ACJiIiIiKjky1di4e3tjfr16wMAjhw5AmNjY4SEhGDXrl1Yu3ZtgQZIREREREQlX74Si4SEBGhqagIALly4gI4dO0JOTg61atVCSEhIgQZIREREREQln0J+NrK3t8eJEyfQoUMH/PPPPxg3bhwA4NOnT9DS0irQAImIiGRx6tSpXNdt27ZtIUZCRPRzy1diMWvWLPTo0QPjxo1Dw4YNUbt2bQAZdy8qV65coAESERHJon379rmqJxKJkJ6eXrjBEBH9xPKVWHTu3Bn16tVDaGgoKlasKClv1KgROnToUGDBERERyUosFhd3CERE/wn5fo6FiYkJNDU1cfHiRSQmJgIAqlevjrJlyxZYcEREREREVDrk645FREQEunTpgqtXr0IkEiEwMBC2trYYOHAgdHR0sGLFioKOk4iIqEDEx8fj+vXrePPmDVJSUqTWjR49upiiIiIq/fKVWIwbNw6Kiop48+YNnJ2dJeVdu3bFuHHjmFgQEVGJ5OPjg5YtWyIhIQHx8fHQ09NDeHg41NTUYGRkxMSCiEgG+eoKdeHCBSxZsgRlypSRKndwcOB0s0REVGKNGzcObdq0QWRkJFRVVXHv3j2EhISgatWqWL58eXGHR0RUquUrsYiPj4eamlqm8vDwcCgrK8scFBERUWHw9fXFhAkTIC8vD3l5eSQnJ8PCwgJLly7FtGnTijs8IqJSLV+JRYMGDbBr1y7JskgkglgsxrJly+Dh4VFgwRERERUkRUVFiEQiAICxsTHevHkDANDW1pb8TkRE+ZOvMRbLli2Du7s7Hj58iJSUFEyePBlPnjxBZGQkbt++XdAxEhERFYjKlSvj4cOHcHR0hIeHB2bNmoXw8HDs3r0brq6uxR0eEVGplq87Fi4uLvDz80ONGjXQpEkTxMfHo2PHjvDx8YGdnV1Bx0hERFQgFi5cCFNTUwDA77//Dn19fQwbNgyfPn3C5s2bizk6IqLSLV93LICM51jMnTu3IGMhIiIqVNWqVZP8bmhoiLNnzxZjNEREP5d83bE4f/48bt26JVlev349KlWqhB49euDLly8FFhwREREREZUO+UosJk2ahJiYGACAv78/xo8fj5YtWyIoKAjjx48v0ACJiIgKio2NDWxtbbP9ISKi/MtXV6jXr1/DxcUFAHD06FG0adMGCxcuhLe3N1q2bFmgAdLPyfPCY9w57YPYqAQYldFDsz51YVXWLMu6wU/fY+fvJzOVj1jeHQbmupLlpPhkXD54H888g5AYnwxdQ0007VUXDpWtCu08ChPbKHfYTjljG30zduxYqeXU1FT4+Pjg/PnzmDRpUvEERUT0k8hXYqGkpISEhAQAwKVLl9CnTx8AgJ6enuROBuVdamoqFBUVizuMQvf4biDO77qFVgMawMLJBF6XnmLv4tMYsbw7tA00s91u5MoeUFZVkiyraalIfk9PS8fuhaegrqWKX8Y2g5aeBmIi4qCkWjrbk22UO2ynnLGNpI0ZMybL8vXr1+Phw4dFHA0R0c8lX12h6tWrh/Hjx+P333/HgwcP0KpVKwDAixcvMj2NuyQ7f/486tWrBx0dHejr66N169Z49eqVZP27d+/QrVs36OnpQV1dHdWqVcP9+/cl60+dOoVq1apBRUUFBgYG6Nixo2SdSCTCiRMnpI6no6ODHTt2AACCg4MhEolw6NAhuLu7Q0VFBXv27EFERAS6d++OMmXKQE1NDa6urti/f7/UfsRiMZYsWQJ7e3soKyvD0tISCxYsAAA0bNgQI0eOlKofEREBZWVlXLlypSCaTWb3zjxCZQ9nVGnoAkNzPTTvWw/a+hrwvPj4h9upa6lCQ0dN8iMn9+3l63M1AIlxyeg6oQUsnUyhY6gJy7KmMLEyKOzTKRRso9xhO+WMbZQ7LVq0wNGjR4s7DCKiUi1fdyz++OMPDB8+HEeOHMHGjRthbm4OADh37hyaN29eoAEWpvj4eIwfPx6urq6Ij4/HrFmz0KFDB/j6+iIhIQFubm4wNzfHqVOnYGJiAm9vb4jFYgDAmTNn0LFjR0yfPh27d+9GSkoKzpw5k+cYpkyZghUrVmD79u1QVlZGUlISqlatiilTpkBLSwtnzpxB7969YWtri5o1awIApk6dii1btmDVqlWoV68eQkND8ezZMwDAwIEDMXLkSKxYsULyFPS9e/fCzMysRDy8MD0tHR9ef0bddlWkym0rWODdi48/3PbPqYeQlpoOQ3Nd1O9QDTblzCXrnnsHo4yDMc5uv4nnD19DTUsVrnUdULdtZakPRKUB2yh32E45Yxvl3pEjR6Cnp1fcYRARlWr5SiwsLS1x+vTpTOWrVq2SOaCi1KlTJ6nlrVu3wsjICE+fPsWdO3fw+fNneHp6St5s7O3tJXUXLFiAbt26SU25W7FixTzHMHbsWKk7HQAwceJEye+jRo3C+fPncfjwYdSsWROxsbFYs2YN/vjjD/Tt2xcAYGdnh3r16knOadSoUTh58iS6dOkCANi+fTv69esnedpscUqISYIgFqChrSpVrqGthlfRb7PcRkNHDa0HusPM1hBpqenwu/kcuxacRL+Z7WHlnNFP/MunGLz+HAvXug7oMaUVIsOicXb7DYjTxXDrVL3Qz6sgsY1yh+2UM7ZRZpUrV5a6FgqCgLCwMHz+/BkbNmwoxsiIiEq/fCUW3t7eUFRUlDyl9OTJk9i+fTtcXFwwZ84cKCkp5bCHkuHVq1eYOXMm7t27h/DwcMndiDdv3sDX1xeVK1fO9hssX19fDBo0SOYY/j2nOgCkp6dj8eLFOHjwIN6/f4/k5GQkJydDXV0dABAQEIDk5GQ0atQoy/0pKyujV69e2LZtG7p06QJfX188evQoU7esf/t6jH9LTUmDolK+H3OSC9JJjiAI3xdJGJjpwsDs26BRC0cTxETG4c5pX8kHHUEsQF1LFW0GuUNOTg5mtkaI/RKPO6d9S/wHneyxjXKH7ZQzttFX7dq1k0os5OTkYGhoCHd3d5QtW7YYIyMiKv3y9clxyJAh+O233+Dq6oqgoCB069YNHTp0wOHDh5GQkIDVq1cXcJiFo02bNrCwsMCWLVtgZmYGsViM8uXLIyUlBaqqqj/cNqf1IpEo4837X1JTUzPV+5owfLVixQqsWrUKq1evhqurK9TV1TF27FikpKTk6rhARneoSpUq4d27d9i2bRsaNWoEK6vsZ2pZtGhRpocddhzcDJ2GtMjxWHmlpqUCkZwIcdEJUuXxMYnQ0FLL9X7M7Y3hf+uFZFlTRx1yCnJS3TAMzHURF5WA9LR0yCvIyx58EWEb5Q7bKWdso8zmzJlT3CHkKPH8i5wrEeWCX6RXcYdA/zH56gz74sULVKpUCQBw+PBhNGjQAPv27cOOHTtKzeC3iIgIBAQEYMaMGWjUqBGcnZ2lHu5XoUIF+Pr6IjIyMsvtK1SogMuXL2e7f0NDQ4SGhkqWAwMDJTNp/cjNmzfRrl079OrVCxUrVoStrS0CAwMl6x0cHKCqqvrDY7u6uqJatWrYsmUL9u3bhwEDBvzwmFOnTkV0dLTUT9v+TXKMNT/kFeRhZmOIID/pbhhB/u9QxtE41/sJCw6Hhs63pMzCyQSRYdEQxN+SuYjQKGjoqJXoDzlZYRvlDtspZ2yjzOTl5fHp06dM5REREZCXL9mxExGVdPlKLARBkHQbunTpkuTZFRYWFggPDy+46AqRrq4u9PX1sXnzZrx8+RJXrlyRerhf9+7dYWJigvbt2+P27dsICgrC0aNHcffuXQDA7NmzsX//fsyePRsBAQHw9/fH0qVLJds3bNgQf/zxB7y9vfHw4UMMHTo0V1PJ2tvb4+LFi7hz5w4CAgIwZMgQhIWFSdarqKhgypQpmDx5Mnbt2oVXr17h3r172Lp1q9R+Bg4ciMWLFyM9PR0dOnT44TGVlZWhpaUl9VOY3aBqtaoI76sB8LkagM/vI3F+1y1Eh8eiWuPyAIBL++/i+IZLkvr3zj7CM88gRIRG4dPbSFzafxcBD4JQo1l5SZ1qTcohMS4J53beQkRoFF54B+PWCW9Ub1o+0/FLA7ZR7rCdcsY2kvb9neSvkpOTS003XiKikipfnx6rVauG+fPno3Hjxrh+/To2btwIIOPBecbGuf8WrDjJycnhwIEDGD16NMqXLw8nJyesXbsW7u7uADKe1XHhwgVMmDABLVu2RFpaGlxcXLB+/XoAgLu7Ow4fPozff/8dixcvhpaWFho0aCDZ/4oVK9C/f380aNAAZmZmWLNmDby8cr4lOXPmTLx+/RrNmjWDmpoaBg8ejPbt2yM6OlqqjoKCAmbNmoUPHz7A1NQUQ4cOldpP9+7dMXbsWPTo0QMqKirfH6ZYla/tgMTYZFw/9hBxUfEwstBHzymtoWOYMad+XFQCosPjJPXT08S4sPcOYiPjoaCkAKMyeugxuZXUg7i09TXRa2ob/LP7NjZOOQgtXXXUbFEBddtWLvLzKwhso9xhO+WMbZRh7dq1ADK6qf7111/Q0NCQrEtPT8eNGzc4xoKISEYiIbuvb37Az88PPXv2xJs3bzB+/HjMnj0bQMYMRhEREdi3b1+BB0p58/btW1hbW8PT0xNVqlTJeYPv7PNeUwhRERHlX48qWT/cLjdsbGwAACEhIShTpoxUtyclJSVYW1tj3rx5kmm9i1NSes7dZolyg2MsqKDUMKyfq3r5umNRoUIF+Pv7ZypftmwZ+6gWs9TUVISGhuK3335DrVq18pVUEBH9bF6/fg0A8PDwwLFjx6Crq5vDFkRElFcF2pG+pHW5+S+6ffs2PDw84OjoiCNHjhR3OEREJcrVq1eLOwQiop9WvgZvp6enY/ny5ahRowZMTEygp6cn9UPFx93dHYIg4Pnz55LnjBARUYbOnTtj8eLFmcqXLVuGX375pRgiIiL6eeQrsZg7dy5WrlyJLl26IDo6GuPHj0fHjh0hJydXKuYIJyKi/6br16+jVatWmcqbN2+OGzduFENEREQ/j3wlFnv37sWWLVswceJEKCgooHv37vjrr78wa9Ys3Lt3r6BjJCIiKhBxcXFZTiurqKiImJiYYoiIiOjnka/EIiwsTNLNRkNDQzIVauvWrXHmzJmCi46IiKgAlS9fHgcPHsxUfuDAAbi4uBRDREREP498Dd4uU6YMQkNDYWlpCXt7e1y4cAFVqlSBp6cnlJWVCzpGIiKiAjFz5kx06tQJr169QsOGDQEAly9fxr59+zjhBRGRjPKVWHTo0AGXL19GzZo1MWbMGHTv3h1bt27FmzdvMG7cuIKOkYiIqEC0bdsWJ06cwMKFC3HkyBGoqqqiYsWKuHLlCrS0tIo7PCKiUi1fD8j73r1793Dnzh3Y29ujbdu2BREXFTM+II+IShpZHpCXnaioKOzduxdbt27Fo0ePkJ6eXuDHyCs+II8KCh+QRwWlUB+Q971atWqhVq1aBbErIiKiQnflyhVs27YNx44dg5WVFTp16oStW7cWd1hERKVarhOLU6dO5XqnvGtBREQlzbt377Bjxw5s27YN8fHx6NKlC1JTU3H06FEO3CYiKgC5Tizat2+fq3oikahE3EomIiL6qmXLlrh16xZat26NdevWoXnz5pCXl8emTZuKOzQiop9GrhMLsVhcmHEQEREVmgsXLmD06NEYNmwYHBwcijscIqKfUp6eY3HlyhW4uLhk+RCh6OholCtXDjdv3iyw4IiIiArCzZs3ERsbi2rVqqFmzZr4448/8Pnz5+IOi4jop5KnxGL16tUYNGhQllPyaWtrY8iQIVi5cmWBBUdERFQQateujS1btiA0NBRDhgzBgQMHYG5uDrFYjIsXLyI2Nra4QyQiKvXylFg8evQIzZs3z3Z906ZN4eXFqc2IiKhkUlNTw4ABA3Dr1i34+/tjwoQJWLx4MYyMjDjxCBGRjPKUWHz8+BGKiorZrldQUOCtZSIiKhWcnJywdOlSvHv3Dvv37y/ucIiISr08JRbm5ubw9/fPdr2fnx9MTU1lDoqIiKioyMvLo3379nmaVp2IiDLLU2LRsmVLzJo1C0lJSZnWJSYmYvbs2WjdunWBBUdERERERKVDnp68PWPGDBw7dgyOjo4YOXIknJycIBKJEBAQgPXr1yM9PR3Tp08vrFiJiIiIiKiEylNiYWxsjDt37mDYsGGYOnUqBEEAkPFQvGbNmmHDhg0wNjYulECJiIiIiKjkylNiAQBWVlY4e/Ysvnz5gpcvX0IQBDg4OEBXV7cw4iMiIiIiolIgz4nFV7q6uqhevXpBxkJERERERKVUngZvExERERERZYWJBRERERERySzfXaGIiKhgLBr4prhDKBV6eBd3BERE9CO8Y0FERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJjYkFERERERDJTKO4A6L/J88Jj3Dntg9ioBBiV0UOzPnVhVdYsy7rBT99j5+8nM5WPWN4dBua6AIAd804gJOBDpjoOlSzRY0rrgg2+iBR0GwHAvbOP8PDSE0SHx0JNUwXONe3QuFstKCiV3ktBYbTTV4/vBOLouotwqmaDbhNaFHjsP1K1ii369XGHi3MZGBlqY8z47bhy7XGWdWdN74xfOtXGkuUnsGffTanyWjUcYGiojYTEZDx6FIxVa8/gdfAnSR3nsuYYN7o1ypWzgDhdjEtX/LB0xSkkJqYAABwdTPFr/0aoUskGOjrq+BAaiUNH7mLv/puZ4vg3RUV5TBzXFi2aVYayigLuP3iJBYuO4uOnaEkdLU1V/Da5PdwblAMAXLvxBIuWHEdsXFK+242IiIpP6f00UYKlpqZCUVGxuMMosR7fDcT5XbfQakADWDiZwOvSU+xdfBojlneHtoFmttuNXNkDyqpKkmU1LRXJ713HN0d6mliynBCbhE2/HYRLLfvCOYlCVhht5HfrBS4duId2Qzxg4WiCiNAonNh4BQDQvE+9wjuZQlQY7fRV1OdYXNh7B5ZlTQsl9pyoqijhxYsPOHHKE6uX98u2XkP38nAtbyn1gf2rpwHvcOacN0JDv0BbWw3DhjTDn+sHo3mbBRCLBRgaaGHLxqE4f8EXC5ccg7q6CqZMbIf5c7thwuRdAAAXFwt8+RKHqTP2IuxjFCpVtMas6b9ALBZj/8Hb2cY1ZWJ7uDdwweSpuxEVnYCJ49vgjzW/omvPVRCLBQDAkoU9YWykg2GjtgAAZk//BQvn98CosdtkaDkiIiouP1VXKHd3d4waNQpjx46Frq4ujI2NsXnzZsTHx6N///7Q1NSEnZ0dzp07J9kmPT0dv/76K2xsbKCqqgonJyesWbMm0763bduGcuXKQVlZGaamphg5cqRknUgkwqZNm9CuXTuoq6tj/vz5AICNGzfCzs4OSkpKcHJywu7du38Yv6enJ5o0aQIDAwNoa2vDzc0N3t7ekvXdu3dHt27dpLZJTU2FgYEBtm/fDgCIjY1Fz549oa6uDlNTU6xatQru7u4YO3ZsntuzsNw78wiVPZxRpaELDM310LxvPWjra8DzYtbfxn6lrqUKDR01yY+c3LeXr6qGitS6IP+3UFRWgEtNu8I+nUJRGG30LjAMlo4mcK3rCB1DLdhVsET5Og4IDfpc2KdTaAqjnQBALBbj2PqLcO9cHbpGWoV5Ctm6decZ1m04j8tX/LOtY2SohWlTOuC36XuRlpaeaf2RY/fg5R2ED6FfEPDsPf7YcA6mprowM9MDALg1cEFaWjoWLD6G4JDPePL0LRYsPoamjSvCwkIfAHDi5AMsXnYCD72D8O59JE6f9cbJvz3RqKFrtnFpaKigY/saWLbqb9x7EIhnz99j6vR9cLA3Ra2ajgAAGxsj1KvrjNm/H8IjvxA88gvBnPmH4N6gHKytDGVpOiIiKiY/VWIBADt37oSBgQEePHiAUaNGYdiwYfjll19Qp04deHt7o1mzZujduzcSEhIAZHyAKFOmDA4dOoSnT59i1qxZmDZtGg4dOiTZ58aNGzFixAgMHjwY/v7+OHXqFOztpb8Jnz17Ntq1awd/f38MGDAAx48fx5gxYzBhwgQ8fvwYQ4YMQf/+/XH16tVsY4+NjUXfvn1x8+ZN3Lt3Dw4ODmjZsiViY2MBAD179sSpU6cQFxcn2eaff/5BfHw8OnXqBAAYP348bt++jVOnTuHixYu4efOmVHJS3NLT0vHh9WfYVbCQKretYIF3Lz7+cNs/px7CimE7sGv+Sbx+8v6HdX2uBaB8bQcoqZS+O0eF1UaWTqb48Poz3r/M2MeXj9F46RsCh8pWBXsCRaQwX0vXjz6EuqYqqni4FGjMBUkkEmHh/B7YvusaXgX9+HyBjDsg7dtWx7t3EQgLiwIAKCkqIDU1HYIgSOolJ6cCAKpUss12XxoaKoiOTsh2vYtzGSgqKuDu3eeSss/hMXj5KgyVKloDACpWsEZMbCL8H7+R1PHzf4OY2ERJHSIiKl1+uq5QFStWxIwZMwAAU6dOxeLFi2FgYIBBgwYBAGbNmoWNGzfCz88PtWrVgqKiIubOnSvZ3sbGBnfu3MGhQ4fQpUsXAMD8+fMxYcIEjBkzRlKvevXqUsft0aMHBgwYILXcr18/DB8+HEDGB/579+5h+fLl8PDwyDL2hg0bSi3/+eef0NXVxfXr19G6dWs0a9YM6urqOH78OHr37g0A2LdvH9q0aQMtLS3ExsZi586d2LdvHxo1agQA2L59O8zMsu5vXhwSYpIgiAVoaKtKlWtoq+FV9Nsst9HQUUPrge4wszVEWmo6/G4+x64FJ9FvZntYOWc+t/cvP+LT20i0HZx1O5d0hdVG5es4ID4mEdvmHAcAiNPFqNa4HOq1q1K4J1RICqud3jwPhc+1AAxd1KXQz0EWA/p5ID1NnONYh66/1MH4Ma2hpqaMoNcfMWj4n5K7G/c9AzFxfFv06+OOPftuQk1VCaNHtgQAGGTTlaxiBSs0a1IRI0ZvzfaYBvqaSElJQ0xsolR5REQsDPQ1JXUiI+MybRsZGQd9/ey7sRERUcn10yUWFSpUkPwuLy8PfX19uLp+u2VvbGwMAPj06dvgxU2bNuGvv/5CSEgIEhMTkZKSgkqVKknqffjwQfJBPTvVqlWTWg4ICMDgwYOlyurWrZtlN6uvPn36hFmzZuHKlSv4+PEj0tPTkZCQgDdvMr7RU1RUxC+//IK9e/eid+/eiI+Px8mTJ7Fv3z4AQFBQEFJTU1GjRg3JPrW1teHk5PTD2JOTk5GcnCxVlpqSBsVCHdArkloSBOH7IgkDM10YmH0bWGvhaIKYyDjcOe2bZWLhfS0ARhZ6MLc3LtCIi17BtlHw0/e4ecILrQY0gLm9MSI/RuP8zlvQOPYQbh2rZb3jUqHg2ik5MQXH119Cm0HuUNNSzXonJYCLcxn06l4fXXqsyrHumXPeuHvvBQwNtdC3tztWLOmN3v3/QEpKGl4FfcSM2fsxaXxbjBnZEmKxgL0HbiI8PEYyDuLf7GyNsWZlf2zafBF377/Ic9wikUjq7si/f/9WB0DmYiIiKgV+usTi+0HTIpFIqkwkyvjEIRZnDPQ9dOgQxo0bhxUrVqB27drQ1NTEsmXLcP/+fQCAqmruPlyoq6tnKvt6rK8EQchU9m/9+vXD58+fsXr1alhZWUFZWRm1a9dGSkqKpE7Pnj3h5uaGT58+4eLFi1BRUUGLFi0k+8/uuD+yaNEiqbs2ANBxcDN0GlLws+CoaalAJCdC3HfdKOJjEqGhpZbr/ZjbG8P/VuYPNqnJqXhy5yXcf6mexValQ2G10dVDD1ChvhOqNMzo3mNsqY/UpFT8/dd1NGhfFSK57F+bJVFhtNOXjzGI+hyL/cvOStZ//f8zr+dGjFzZA3rG2gUQvWyqVLaBnp4GLpydISlTUMiYhalXjwZo3nqBpDwuLglxcUl48zYcj/xCcPv672jk4Ypz//gAAM6e98HZ8z7Q19NAQmIKIAB9errh/fsIqWPa2hhj65/DcPT4fWzeeumH8YVHxEJJSQFamqpSdy309DTg6xcsqZPVnQldXQ1ERMbmuU2IiKj4/XSJRV7dvHkTderUkXRZAoBXr15JftfU1IS1tTUuX76cbRemrDg7O+PWrVvo06ePpOzOnTtwdnb+YSwbNmxAy5YZXRHevn2L8PBwqTp16tSBhYUFDh48iHPnzuGXX36BklLG7DZ2dnZQVFTEgwcPYGGR0e88JiYGgYGBcHNzy/a4U6dOxfjx46XKjj/dkutzzQt5BXmY2RgiyO8tnKt/68Md5P8OTlWtc72fsOBwaOhkTuae3HuFtLR0VKj347s0JVlhtVFqSlqmpFMkJwcIAgQIEGX3NX8JVRjtZGCmg2FLu0qtv3LoAVISUyQDw0uCv8944d79QKmyTesH4/QZL5w49eCH24ogglIWdyMj/t8tqX27GkhOScXde9+SUjvbjKTi5OmHWLf+XKZtv/c04B1SU9NQu5Yj/rn4CEBG1yp7OxOsXHMaAPDILxhamqooX84Cj59kdF1zLW8JLU1V+D4KzvEYRERU8vznEwt7e3vs2rUL//zzD2xsbLB79254enrCxsZGUmfOnDkYOnQojIyM0KJFC8TGxuL27dsYNWpUtvudNGkSunTpgipVqqBRo0b4+++/cezYMVy6lP03ffb29ti9ezeqVauGmJgYTJo0KdMdE5FIhB49emDTpk148eKF1GBwTU1N9O3bF5MmTYKenh6MjIwwe/ZsyMnJ/fBOibKyMpSVlaXKCrMbVK1WFXF8/WWY2RqhjKMxvC4/RXR4LKo1Lg8AuLT/LmK/xKPD8MYAMp69oGOoCcMyekhPE8Pv1nMEPAhCl3HNMu3b52oAylazgZpm5ulDS5PCaCPHKla4e/YRTK0NMrpChUXj6uH7cKpqnWlWpNKioNtJQUkBRv+fDekrFbWMxP378sKmqqoESwsDybK5uR6cHM0QHZOAsLCoTIOn09LSER4Rg+CQjFm+ypjroVnTSrh77wUiv8TB2EgbA/o2RHJyKm7eCpBs171rXfg+CkZCQjJq13LC+DGtsXrdGcmzJOxsjbF18zDcvfcCu/Zcl9xlEKeL8SUqHkDG7FR/bRqGabP24fGTt4iLS8KxEw8wcVxbREUnIDo6ARPGtUHgy1Dc+38XqtevP+HW7QDMmdkF8xYcBgDMnvELrt14IjkHIiIqXf7zicXQoUPh6+uLrl27QiQSoXv37hg+fLjUlLR9+/ZFUlISVq1ahYkTJ8LAwACdO3f+4X7bt2+PNWvWYNmyZRg9ejRsbGywfft2uLu7Z7vNtm3bMHjwYFSuXBmWlpZYuHAhJk6cmKlez549sXDhQlhZWaFu3bpS61auXImhQ4eidevW0NLSwuTJk/H27VuoqJScD9rlazsgMTYZ1489RFxUPIws9NFzSmvoGGZ8YImLSkB0+LdBnelpYlzYewexkfEZH/zK6KHH5FaZZjOKCI3Cm+eh6DW1TZGeT2EojDZq0KEaABGuHLqP2Mh4qGmpwrGKNRp1rVnUp1dgCuu1VBKUc7HA9i3f7qROntAOAHDylCdmzDmQ4/bJyWmoWtkWvXs0gJaWKiIi4uDlHYTe/dch8su3NilfzhLDhzSDmpoyXgd/wryFR3D6jJdkfdMmFaGvp4nWLauidcuqkvL3HyIlXa4UFORhY2MEFZVvzwZZuuIk0tPFWL64N5SVFXHfMxAjZx+QGrsxZfpeTJ3cAX+uHwIg4wF5Cxcfy2tTERFRCSEScuqAT6VafHw8zM3NsWLFCvz666+53m6fd/aDzImoYC0a+CbnSgR/7xXFHUKRSErPfipforzwi/TKuRJRLtQwrJ+rev/5OxY/Gx8fHzx79gw1atRAdHQ05s2bBwBo165dMUdGRERERD8zJhY/oeXLl+P58+dQUlJC1apVcfPmTRgYGOS8IRERERFRPjGx+MlUrlwZXl689UlERERERat0TgVDREREREQlCu9YEBERUZHyeuiFHdt2IeDJU3z+HI5Va1eiYeNvz4qaOW0WTp34W2ob1wqu2HNgl1TZI99HWLdmPfz9/KGooACnsk5Y/+cfkpkQR48Yg+cBLxAZGQktLS3UrF0TYyeMhpGRUeGfJBWJU7vP4uF1b4SGhEJRWQkOrnboNqwzTC1NJHX+XLANt87dkdrOzsUWczZPy7Q/QRCwfOIa+N1/jDELR6Bag8qSdSd3nobvXX+8CXwLBUV5/Hl+XeGdWCnFxIKIiIiKVGJCIpycHNGuQ1tMGJN5WnUAqFuvDuYtmCtZVlRUlFr/yPcRhg8eiQGD+uO3aVOgqKiAF89fSD2Xp3qN6hg4+FcYGBjg06dPWLlsFSaOnYRd+3YWzolRkXvm8xyNO3rAtqw10tPFOLLlOJaMW4nFe36Hiuq3Z3RVqFkeg6b1lywrKMpnub/zhy4iu+fFpqWlo4ZHVTiUs8X1M7cK9Dx+FkwsiIiIqEjVa1AP9RrU+2EdJSUlGBhmP/HIssUr0L1XN/w6aICkzMpa+pk0vfv2kvxuZm6GAQP7Y+yo8UhNTc2UqFDpNHnlOKnlQVP7Y0SbcQh+HoKylRwl5QpKCtDR1/7hvkIC3+L8wYuYu2UGRrWbkGl9p18zZti8cfZ2AUT+c2JiQURERCXOQ8+HcK/XEJqamqhWvSpGjhkJfX09AEBERCT8/fzRsnUL9OnRF2/fvoONjTVGjhmJKlUrZ7m/6KhonDl9DhUrV2RS8RNLjM94Doy6lrpU+TOf5xjeehzUNdRQtrIjOg/uAG1dLcn65KRkbJi7GX3G9cgxAaHsMbEgIiKiEqVu/bpo0qwJTM1M8f7de2xYuwGD+g/GgSP7oKSkhPfv3gEANq3/E+MnjYNTWSecPnUagwcMwdGTh6XuXKxasQYH9h1AUmISKlR0xbqNa4vrtKiQCYKAvesOwbGCAyxszSXlFWuVR02PatA30cfnD59x9K+TWDR6OX7fOhOKShlJ5t61B+FQ3g5V62edmFLucFYoIiKiQvD27VsMGDDgh3WSk5MRExMj9ZOcnFxEEZZczVs0QwO3+nBwsIe7hxvWb/4DIcEhuHH9JgBALBYDADp36YT2HdvB2aUsJv02EdY21jhx7KTUvvoN6IODRw9g018bIScvjxm/zYQgCEV+TlT4dq7ch7ev3mHEnEFS5bUa1UClOhVgYWuOKvUqYdLyMQh7+xG+d/0AAN63fPHU+xl6je5WHGH/VJhYEBERFYLIyEjs3PnjQcKLFi2Ctra21M+yxcuLKMLSw9DQEGZmpngT8gYAYGBoCACwtbOVqmdja4Ow0DCpMl1dXVhbW6F2nVpYunwxbt64Bb9HfkUTOBWZXav2wee2L6aunQg9I70f1tUx0IGBiT4+vv0EAHjq9Qyf3n/GkBaj0ddtMPq6DQYArJ2xAQtGLi302H8m7ApFRESUD6dOnfrh+qCgoBz3MXXqVIwfP16qTFBIlymun1FUVBTCwj7C8P+Duc3NzWBoZIjg4GCpeiHBIahXv262+/l6pyIlJbXQYqWiJQgCdq3aB68bPpi2bhKMzAxz3CY2Og6RnyIlYyla92oBtzb1pepM6zMbPUd1ReW6FQsl7p8VEwsiIqJ8aN++PUQi0Q+71YhE2cxb+X/KyspQVlaWKktKTyiQ+EqyhPgEvHnzVrL8/v17PAt4Dm1tLWhra2Pj+k1o3LQRDAwN8eH9B6xbvQ46ujpo2LghgIx27TegLzb+sQlOTo5wKuuEUyf/RvDrYKxYvQwA4O/3GI/9H6NylcrQ0tLEu3fvsWHdRlhYWKBipQrFct5U8Hau2Iu7l+5j7KKRUFFTQVRENABATUMVSspKSEpIwrFtp1DdvSp09LURHhqOQ5uPQ0NbE1XdqgAAdPS1sxywrW+sL5WohIdFID42HhEfIyFOFyMkMOMOmrG5EVTUVIrgbEs+JhZERET5YGpqivXr16N9+/ZZrvf19UXVqlWLNqhS4smTpxjY71s/+OVLVgAA2rZvg+mzpiEw8CX+PnUasTGxMDQ0QPWa1bF0xRKoq3+b6adXn55ITk7GsiUrEB0dDScnR2z6ayMsLC0AACoqyrh86Qo2/rEJiYmJMDA0QN16dbBkxWIoKSkV7QlTobl84hoAYOGoZVLlg6b1R4OWdSEnL4d3Qe9x6/xdJMQlQEdfG85VymLk3CFQzWMycHTrSakH7c3oPw8AMG3tRDhXKSvbifwkRAJHMFEW9nmvKe4QiP4zFg18U9whlAr+3iuKOwQpbdu2RaVKlTBv3rws1z969AiVK1eWDDTOrf/CHQsqGn6RXsUdAv0kahjWz7kSeMeCiIgoXyZNmoT4+Phs19vb2+Pq1atFGBERUfFiYkFERJQP9ev/+Bs8dXV1uLm5FVE0RETFj9PNEhERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzJhYEBERERGRzH6qxMLa2hqrV6/Odf3g4GCIRCL4+voWWkz/tmPHDujo6BTJsYiIiIiIipJCcQdQkDw9PaGurl6g+9yxYwfGjh2LqKioAt3vf53nhce4c9oHsVEJMCqjh2Z96sKqrFm29dNS03H9mCf8b71AXFQCtPQ0UL99VVT2cJbUSYpPxuWD9/HMMwiJ8cnQNdRE01514VDZqihOqcDlpY2Cn77Hzt9PZiofsbw7DMx1Jcs/WxsBhdNO984+wsNLTxAdHgs1TRU417RD4261oKBUdJfMqlVs0a+PO1ycy8DIUBtjxm/HlWuPs6w7a3pn/NKpNpYsP4E9+25Kldeq4QBDQ20kJCbj0aNgrFp7Bq+DP0nqOJc1x7jRrVGunAXE6WJcuuKHpStOITExBQDg6GCKX/s3QpVKNtDRUceH0EgcOnIXe/ffzBTHvykqymPiuLZo0awylFUUcP/BSyxYdBQfP0VL6mhpquK3ye3h3qAcAODajSdYtOQ4YuOS8t1uRERUfH6qxMLQ0LC4Q6BceHw3EOd33UKrAQ1g4WQCr0tPsXfxaYxY3h3aBppZbnNkzT+Ii05E28Ee0DPRRnx0IsRisWR9elo6di88BXUtVfwythm09DQQExEHJVXFojqtApWfNgKAkSt7QFlVSbKspqUi+f1nayOgcNrJ79YLXDpwD+2GeMDC0QQRoVE4sfEKAKB5n3qFdzLfUVVRwosXH3DilCdWL++Xbb2G7uXhWt5S6gP7V08D3uHMOW+Ehn6BtrYahg1phj/XD0bzNgsgFgswNNDClo1Dcf6CLxYuOQZ1dRVMmdgO8+d2w4TJuwAALi4W+PIlDlNn7EXYxyhUqmiNWdN/gVgsxv6Dt7ONa8rE9nBv4ILJU3cjKjoBE8e3wR9rfkXXnqsgFgsAgCULe8LYSAfDRm0BAMye/gsWzu+BUWO3ydByRERUXIqtK9Tff/8NHR0dyYdDX19fiEQiTJo0SVJnyJAh6N69u2T5zp07aNCgAVRVVWFhYYHRo0cjPj5esv77rlDPnj1DvXr1oKKiAhcXF1y6dAkikQgnTpyQiiUoKAgeHh5QU1NDxYoVcffuXQDAtWvX0L9/f0RHR0MkEkEkEmHOnDkAgJSUFEyePBnm5uZQV1dHzZo1ce3aNan97tixA5aWllBTU0OHDh0QERGRY7tMmTIFjo6OUFNTg62tLWbOnInU1FQAwPPnzyESifDs2TOpbVauXAlra2sIQsab9alTp+Dg4ABVVVV4eHhg586dEIlEJeauy70zj1DZwxlVGrrA0FwPzfvWg7a+BjwvZv1t7EvfNwgO+ICeU1rB1tUCOoZaMLc3hoWjqaSOz9UAJMYlo+uEFrB0MoWOoSYsy5rCxMqgqE6rQOW1jb5S11KFho6a5EdO7tt/8Z+tjYDCaad3gWGwdDSBa11H6Bhqwa6CJcrXcUBo0OfCPh0pt+48w7oN53H5in+2dYwMtTBtSgf8Nn0v0tLSM60/cuwevLyD8CH0CwKevccfG87B1FQXZmZ6AAC3Bi5IS0vHgsXHEBzyGU+evsWCxcfQtHFFWFjoAwBOnHyAxctO4KF3EN69j8Tps944+bcnGjV0zTYuDQ0VdGxfA8tW/Y17DwLx7Pl7TJ2+Dw72pqhV0xEAYGNjhHp1nTH790N45BeCR34hmDP/ENwblIO1Fb8kIiIqjYotsWjQoAFiY2Ph4+MDALh+/ToMDAxw/fp1SZ1r167Bzc0NAODv749mzZqhY8eO8PPzw8GDB3Hr1i2MHDkyy/2LxWK0b98eampquH//PjZv3ozp06dnWXf69OmYOHEifH194ejoiO7duyMtLQ116tTB6tWroaWlhdDQUISGhmLixIkAgP79++P27ds4cOAA/Pz88Msvv6B58+YIDAwEANy/fx8DBgzA8OHD4evrCw8PD8yfPz/HdtHU1MSOHTvw9OlTrFmzBlu2bMGqVasAAE5OTqhatSr27t0rtc2+ffvQo0cPiEQiBAcHo3Pnzmjfvj18fX0xZMiQbM+7OKSnpePD68+wq2AhVW5bwQLvXnzMcpvnXq9hZmuE23/7YOXwnVg3bi8u7LmN1JS0b3W8g1HGwRhnt9/E8iHbsWHSAdw84SV1V6O0yE8bffXn1ENYMWwHds0/iddP3kut+5naCCi8drJ0MsWH15/x/mXGPr58jMZL35AS111MJBJh4fwe2L7rGl4F/fh8gYw7IO3bVse7dxEIC4sCACgpKiA1NV3ypQQAJCdnfJFRpZJttvvS0FBBdHRCtutdnMtAUVEBd+8+l5R9Do/By1dhqFTRGgBQsYI1YmIT4f/4jaSOn/8bxMQmSuoQEVHpUmxdobS1tVGpUiVcu3YNVatWxbVr1zBu3DjMnTsXsbGxiI+Px4sXL+Du7g4AWLZsGXr06IGxY8cCABwcHLB27Vq4ublh48aNUFFRkdr/hQsX8OrVK1y7dg0mJiYAgAULFqBJkyaZYpk4cSJatWoFAJg7dy7KlSuHly9fomzZstDW1oZIJJLsAwBevXqF/fv34927dzAzM5Ps4/z589i+fTsWLlyINWvWoFmzZvjtt98AAI6Ojrhz5w7Onz//w3aZMWOG5Hdra2tMmDABBw8exOTJkwEAPXv2xB9//IHff/8dAPDixQt4eXlh166MbgubNm2Ck5MTli1bBiAjGXn8+DEWLFiQw1+kaCTEJEEQC9DQVpUq19BWw6vot1lu8+VTDN48D4WCojy6jm+OhNgknNl2A4lxyWg3tKGkzuvPsXCt64AeU1ohMiwaZ7ffgDhdDLdO1Qv9vApSftpIQ0cNrQe6w8zWEGmp6fC7+Ry7FpxEv5ntYeWc8Rr9mdoIKLx2Kl/HAfExidg25zgAQJwuRrXG5VCvXZXCPaE8GtDPA+lp4hzHOnT9pQ7Gj2kNNTVlBL3+iEHD/5Tc3bjvGYiJ49uiXx937Nl3E2qqShg9siUAwCCbrmQVK1ihWZOKGDF6a7bHNNDXREpKGmJiE6XKIyJiYaCvKakTGRmXadvIyDjo62ffjY2IiEquYh1j4e7ujmvXrmH8+PG4efMm5s+fj6NHj+LWrVuIioqCsbExypYtCwDw8vLCy5cvpb6tFwQBYrEYr1+/hrOzs9S+nz9/DgsLC6mEoEaNGlnGUaFCBcnvpqYZ3Ws+ffokOfb3vL29IQgCHB0dpcqTk5Ohr5/RfSAgIAAdOnSQWl+7du0cE4sjR45g9erVePnyJeLi4pCWlgYtLS3J+m7dumHSpEm4d+8eatWqhb1796JSpUpwcXGRnHf16tIfErM773/HnZycLFWWmpIGxUIdqCqSWhIE4fsiqXUiAB1HNoaKmjIAoFnvOji0+h+0HNAAikoKEMQC1LVU0WaQO+Tk5GBma4TYL/G4c9q3VH5ozpD7NjIw04WB2bfBxxaOJoiJjMOd076SD8w/ZxsBBd1OwU/f4+YJL7Qa0ADm9saI/BiN8ztvQePYQ7h1rFZoZ5EXLs5l0Kt7fXTpsSrHumfOeePuvRcwNNRC397uWLGkN3r3/wMpKWl4FfQRM2bvx6TxbTFmZEuIxQL2HriJ8PAYyTiIf7OzNcaalf2xafNF3L3/Is9xi0Qiqbsj//79Wx0AmYuJiKgUKPbEYuvWrXj06BHk5OTg4uICNzc3XL9+HV++fJF0gwIyujYNGTIEo0ePzrQfS0vLTGWCIEAkyubTxXcUFb8NXv26zY+6h4jFYsjLy8PLywvy8vJS6zQ0NCTHz6t79+6hW7dumDt3Lpo1awZtbW0cOHAAK1askNQxNTWFh4cH9u3bh1q1amH//v0YMmSIZH1W551TLIsWLcLcuXOlyjoOboZOQ1rk+RxyoqalApGcCHHfdaOIj0mEhpZaltto6qhDU09dklQAyJjBRwBiIuKgb6oDTR11yCnISfWVNzDXRVxUAtLT0iGvIJ/Vrkuk/LRRVsztjeF/69uHv5+pjYDCa6erhx6gQn0nVGmYkawbW+ojNSkVf/91HQ3aV4VILnfXlcJUpbIN9PQ0cOHstzucCgoZszD16tEAzVt/u0MZF5eEuLgkvHkbjkd+Ibh9/Xc08nDFuX8yuqGePe+Ds+d9oK+ngYTEFEAA+vR0w/v30mPCbG2MsfXPYTh6/D42b730w/jCI2KhpKQALU1VqbsWenoa8PULltTJ6s6Erq4GIiJj89wmRERU/Io1sfg6zmL16tVwc3ODSCSCm5sbFi1ahC9fvmDMmDGSulWqVMGTJ09gb2+fq32XLVsWb968wcePH2FsbAwgYzravFJSUkJ6uvSgyMqVKyM9PR2fPn1C/fr1s9zOxcUF9+7dkyr7fvl7t2/fhpWVldSYiJCQkEz1evbsiSlTpqB79+549eoVunXrJllXtmxZnD17Vqr+w4cPf3jcqVOnYvz48VJlx59u+eE2+SWvIA8zG0ME+b2Fc/VvfbiD/N/Bqap1lttYOJngyf1XSElKhZJKRhIYEZoxoF5LX0NSx/92IASxIPngFxEaBQ0dtVL3gTk/bZSVsOBwaOh8m375Z2ojoPDaKTUlLVNyLpKTAwQBAgSIsrsdUoT+PuOFe/cDpco2rR+M02e8cOLUgx9uK4IISlncjYz4f7ek9u1qIDklFXfvfUu27GwzkoqTpx9i3fpzOcb3NOAdUlPTULuWI/65+AhARtcqezsTrFxzGgDwyC8YWpqqKF/OAo+fZHRdcy1vCS1NVfg+Cs7xGEREVPIU6wPyvo6z2LNnj2QsRYMGDeDt7S01vgLImC3p7t27GDFiBHx9fREYGIhTp05h1KhRWe67SZMmsLOzQ9++feHn54fbt29LPrDn9k4GkDHOIS4uDpcvX0Z4eDgSEhLg6OiInj17ok+fPjh27Bhev34NT09PLFmyRPKhfvTo0Th//jyWLl2KFy9e4I8//sixG5S9vT3evHmDAwcO4NWrV1i7di2OHz+eqV7Hjh0RExODYcOGwcPDA+bm5pJ1Q4YMwbNnzzBlyhS8ePEChw4dwo4dO3543srKytDS0pL6KcxuULVaVYT31QD4XA3A5/eROL/rFqLDY1GtcXkAwKX9d3F8w7dvRF3rOkJNQxknN13B53eRCAn4gIt776CSe1lJnNWalENiXBLO7byFiNAovPAOxq0T3qjetHyhnUdhymsb3Tv7CM88gxARGoVPbyNxaf9dBDwIQo1m387/Z2sjoHDaybGKFR5eeozHdwLx5VMMXvm9xdXD9+FU1Vrqbk9hU1VVgpOjGZwcM7pomZvrwcnRDCYmOoiOTsDLV2FSP2lp6QiPiEFwSMbsVWXM9fBr/4ZwcS4DExMdVKxghRVL+iA5ORU3bwVIjtO9a104lzWHlaUBunWpi2mTO2DNurOSZ0nY2Rpj6+ZhuHv/BXbtuQ59fU3o62tC91/JmJGhFk4dnYLy5TIG0sfFJeHYiQeYOK4tatZwQFkncyya3xOBL0Nx7/9dqF6//oRbtwMwZ2YXVHC1RAVXS8yZ+Quu3XgiOQciIipdiv05Fh4eHvD29pYkEbq6unBxccGHDx+kxk1UqFAB169fx/Tp01G/fn0IggA7Ozt07do1y/3Ky8vjxIkTGDhwIKpXrw5bW1ssW7YMbdq0yTTQ+0fq1KmDoUOHomvXroiIiMDs2bMxZ84cbN++HfPnz8eECRPw/v176Ovro3bt2mjZMmPgY61atfDXX39J6jdu3BgzZsyQDLrOSrt27TBu3DiMHDkSycnJaNWqFWbOnCmZ4vYrLS0ttGnTBocPH8a2bdLzvdvY2ODIkSOYMGEC1qxZg9q1a2P69OkYNmwYlJWVURKUr+2AxNhkXD/2EHFR8TCy0EfPKa2hY5jRLSIuKgHR4d8GdSqpKKL3tLY4t+MmNk8/AjUNZbjUskfDrjUldbT1NdFrahv8s/s2Nk45CC1dddRsUQF121Yu8vMrCHlto/Q0MS7svYPYyHgoKCnAqIweekxuJTWT0c/WRkDhtFODDtUAiHDl0H3ERsZDTUsVjlWs0ehfr7eiUM7FAtu3DJcsT57QDgBw8pQnZsw5kOP2yclpqFrZFr17NICWlioiIuLg5R2E3v3XIfLLtzYpX84Sw4c0g5qaMl4Hf8K8hUdw+oyXZH3TJhWhr6eJ1i2ronXLqpLy9x8iJV2uFBTkYWNjBBWVb88GWbriJNLTxVi+uDeUlRVx3zMQI2cfkBq7MWX6Xkyd3AF/rs/oznntxhMsXHwsr01FREQlhEjIz2CAUur27duoV68eXr58CTs7u+IOp8gsWLAAmzZtwtu3Wc+Uk5V93msKMSIi+rdFA9/kXIng770i50o/gaT07KfyJcoLv0ivnCsR5UINw6y7/n+v2O9YFKbjx49DQ0MDDg4OePnyJcaMGYO6dev+9EnFhg0bUL16dejr6+P27dtYtmxZts/7ICIiIiIqCD91YhEbG4vJkyfj7du3MDAwQOPGjaVmWPpZBQYGYv78+YiMjISlpSUmTJiAqVOnFndYRERERPQT+091haLcY1cooqLDrlC5w65QRHnDrlBUUHLbFapYZ4UiIiIiIqKfAxMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSGRMLIiIiIiKSmUgQBKG4gyD6keTkZCxatAhTp06FsrJycYdTYrGdcsY2yh22E5VkfH1SQeFrqeAxsaASLyYmBtra2oiOjoaWllZxh1NisZ1yxjbKHbYTlWR8fVJB4Wup4LErFBERERERyYyJBRERERERyYyJBRERERERyYyJBZV4ysrKmD17NgdW5YDtlDO2Ue6wnagk4+uTCgpfSwWPg7eJiIiIiEhmvGNBREREREQyY2JBREREREQyY2JBJd61a9cgEokQFRVVoHX/6+bMmYNKlSpJlvv164f27dsXWzyCIGDw4MHQ09ODSCSCr69vscVCREREecfEgkq8OnXqIDQ0FNra2gVal0qW8+fPY8eOHTh9+jRCQ0MRExODNm3awMzMDCKRCCdOnCjuEKkAWFtbY/Xq1cUdBpViGzZsgI2NDVRUVFC1alXcvHmzuEOiUujGjRt8jykETCyoUKWkpMi8DyUlJZiYmEAkEhVo3ZKsINqttHn16hVMTU1Rp04dmJiYID4+HhUrVsQff/xR3KFl67/4d8ovthUVhIMHD2Ls2LGYPn06fHx8UL9+fbRo0QJv3rwp7tColCkN7zGlERMLyhN3d3eMHDkSI0eOhI6ODvT19TFjxgx8nVzM2toa8+fPR79+/aCtrY1BgwYBAO7cuYMGDRpAVVUVFhYWGD16NOLj4yX7TU5OxuTJk2FhYQFlZWU4ODhg69atADJ3bwoJCUGbNm2gq6sLdXV1lCtXDmfPns2yLgAcPXoU5cqVg7KyMqytrbFixQqpc7K2tsbChQsxYMAAaGpqwtLSEps3by6sJszS13YdP348DAwM0KRJEzx9+hQtW7aEhoYGjI2N0bt3b4SHh0u2EYvFWLJkCezt7aGsrAxLS0ssWLBAsn7KlClwdHSEmpoabG1tMXPmTKSmphbpeeVWv379MGrUKLx58wYikQjW1tZo0aIF5s+fj44dO+ZpX3PmzIGlpSWUlZVhZmaG0aNHS9b96HUGANevX0eNGjWgrKwMU1NT/Pbbb0hLS5Osz+rvBCDHv1VBO3LkCFxdXaGqqgp9fX00btwY8fHxcHd3x9ixY6Xqtm/fHv369ZMsW1tb4/fff0ePHj2goaEBMzMzrFu3TmobkUiEjRs3okWLFlBVVYWNjQ0OHz4sVcff3x8NGzaUxDB48GDExcVJ1n/tWrdo0SKYmZnB0dER7u7uCAkJwbhx4yASiUr9FwBU9FauXIlff/0VAwcOhLOzM1avXg0LCwts3LixuEOjUia/7zH0Y0wsKM927twJBQUF3L9/H2vXrsWqVavw119/SdYvW7YM5cuXh5eXF2bOnAl/f380a9YMHTt2hJ+fHw4ePIhbt25h5MiRkm369OmDAwcOYO3atQgICMCmTZugoaGR5fFHjBiB5ORk3LhxA/7+/liyZEm2db28vNClSxd069YN/v7+mDNnDmbOnIkdO3ZI1VuxYgWqVasGHx8fDB8+HMOGDcOzZ89kb6w8+Nqut2/fxuLFi+Hm5oZKlSrh4cOHOH/+PD5+/IguXbpI6k+dOhVLlizBzJkz8fTpU+zbtw/GxsaS9ZqamtixYweePn2KNWvWYMuWLVi1alWRnlNurVmzBvPmzUOZMmUQGhoKT0/PfO3nyJEjWLVqFf78808EBgbixIkTcHV1laz/0evs/fv3aNmyJapXr45Hjx5h48aN2Lp1K+bPny91jH//nf7880+Ehobm+LcqSKGhoejevTsGDBiAgIAAXLt2DR07dkReZg5ftmwZKlSoAG9vb0ydOhXjxo3DxYsXperMnDkTnTp1wqNHj9CrVy90794dAQEBAICEhAQ0b94curq68PT0xOHDh3Hp0iWp/9MAcPnyZQQEBODixYs4ffo0jh07hjJlymDevHkIDQ1FaGio7A1C/xkpKSnw8vJC06ZNpcqbNm2KO3fuFFNURCRFIMoDNzc3wdnZWRCLxZKyKVOmCM7OzoIgCIKVlZXQvn17qW169+4tDB48WKrs5s2bgpycnJCYmCg8f/5cACBcvHgxy2NevXpVACB8+fJFEARBcHV1FebMmZOruj169BCaNGkiVWfSpEmCi4uLZNnKykro1auXZFksFgtGRkbCxo0bf9ASBcvNzU2oVKmSZHnmzJlC06ZNpeq8fftWACA8f/5ciImJEZSVlYUtW7bk+hhLly4VqlatKlmePXu2ULFiRcly3759hXbt2uX7HGS1atUqwcrKKst1AITjx4/nuI8VK1YIjo6OQkpKSqZ1Ob3Opk2bJjg5OUm9ttevXy9oaGgI6enpgiBk/jsJQs5/q4Lm5eUlABCCg4MzrXNzcxPGjBkjVdauXTuhb9++kmUrKyuhefPmUnW6du0qtGjRQrIMQBg6dKhUnZo1awrDhg0TBEEQNm/eLOjq6gpxcXGS9WfOnBHk5OSEsLAwQRAyXk/GxsZCcnKy1H6srKyEVatW5fp8ib56//69AEC4ffu2VPmCBQsER0fHYoqKfga5fY+hnPGOBeVZrVq1pLow1K5dG4H/a+/eg6Ks+jiAf9eVXVZuityUAQyBhAYQpIzwFfEyaFZqjVcyDLQQMQVNbLxsjHdrMlFkGJSLNul4AVPRvBDoxEVSwRsrGKBoo0FqFg4Isuf9w2HHleXWYmh+PzM7wz7nPOec5zwPz+7vec559upVNDY2AgB8fHy08p89exYpKSkwNjbWvAIDA6FWq1FRUYGioiJIpVL4+/u3q/7PPvsMK1euhJ+fH5RKJS5cuNBiXpVKBT8/P61lfn5+Wu0FAA8PD83fEokENjY2qKqqald7OsuT/Xb27FlkZWVp9dmAAQMAPJ6LoFKp8PDhQ4wYMaLF8vbu3YshQ4bAxsYGxsbGWLZs2X9qHPLq1au1+qeyshITJ05EbW0tHB0dMWvWLKSnp2uGMrV1nKlUKvj6+mod235+fqipqcHNmzc1y3Qd363tq87m6emJESNGwN3dHRMnTkRiYiLu3bvXoTJ8fX2bvW+6G9GePCqVCp6enjAyMtKk+/n5Qa1Wo6SkRLPM3d0dMpmsQ20jasvTQ+iEEBxWR/ScYGBBne7JLxvA47kAn376KYqKijSv8+fP4+rVq+jfvz8UCkWHyp85cybKy8sxffp0XLx4ET4+Ps3GiDfR9YEjdAwZMTAw0HovkUigVqs71C59PdlvarUa7777rlafFRUV4erVq5q5Kq3Jz8/HlClTMGbMGBw6dAiFhYVYsmTJf2oCbVhYmFbf9O3bF3Z2digpKUFcXBwUCgXCw8MxdOhQNDQ0tNlnrR0rTy7XdXy3tq86m1QqxfHjx3HkyBG4ublh06ZNePXVV1FRUYFu3bo1O77bO6+mPV/MmvK09kWutb4i0oeFhQWkUilu376ttbyqqkprGCgRdR0GFtRh+fn5zd47OztDKpXqzO/t7Y3Lly/Dycmp2Usmk8Hd3R1qtRonT55sdxvs7OwQFhaGtLQ0LFiwAImJiTrzubm54eeff9ZalpubCxcXlxbb+zxo6rN+/fo16zMjIyM4OztDoVAgMzNT5/o5OTlwcHDAkiVL4OPjA2dnZ1y/fv1f3opny9zcXKtfunfvDgBQKBR47733EBsbi+zsbOTl5eHixYttHmdubm7Izc3V+mKem5sLExMT2NrattiOtvbVsyCRSODn54eYmBgUFhZCJpMhPT0dlpaWWvMWGhsbcenSpWbr6/ofbrrL0p48bm5uKCoq0noAQ05ODrp16wYXF5dW2y6TybTuFhK1l0wmw6BBg5rNBzp+/DjeeuutLmoVET2JgQV12I0bNxAVFYWSkhLs3LkTmzZtwrx581rMHx0djby8PMyZM0dzJffAgQOYO3cugMdPqQkODkZISAj279+PiooKZGdnY/fu3TrLmz9/Po4ePYqKigqcO3cOP/30E1xdXXXmXbBgATIzM7FixQqUlpYiNTUVmzdvxsKFC/XviGdozpw5uHv3LqZOnYqCggKUl5fj2LFjCAkJQWNjIwwNDREdHY1FixZh+/btKCsrQ35+vuYJR05OTqisrMSuXbtQVlaG2NhYpKend/FWdUxNTY3m6j8AzbC51oZzpaSkYNu2bbh06RLKy8uxY8cOKBQKODg4tHmchYeH48aNG5g7dy6uXLmCH374AUqlElFRUejWreVTZVv7qrOdPn0aq1evxpkzZ1BZWYm0tDRUV1fD1dUVw4cPR0ZGBjIyMnDlyhWEh4fr/LHInJwcrF+/HqWlpYiLi8OePXua/Q/v2bMHSUlJKC0thVKpREFBgWZydlBQEAwNDREcHIxLly4hKysLc+fOxfTp09u8ctyvXz+cOnUKv/322zN9chb9N0VFRWHr1q1ISkqCSqVCZGQkKisrERYW1tVNoxfMP/mMoXbouukd9CLy9/cX4eHhIiwsTJiamopevXqJxYsXaya8tjQxs6CgQIwaNUoYGxsLIyMj4eHhIVatWqVJr62tFZGRkaJPnz5CJpMJJycnkZSUJIRoPiE7IiJC9O/fX8jlcmFpaSmmT58u/vjjD515hRBi7969ws3NTRgYGAh7e3vx1VdfabVNV5s9PT2FUqnUr7M6QNek29LSUjFhwgTRs2dPoVAoxIABA8T8+fM1fd3Y2ChWrlwpHBwcNNu2evVqzfqff/656N27tzA2NhaTJ08WGzZsEGZmZpr0533ydtO+fPr15ETkp6Wnp4vBgwcLU1NTYWRkJN58801x4sQJTXprx5kQQmRnZ4vXX39dyGQyYWNjI6Kjo0VDQ4MmXdd+EqLtfdWZiouLRWBgoLC0tBRyuVy4uLiITZs2CSGEqK+vF7Nnzxbm5ubCyspKrFmzRufk7ZiYGDFp0iTRo0cPYW1tLb799lutOgCIuLg4MWrUKCGXy4WDg4PYuXOnVp4LFy6IgIAAYWhoKMzNzcWsWbPE33//rUlv6XjKy8sTHh4eQi6XC34E0T8RFxcnHBwchEwmE97e3uLkyZNd3SR6Af2Tzxhqm0SIDjyjkF56w4YNw8CBA/nLuUQvqH79+mH+/PnNfu/iSRKJBOnp6Rg/fvy/1i4iInrxcSgUERERERHpjYEFERERERHpjUOhiIiIiIhIb7xjQUREREREemNgQUREREREemNgQUREREREemNgQUREREREemNgQUREREREemNgQURdRiKRYP/+/V3dDCKiF96XX36JgQMHat7PmDGjS37k8tq1a5BIJCgqKvrX66aux8CC6CU3Y8YMSCQShIWFNUsLDw+HRCLBjBkz2lVWdnY2JBIJ/vzzz3blv3XrFsaMGdOB1hIRvViazrESiQQGBgZwdHTEwoUL8eDBg2da78aNG5GSktKuvAwGqLMwsCAi2NnZYdeuXaitrdUsq6urw86dO2Fvb9/p9dXX1wMAbGxsIJfLO718IqLnyejRo3Hr1i2Ul5dj5cqV2LJlCxYuXNgsX0NDQ6fVaWZmhp49e3ZaeUTtwcCCiODt7Q17e3ukpaVplqWlpcHOzg5eXl6aZUIIrF+/Ho6OjlAoFPD09MTevXsBPL7iFRAQAADo1auX1p2OYcOGISIiAlFRUbCwsMCoUaMANB8KdfPmTUyZMgXm5uYwMjKCj48PTp8+DQA4f/48AgICYGJiAlNTUwwaNAhnzpx5lt1CRNQp5HI5bGxsYGdnh2nTpiEoKAj79+/XDF9KSkqCo6Mj5HI5hBC4f/8+PvnkE1hZWcHU1BTDhw/H+fPntcpcu3YtrK2tYWJigtDQUNTV1WmlPz0USq1WY926dXBycoJcLoe9vT1WrVoFAHjllVcAAF5eXpBIJBg2bJhmveTkZLi6usLQ0BADBgzAli1btOopKCiAl5cXDA0N4ePjg8LCwk7sOXrRdO/qBhDR8+Hjjz9GcnIygoKCAABJSUkICQlBdna2Js/SpUuRlpaG+Ph4ODs749SpU/jwww9haWmJIUOGYN++ffjggw9QUlICU1NTKBQKzbqpqamYPXs2cnJyIIRoVn9NTQ38/f1ha2uLAwcOwMbGBufOnYNarQYABAUFwcvLC/Hx8ZBKpSgqKoKBgcGz7RQiomdAoVBo7k78+uuv2L17N/bt2wepVAoAGDt2LMzNzXH48GGYmZkhISEBI0aMQGlpKczNzbF7924olUrExcXhf//7H3bs2IHY2Fg4Ojq2WOcXX3yBxMREbNiwAUOGDMGtW7dw5coVAI+DgzfeeAMnTpzAa6+9BplMBgBITEyEUqnE5s2b4eXlhcLCQsyaNQtGRkYIDg7GgwcP8M4772D48OH47rvvUFFRgXnz5j3j3qPnmiCil1pwcLAYN26cqK6uFnK5XFRUVIhr164JQ0NDUV1dLcaNGyeCg4NFTU2NMDQ0FLm5uVrrh4aGiqlTpwohhMjKyhIAxL1797Ty+Pv7i4EDBzarG4BIT08XQgiRkJAgTExMxJ07d3S208TERKSkpOi/wURE/6Kmc2yT06dPi969e4tJkyYJpVIpDAwMRFVVlSY9MzNTmJqairq6Oq1y+vfvLxISEoQQQvj6+oqwsDCt9MGDBwtPT0+d9f71119CLpeLxMREnW2sqKgQAERhYaHWcjs7O/H9999rLVuxYoXw9fUVQjw+b5ubm4sHDx5o0uPj43WWRS8H3rEgIgCAhYUFxo4di9TUVAghMHbsWFhYWGjSi4uLUVdXpxnG1KS+vl5ruFRLfHx8Wk0vKiqCl5cXzM3NdaZHRUVh5syZ2LFjB0aOHImJEyeif//+7dgyIqKudejQIRgbG+PRo0doaGjAuHHjsGnTJmzZsgUODg6wtLTU5D179ixqamrQu3dvrTJqa2tRVlYGAFCpVM0euOHr64usrCyd9atUKjx8+BAjRoxod5urq6tx48YNhIaGYtasWZrljx49gpmZmaZcT09P9OjRQ6sd9PJiYEFEGiEhIYiIiAAAxMXFaaU1DUnKyMiAra2tVlp7JmAbGRm1mv7ksCldvvzyS0ybNg0ZGRk4cuQIlEoldu3ahQkTJrRZNxFRVwoICEB8fDwMDAzQt29frWGcT58b1Wo1+vTpozUMtck/nYzd1vlVl6ZzfmJiIgYPHqyV1jRkS+gY1kovN07eJiKN0aNHo76+HvX19QgMDNRKc3Nzg1wuR2VlJZycnLRednZ2AKAZl9vY2Njhuj08PFBUVIS7d++2mMfFxQWRkZE4duwY3n//fSQnJ3e4HiKif5uRkRGcnJzg4ODQ5twwb29v3L59G927d292rm26i+zq6or8/Hyt9Z5+/yRnZ2coFApkZmbqTNd17ra2toatrS3Ky8ubtaNpsrebmxvOnz+v9UTB1tpB/30MLIhIQyqVQqVSQaVSaa5INTExMcHChQsRGRmJ1NRUlJWVobCwEHFxcUhNTQUAODg4QCKR4NChQ6iurkZNTU276546dSpsbGwwfvx45OTkoLy8HPv27UNeXh5qa2sRERGB7OxsXL9+HTk5Ofjll1/g6uraqdtPRNTVRo4cCV9fX4wfPx5Hjx7FtWvXkJubi6VLl2qehDdv3jwkJSUhKSkJpaWlUCqVuHz5cotlGhoaIjo6GosWLcL27dtRVlaG/Px8bNu2DQBgZWUFhUKBH3/8Eb///jvu378P4PGd4jVr1mDjxo0oLS3FxYsXkZycjG+++QYAMG3aNHTr1g2hoaEoLi7G4cOH8fXXXz/jHqLnGQMLItJiamoKU1NTnWkrVqzA8uXLsWbNGri6uiIwMBAHDx7UXL2ytbVFTEwMFi9eDGtra82wqvaQyWQ4duwYrKys8Pbbb8Pd3R1r166FVCqFVCrFnTt38NFHH8HFxQWTJk3CmDFjEBMT0ynbTET0vJBIJDh8+DCGDh2KkJAQuLi4YMqUKbh27Rqsra0BAJMnT8by5csRHR2NQYMG4fr165g9e3ar5S5btgwLFizA8uXL4erqismTJ6OqqgoA0L17d8TGxiIhIQF9+/bFuHHjAAAzZ87E1q1bkZKSAnd3d/j7+yMlJUVzzjc2NsbBgwdRXFwMLy8vLFmyBOvWrXuGvUPPO4ngADkiIiIiItIT71gQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHeGFgQEREREZHe/g/51F7+NHRgcAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression(max_iter=10000, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;LogisticRegression<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>LogisticRegression(max_iter=10000, random_state=42)</pre></div> </div></div></div></div>"], "text/plain": ["LogisticRegression(max_iter=10000, random_state=42)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["X_lr_train_resampled, y_lr_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(logistic_regression_model, X_lr_train_resampled, y_lr_train_resampled, X_test_reduced, y_test_reduced)"]}, {"cell_type": "markdown", "id": "fbfd4ab6870c95cc", "metadata": {}, "source": ["#### ii. Eva<PERSON>ate Random Forest Classifier"]}, {"cell_type": "code", "execution_count": 10, "id": "2ad2fdabdb3fc119", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.582120500Z", "start_time": "2025-06-22T20:23:43.101702Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.6806\n", "Precision: 0.3734\n", "Recall: 0.1771\n", "F1 Score: 0.2403\n", "ROC AUC Score: 0.5695\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestClassifier(n_estimators=200, random_state=42)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomForestClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.5/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(n_estimators=200, random_state=42)</pre></div> </div></div></div></div>"], "text/plain": ["RandomForestClassifier(n_estimators=200, random_state=42)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["X_rf_train_resampled, y_rf_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(random_forest_classifier_model, X_rf_train_resampled, y_rf_train_resampled, X_test_reduced, y_test_reduced)"]}, {"cell_type": "markdown", "id": "457964c1302fce34", "metadata": {}, "source": ["#### iii. Evaluate XGBoost Classifier"]}, {"cell_type": "code", "execution_count": 11, "id": "84a9e6e4d90bbafc", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.586187900Z", "start_time": "2025-06-22T20:24:05.267110Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Class Imbalance handling SMOTE\n", "------------------------------\n", "Before SMOTE: Counter({False: 41149, True: 16418})\n", "After SMOTE: Counter({True: 41149, False: 41149})\n", "\n", "\n", "Evaluation Metrics:\n", "--------------------\n", "Accuracy: 0.7089\n", "Precision: 0.4594\n", "Recall: 0.1187\n", "F1 Score: 0.1886\n", "ROC AUC Score: 0.6072\n", "\n", "\n", "Evaluation:\n", "-----------------\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<style>#sk-container-id-3 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-3 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-3 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-3 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-3 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-3 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-3 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-3 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-3 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-3 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-3 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-3 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-3 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-3 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-3 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-3 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-3 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-3 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-3 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-3 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-3 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-3 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-3 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-3 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-3 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-3 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-3\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" checked><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;XGBClassifier<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://xgboost.readthedocs.io/en/release_3.0.0/python/python_api.html#xgboost.XGBClassifier\">?<span>Documentation for XGBClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)</pre></div> </div></div></div></div>"], "text/plain": ["XGBClassifier(base_score=None, booster=None, callbacks=None,\n", "              colsample_bylevel=None, colsample_bynode=None,\n", "              colsample_bytree=None, device=None, early_stopping_rounds=None,\n", "              enable_categorical=False, eval_metric=None, feature_types=None,\n", "              feature_weights=None, gamma=None, grow_policy=None,\n", "              importance_type=None, interaction_constraints=None,\n", "              learning_rate=None, max_bin=None, max_cat_threshold=None,\n", "              max_cat_to_onehot=None, max_delta_step=None, max_depth=None,\n", "              max_leaves=None, min_child_weight=None, missing=nan,\n", "              monotone_constraints=None, multi_strategy=None, n_estimators=200,\n", "              n_jobs=None, num_parallel_tree=None, ...)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["X_xgb_train_resampled, y_xgb_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)\n", "evaluate_model(XGBClassifier_model, X_xgb_train_resampled, y_xgb_train_resampled, X_test_reduced, y_test_reduced)"]}, {"cell_type": "markdown", "id": "8c4929f5d44ca215", "metadata": {}, "source": ["### 5.3 Hyperparameter Tuning\n", "<p>\n", "\n", "Using `GridSearchCV` to tune `Hyperparameter` for the best-performing model, Random Forest\n", "\n", "</p>"]}, {"cell_type": "code", "execution_count": 12, "id": "74f67fb2391b6181", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.588213Z", "start_time": "2025-06-22T20:24:07.085359Z"}}, "outputs": [], "source": ["from sklearn.model_selection import GridSearchCV, RandomizedSearchCV"]}, {"cell_type": "code", "execution_count": 13, "id": "d7d3d4148d2d2085", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.589215800Z", "start_time": "2025-06-22T20:24:07.186467Z"}}, "outputs": [], "source": ["# Define the parameter grid for each model\n", "param_grid_log_reg_a = {\n", "    'C': [0.01, 0.1, 1, 10, 100],  # Regularization strength\n", "    'penalty': ['l1', 'l2'],       # Type of regularization\n", "    'solver': ['saga','liblinear']        # Solver for optimization\n", "}\n", "\n", "param_grid_random_forest_a = {\n", "    'n_estimators': [50, 100, 200],  # Number of trees in the forest\n", "    'max_depth': [None, 10, 20, 30], # Maximum depth of the tree\n", "    'min_samples_split': [2, 5, 10], # Minimum number of samples required to split an internal node\n", "    'min_samples_leaf': [1, 2, 4]    # Minimum number of samples required to be at a leaf node\n", "}\n", "\n", "param_grid_xgb_a = {\n", "    'n_estimators': [50, 100, 200],       # Number of trees in the ensemble\n", "    'learning_rate': [0.01, 0.1, 0.2],    # Step size shrinkage used in update to prevent overfitting\n", "    'max_depth': [3, 5, 7],               # Maximum depth of a tree\n", "    'subsample': [0.8, 0.9, 1.0],         # Subsample ratio of the training instances\n", "    'colsample_bytree': [0.8, 0.9, 1.0]   # Subsample ratio of columns when constructing each tree\n", "}"]}, {"cell_type": "markdown", "id": "70df756a174e31f7", "metadata": {}, "source": ["#### 1. GridSearchCV"]}, {"cell_type": "code", "execution_count": 14, "id": "d2fbd3ad0a2bf2ed", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.589215800Z", "start_time": "2025-06-22T20:24:07.285422Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tuning Logistic Regression....\n", "Fitting 5 folds for each of 20 candidates, totalling 100 fits\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 4\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTuning Logistic Regression....\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      3\u001b[0m grid_log_reg \u001b[38;5;241m=\u001b[39m GridSearchCV(estimator\u001b[38;5;241m=\u001b[39mlogistic_regression_model, param_grid\u001b[38;5;241m=\u001b[39mparam_grid_log_reg_a, cv\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m5\u001b[39m, scoring\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mf1\u001b[39m\u001b[38;5;124m'\u001b[39m, n_jobs\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, verbose\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m3\u001b[39m)\n\u001b[1;32m----> 4\u001b[0m grid_log_reg\u001b[38;5;241m.\u001b[39mfit(X_train_reduced, y_train_reduced)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\base.py:1473\u001b[0m, in \u001b[0;36m_fit_context.<locals>.decorator.<locals>.wrapper\u001b[1;34m(estimator, *args, **kwargs)\u001b[0m\n\u001b[0;32m   1466\u001b[0m     estimator\u001b[38;5;241m.\u001b[39m_validate_params()\n\u001b[0;32m   1468\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m config_context(\n\u001b[0;32m   1469\u001b[0m     skip_parameter_validation\u001b[38;5;241m=\u001b[39m(\n\u001b[0;32m   1470\u001b[0m         prefer_skip_nested_validation \u001b[38;5;129;01mor\u001b[39;00m global_skip_validation\n\u001b[0;32m   1471\u001b[0m     )\n\u001b[0;32m   1472\u001b[0m ):\n\u001b[1;32m-> 1473\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m fit_method(estimator, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:1018\u001b[0m, in \u001b[0;36mBaseSearchCV.fit\u001b[1;34m(self, X, y, **params)\u001b[0m\n\u001b[0;32m   1012\u001b[0m     results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_format_results(\n\u001b[0;32m   1013\u001b[0m         all_candidate_params, n_splits, all_out, all_more_results\n\u001b[0;32m   1014\u001b[0m     )\n\u001b[0;32m   1016\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[1;32m-> 1018\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_run_search(evaluate_candidates)\n\u001b[0;32m   1020\u001b[0m \u001b[38;5;66;03m# multimetric is determined here because in the case of a callable\u001b[39;00m\n\u001b[0;32m   1021\u001b[0m \u001b[38;5;66;03m# self.scoring the return type is only known after calling\u001b[39;00m\n\u001b[0;32m   1022\u001b[0m first_test_score \u001b[38;5;241m=\u001b[39m all_out[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest_scores\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:1572\u001b[0m, in \u001b[0;36mGridSearchCV._run_search\u001b[1;34m(self, evaluate_candidates)\u001b[0m\n\u001b[0;32m   1570\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_run_search\u001b[39m(\u001b[38;5;28mself\u001b[39m, evaluate_candidates):\n\u001b[0;32m   1571\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Search all candidates in param_grid\"\"\"\u001b[39;00m\n\u001b[1;32m-> 1572\u001b[0m     evaluate_candidates(ParameterGrid(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparam_grid))\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\model_selection\\_search.py:964\u001b[0m, in \u001b[0;36mBaseSearchCV.fit.<locals>.evaluate_candidates\u001b[1;34m(candidate_params, cv, more_results)\u001b[0m\n\u001b[0;32m    956\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbose \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m    957\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\n\u001b[0;32m    958\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFitting \u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;124m folds for each of \u001b[39m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m candidates,\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    959\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m totalling \u001b[39m\u001b[38;5;132;01m{2}\u001b[39;00m\u001b[38;5;124m fits\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[0;32m    960\u001b[0m             n_splits, n_candidates, n_candidates \u001b[38;5;241m*\u001b[39m n_splits\n\u001b[0;32m    961\u001b[0m         )\n\u001b[0;32m    962\u001b[0m     )\n\u001b[1;32m--> 964\u001b[0m out \u001b[38;5;241m=\u001b[39m parallel(\n\u001b[0;32m    965\u001b[0m     delayed(_fit_and_score)(\n\u001b[0;32m    966\u001b[0m         clone(base_estimator),\n\u001b[0;32m    967\u001b[0m         X,\n\u001b[0;32m    968\u001b[0m         y,\n\u001b[0;32m    969\u001b[0m         train\u001b[38;5;241m=\u001b[39mtrain,\n\u001b[0;32m    970\u001b[0m         test\u001b[38;5;241m=\u001b[39mtest,\n\u001b[0;32m    971\u001b[0m         parameters\u001b[38;5;241m=\u001b[39mparameters,\n\u001b[0;32m    972\u001b[0m         split_progress\u001b[38;5;241m=\u001b[39m(split_idx, n_splits),\n\u001b[0;32m    973\u001b[0m         candidate_progress\u001b[38;5;241m=\u001b[39m(cand_idx, n_candidates),\n\u001b[0;32m    974\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mfit_and_score_kwargs,\n\u001b[0;32m    975\u001b[0m     )\n\u001b[0;32m    976\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m (cand_idx, parameters), (split_idx, (train, test)) \u001b[38;5;129;01min\u001b[39;00m product(\n\u001b[0;32m    977\u001b[0m         \u001b[38;5;28menumerate\u001b[39m(candidate_params),\n\u001b[0;32m    978\u001b[0m         \u001b[38;5;28menumerate\u001b[39m(cv\u001b[38;5;241m.\u001b[39msplit(X, y, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrouted_params\u001b[38;5;241m.\u001b[39msplitter\u001b[38;5;241m.\u001b[39msplit)),\n\u001b[0;32m    979\u001b[0m     )\n\u001b[0;32m    980\u001b[0m )\n\u001b[0;32m    982\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(out) \u001b[38;5;241m<\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m    983\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    984\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo fits were performed. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    985\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWas the CV iterator empty? \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    986\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWere there no candidates?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    987\u001b[0m     )\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\sklearn\\utils\\parallel.py:74\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m     69\u001b[0m config \u001b[38;5;241m=\u001b[39m get_config()\n\u001b[0;32m     70\u001b[0m iterable_with_config \u001b[38;5;241m=\u001b[39m (\n\u001b[0;32m     71\u001b[0m     (_with_config(delayed_func, config), args, kwargs)\n\u001b[0;32m     72\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m delayed_func, args, kwargs \u001b[38;5;129;01min\u001b[39;00m iterable\n\u001b[0;32m     73\u001b[0m )\n\u001b[1;32m---> 74\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__call__\u001b[39m(iterable_with_config)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:2007\u001b[0m, in \u001b[0;36mParallel.__call__\u001b[1;34m(self, iterable)\u001b[0m\n\u001b[0;32m   2001\u001b[0m \u001b[38;5;66;03m# The first item from the output is blank, but it makes the interpreter\u001b[39;00m\n\u001b[0;32m   2002\u001b[0m \u001b[38;5;66;03m# progress until it enters the Try/Except block of the generator and\u001b[39;00m\n\u001b[0;32m   2003\u001b[0m \u001b[38;5;66;03m# reaches the first `yield` statement. This starts the asynchronous\u001b[39;00m\n\u001b[0;32m   2004\u001b[0m \u001b[38;5;66;03m# dispatch of the tasks to the workers.\u001b[39;00m\n\u001b[0;32m   2005\u001b[0m \u001b[38;5;28mnext\u001b[39m(output)\n\u001b[1;32m-> 2007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m output \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mreturn_generator \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mlist\u001b[39m(output)\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:1650\u001b[0m, in \u001b[0;36mParallel._get_outputs\u001b[1;34m(self, iterator, pre_dispatch)\u001b[0m\n\u001b[0;32m   1647\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1649\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mretrieval_context():\n\u001b[1;32m-> 1650\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_retrieve()\n\u001b[0;32m   1652\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mGeneratorExit\u001b[39;00m:\n\u001b[0;32m   1653\u001b[0m     \u001b[38;5;66;03m# The generator has been garbage collected before being fully\u001b[39;00m\n\u001b[0;32m   1654\u001b[0m     \u001b[38;5;66;03m# consumed. This aborts the remaining tasks if possible and warn\u001b[39;00m\n\u001b[0;32m   1655\u001b[0m     \u001b[38;5;66;03m# the user if necessary.\u001b[39;00m\n\u001b[0;32m   1656\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exception \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "File \u001b[1;32mC:\\ProgramData\\anaconda3\\Lib\\site-packages\\joblib\\parallel.py:1762\u001b[0m, in \u001b[0;36mParallel._retrieve\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1757\u001b[0m \u001b[38;5;66;03m# If the next job is not ready for retrieval yet, we just wait for\u001b[39;00m\n\u001b[0;32m   1758\u001b[0m \u001b[38;5;66;03m# async callbacks to progress.\u001b[39;00m\n\u001b[0;32m   1759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m ((\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m\n\u001b[0;32m   1760\u001b[0m     (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_jobs[\u001b[38;5;241m0\u001b[39m]\u001b[38;5;241m.\u001b[39mget_status(\n\u001b[0;32m   1761\u001b[0m         timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout) \u001b[38;5;241m==\u001b[39m TASK_PENDING)):\n\u001b[1;32m-> 1762\u001b[0m     time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m0.01\u001b[39m)\n\u001b[0;32m   1763\u001b[0m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[0;32m   1765\u001b[0m \u001b[38;5;66;03m# We need to be careful: the job list can be filling up as\u001b[39;00m\n\u001b[0;32m   1766\u001b[0m \u001b[38;5;66;03m# we empty it and Python list are not thread-safe by\u001b[39;00m\n\u001b[0;32m   1767\u001b[0m \u001b[38;5;66;03m# default hence the use of the lock\u001b[39;00m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# Performance GridSearchCV for Logistic Regression\n", "print(\"Tuning Logistic Regression....\")\n", "grid_log_reg = GridSearchCV(estimator=logistic_regression_model, param_grid=param_grid_log_reg_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_log_reg.fit(X_train_reduced, y_train_reduced)\n"]}, {"cell_type": "code", "execution_count": null, "id": "1ed5561b312f1be9", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.595104400Z", "start_time": "2025-06-22T20:29:00.326549Z"}}, "outputs": [], "source": ["# Performance GridSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "grid_rf = GridSearchCV(estimator=random_forest_classifier_model, param_grid=param_grid_random_forest_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_rf.fit(X_train_reduced, y_train_reduced)\n"]}, {"cell_type": "code", "execution_count": null, "id": "efd7b744107ec485", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.596348800Z", "start_time": "2025-06-22T20:40:27.613133Z"}}, "outputs": [], "source": ["# Performance GridSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "grid_xgb = GridSearchCV(estimator=XGBClassifier_model, param_grid=param_grid_xgb_a, cv=5, scoring='f1', n_jobs=-1, verbose=3)\n", "grid_xgb.fit(X_train_reduced, y_train_reduced)"]}, {"cell_type": "code", "execution_count": null, "id": "69f6ba2b60d7934b", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.598391Z", "start_time": "2025-06-22T20:44:32.513944Z"}}, "outputs": [], "source": ["print(f\"Logistic Regression Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression: {grid_log_reg.best_params_}\")\n", "print(f\"Best cross-validation score for Logistic Regression: {grid_log_reg.best_score_:.4f}\")\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest: {grid_rf.best_params_}\")\n", "print(f\"Best cross-validation score for Random Forest: {grid_rf.best_score_:.4f}\")\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using GridSearchCV Results:\")\n", "print(f\"---------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost: {grid_xgb.best_params_}\")\n", "print(f\"Best cross-validation score for XGBoost: {grid_xgb.best_score_:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "460674e643091aca", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.599904500Z", "start_time": "2025-06-22T20:44:32.683679Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for Logistic Regression\n", "print(\"\\nTuning Logistic Regression...\")\n", "random_log_reg = RandomizedSearchCV(logistic_regression_model, param_grid_log_reg_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_log_reg.fit(X_train_reduced, y_train_reduced)\n"]}, {"cell_type": "code", "execution_count": null, "id": "fc318446e2b90b28", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.602442200Z", "start_time": "2025-06-22T20:47:53.818623Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "random_rf = RandomizedSearchCV(random_forest_classifier_model, param_grid_random_forest_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_rf.fit(X_train_reduced, y_train_reduced)"]}, {"cell_type": "code", "execution_count": null, "id": "77caec97b4bf866a", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.604459400Z", "start_time": "2025-06-22T20:48:43.641712Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "random_xgb = RandomizedSearchCV(XGBClassifier_model, param_grid_xgb_a, n_iter=10, n_jobs=-1, cv=5, scoring='accuracy', verbose=3)\n", "random_xgb.fit(X_train_reduced, y_train_reduced)"]}, {"cell_type": "code", "execution_count": null, "id": "6e8deed15505322", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.607488Z", "start_time": "2025-06-22T20:48:51.347276Z"}}, "outputs": [], "source": ["print(f\"Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression:\", random_log_reg.best_params_)\n", "print(f\"Best cross-validation score for Logistic Regression:\", random_log_reg.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest:\", random_rf.best_params_)\n", "print(f\"Best cross-validation score for Random Forest:\", random_rf.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost:\", random_xgb.best_params_)\n", "print(f\"Best cross-validation score for XGBoost:\", random_xgb.best_score_)\n", "print(f\"\\n\")"]}, {"cell_type": "markdown", "id": "fd4867f0d039cf44", "metadata": {}, "source": ["#### 2. RandomizedSearchCV"]}, {"cell_type": "code", "execution_count": null, "id": "76263e49ab6a9b59", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.617552500Z", "start_time": "2025-06-22T20:48:51.517890Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for Logistic Regression\n", "print(\"\\nTuning Logistic Regression...\")\n", "random_log_reg = RandomizedSearchCV(logistic_regression_model, param_grid_log_reg_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_log_reg.fit(X_train_reduced, y_train_reduced)"]}, {"cell_type": "code", "execution_count": null, "id": "b9481caa65549601", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.619711Z", "start_time": "2025-06-22T20:59:16.252483Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for Random Forest\n", "print(\"\\nTuning Random Forest...\")\n", "random_rf = RandomizedSearchCV(random_forest_classifier_model, param_grid_random_forest_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_rf.fit(X_train_reduced, y_train_reduced)"]}, {"cell_type": "code", "execution_count": null, "id": "eced647dc6443d58", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.621880700Z", "start_time": "2025-06-22T21:02:49.380860Z"}}, "outputs": [], "source": ["# Perform RandomizedSearchCV for XGBoost\n", "print(\"\\nTuning XGBoost...\")\n", "random_xgh = RandomizedSearchCV(XGBClassifier_model, param_grid_xgb_a, n_iter=10, cv=5, scoring='accuracy', verbose=3)\n", "random_xgh.fit(X_train_reduced, y_train_reduced)\n"]}, {"cell_type": "code", "execution_count": null, "id": "6eacdae0193638fc", "metadata": {"ExecuteTime": {"end_time": "2025-07-05T22:07:20.622881700Z", "start_time": "2025-06-22T21:03:03.313310Z"}}, "outputs": [], "source": ["print(f\"Logistic Regression Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------------\")\n", "print(f\"Best parameters for Logistic Regression:\", random_log_reg.best_params_)\n", "print(f\"Best cross-validation score for Logistic Regression:\", random_log_reg.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"Random Forest Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------------\")\n", "print(f\"Best parameters for Random Forest:\", random_rf.best_params_)\n", "print(f\"Best cross-validation score for Random Forest:\", random_rf.best_score_)\n", "print(f\"\\n\")\n", "\n", "print(f\"XGBoost Hyperparameter Tuning using RandomizedSearchCV Results:\")\n", "print(f\"---------------------------------------------------------------\")\n", "print(f\"Best parameters for XGBoost:\", random_xgh.best_params_)\n", "print(f\"Best cross-validation score for XGBoost:\", random_xgh.best_score_)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}