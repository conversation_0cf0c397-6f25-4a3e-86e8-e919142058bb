import os
import logging

from pathlib import Path
from typing import Optional, Union, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Use environment variables with fallbacks for flexibility
PROJECT_ROOT = Path(os.getenv('CAUSALITY_PROJECT_ROOT', Path(__file__).resolve().parent.parent))
DATA_SOURCE = Path(os.getenv('CAUSALITY_DATA_SOURCE', Path(__file__).resolve().parent.parent.parent))

# Base paths
DATA_SOURCE_BASE_PATH = Path(os.getenv('CAUSALITY_BASE_PATH', DATA_SOURCE / 'source'))
MODEL_SOURCE_BASE_PATH = Path(os.getenv('CAUSALITY_MODEL_BASE_PATH', DATA_SOURCE / 'models'))

# Data file names
PRIMARY_DATA_FILE = "medical_appointment_no_show.csv"
PREPROCESSED_DATA_FILE = "medical_appointment_no_show_cleaned.csv"
FINAL_DATA_FILE = "medical_appointment_no_show_final.csv"
FEATURE_DATA_FILE = "medical_appointment_no_show_features.csv"

# Model file names
PREDICTIVE_MODEL_FILE = "predictive_model.pkl"
CAUSAL_MODEL_FILE = "causal_model.pkl"

# Settings dictionaries
settings = {
    "data_source_base_path": str(DATA_SOURCE_BASE_PATH),
    "primary_data_file": PRIMARY_DATA_FILE,
    "preprocessed_data_file": PREPROCESSED_DATA_FILE,
    "final_data_file": FINAL_DATA_FILE,
    "feature_data_file": FEATURE_DATA_FILE,
    "predictive_model_file": PREDICTIVE_MODEL_FILE,
    "causal_model_file": CAUSAL_MODEL_FILE,
}

app_settings = {
    "app_base_path": str(DATA_SOURCE_BASE_PATH),
    "app_source_path": Path("raw") / PRIMARY_DATA_FILE,
    "app_preprocessed_data_file": Path("interim") / PREPROCESSED_DATA_FILE,
    "app_feature_data_file": Path("processed") / FEATURE_DATA_FILE,
    "app_final_data_file": Path("processed") / FINAL_DATA_FILE,
    "app_predictive_model_path": str(PROJECT_ROOT / "models" / PREDICTIVE_MODEL_FILE),
    "app_causal_model_path": str(PROJECT_ROOT / "models" / CAUSAL_MODEL_FILE),
}

# Model settings
model_settings = {
    "random_state": 42,
    "test_size": 0.2,
    "cv_folds": 5,
    "n_jobs": -1,
}

# Feature engineering settings
feature_settings = {
    "age_bins": [0, 18, 35, 50, 65, 100],
    "age_labels": ['0-18', '19-35', '36-50', '51-65', '66+'],
    "weekend_days": [5, 6],  # Saturday and Sunday
}

# API settings
api_settings = {
    "host": os.getenv("API_HOST", "0.0.0.0"),
    "port": int(os.getenv("API_PORT", 8000)),
    "reload": os.getenv("API_RELOAD", "true").lower() == "true",
    "log_level": os.getenv("API_LOG_LEVEL", "info"),
}

class DataPaths:
    """Class to manage data paths and settings."""
    def __init__(self, base_path: Optional[Path] = None, model_path: Optional[Path] = None):
        self.base_path = Path(base_path) if base_path else DATA_SOURCE_BASE_PATH
        self.model_path = Path(model_path) if model_path else MODEL_SOURCE_BASE_PATH
        self._ensure_directories()
        
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.base_path,
            self.base_path.parent / "interim",
            self.base_path.parent / "processed",
            self.base_path.parent / "final",
            self.model_path,
            self.base_path.parent.parent / "logs",
            self.base_path.parent.parent / "reports",
            self.base_path.parent.parent / "reports" / "figures",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {directory}")
                
    @property
    def raw_data(self) -> Path:
        """Path to raw data directory."""
        return self.base_path / "raw"

    @property
    def interim_data(self) -> Path:
        """Path to interim data directory."""
        return self.base_path / "interim"

    @property
    def processed_data(self) -> Path:
        """Path to processed data directory."""
        return self.base_path / "processed"
    
    @property
    def final_data(self) -> Path:
        """Path to final data directory."""
        return self.base_path / "final"

    @property
    def models(self) -> Path:
        """Path to models directory."""
        return self.model_path

    @property
    def logs(self) -> Path:
        """Path to logs directory."""
        return self.base_path.parent.parent / "logs"

    @property
    def reports(self) -> Path:
        """Path to reports directory."""
        return self.base_path.parent.parent / "reports"

    @property
    def figures(self) -> Path:
        """Path to figures directory within reports."""
        return self.reports / "figures"

    def get_data_file(self, filename: str, stage: str = "raw") -> Path:
        """
        Get the full path to a data file based on the stage.
        
        Args:
            filename (str): The name of the data file.
            stage (str): The stage of the data ('raw', 'interim', 'processed').
            
        Returns:
            Path to the file
        """
        stage_map = {
            "raw": self.raw_data,
            "interim": self.interim_data,
            "processed": self.processed_data,
            "final": self.final_data,
        }
        
        if stage not in stage_map:
            raise ValueError(f"Unknown stage: {stage}. Valid stages: {list(stage_map.keys())}")
        
        return stage_map[stage] / filename

    def get_model_file(self, filename: str) -> Path:
        """Get full path for a model file."""
        return self.models / filename

    def get_log_file(self, filename: str) -> Path:
        """Get full path for a log file."""
        return self.logs / filename

    def get_report_file(self, filename: str) -> Path:
        """Get full path for a report file."""
        return self.reports / filename

    def get_figure_file(self, filename: str) -> Path:
        """Get full path for a figure file."""
        return self.figures / filename

class Config:
    """Configuration management class."""
    def __init__(self):
        self.data_paths = DataPaths()
        self.settings = settings
        self.app_settings = app_settings
        self.model_settings = model_settings
        self.feature_settings = feature_settings
        self.api_settings = api_settings

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key (can use dot notation)
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        # Check environment variable first
        env_key = f"CAUSALITY_{key.upper().replace('.', '_')}"
        env_value = os.getenv(env_key)
        if env_value is not None:
            return env_value
        
        # Check configuration dictionaries
        if '.' in key:
            parts = key.split('.')
            config_dict = getattr(self, parts[0], {})
            return config_dict.get(parts[1], default)
        
        # Check all configuration dictionaries
        for config_name in ['settings', 'app_settings', 'model_settings', 'feature_settings', 'api_settings']:
            config_dict = getattr(self, config_name)
            if key in config_dict:
                return config_dict[key]
        
        return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Configuration value
        """
        if '.' in key:
            parts = key.split('.')
            config_dict = getattr(self, parts[0], {})
            config_dict[parts[1]] = value
        else:
            # Default to settings
            self.settings[key] = value
            
    def to_dict(self) -> Dict[str, Any]:
        """Export all configuration as dictionary."""
        return {
            'settings': self.settings,
            'app_settings': self.app_settings,
            'model_settings': self.model_settings,
            'feature_settings': self.feature_settings,
            'api_settings': self.api_settings,
        }
        
    def validate(self) -> bool:
        """
        Validate configuration.
        
        Returns:
            bool: True if configuration is valid
        """
        required_paths = [
            self.data_paths.raw_data,
            self.data_paths.models,
        ]
        
        for path in required_paths:
            if not path.exists():
                logger.warning(f"Required path does not exist: {path}")
                return False
        
        return True
    
# Create global configuration instance
config = Config()

# Utility functions for backward compatibility
def get_setting(key: str, default: Any = None) -> Any:
    """Get setting value."""
    return config.get(key, default)

def get_data_path(filename: str, stage: str = "raw") -> Path:
    """Get full path for a data file."""
    return config.data_paths.get_data_file(filename, stage)

def get_model_path(filename: str) -> Path:
    """Get full path for a model file."""
    return config.data_paths.get_model_file(filename)

# Export commonly used values
PROJECT_NAME = "Medical Appointment No-Show Analysis"
VERSION = "1.0.0"
AUTHOR = "Your Name"

if __name__ == "__main__":
    # Test configuration
    print(f"Project Root: {PROJECT_ROOT}")
    print(f"Data Source Base Path: {DATA_SOURCE_BASE_PATH}")
    print(f"Model Base Path: {MODEL_SOURCE_BASE_PATH}")
    
    print("\nValidating configuration...")
    if config.validate():
        print("[+] Configuration is valid")
    else:
        print("[-] Configuration has issues")
    
    print("\nData Paths:")
    print(f"Raw Data: {config.data_paths.raw_data}")
    print(f"Interim Data: {config.data_paths.interim_data}")
    print(f"Processed Data: {config.data_paths.processed_data}")
    print(f"Final Data: {config.data_paths.final_data}")
    print(f"Models: {config.data_paths.models}")
    
    print("\nExample file paths:")
    print(f"Primary data: {config.data_paths.get_data_file(PRIMARY_DATA_FILE)}")
    print(f"Predictive model: {config.data_paths.get_model_file(PREDICTIVE_MODEL_FILE)}")