import os, sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import seaborn as sns
import logging as logger
import data.load_data as ld
import matplotlib.pyplot as plt

from tabulate import tabulate
from sklearn.impute import SimpleImputer
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import SMOTE, ADASYN, RandomOverSampler

from xgboost import XGBClassifier
from collections import Counter
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, roc_auc_score, classification_report

def split_data(df:pd.DataFrame):
    """
    Split the data into training and testing sets.
    This method performs the following steps:
    1. Defines features (x) and target (y).
    2. Converts categorical variables to numerical using one-hot encoding.
    3. Imputes missing values with the mean (or median/mode).
    4. Splits the data into training and testing sets.
    5. Print's the shape of the encoded features and the test set.
    """
    tmp_df = df.copy(deep=True)

    # Define features (x) and target (y)
    x_reduced = tmp_df.drop([col for col in tmp_df.columns if col.startswith('neighbourhood_') or col == 'no_show'],
                            axis=1)
    y_reduced = tmp_df['no_show']

    # Convert categorical variables to numerical using one-hot encoding
    X_reduced_encoded = pd.get_dummies(x_reduced, drop_first=True)

    # Impute missing values with the mean (or median/mode)
    impute = SimpleImputer(strategy='mean')
    X_reduced_encoded = pd.DataFrame(impute.fit_transform(X_reduced_encoded), columns=X_reduced_encoded.columns)

    # Split the data into training and testing sets
    X_train_reduced, X_test_reduced, y_train_reduced, y_test_reduced = train_test_split(X_reduced_encoded, y_reduced,
                                                                                        test_size=0.2, random_state=42,
                                                                                        stratify=y_reduced)

    # Print the shape of the encoded features
    print(f"Encoded features shape: {X_train_reduced.shape}")
    # Split the data into training and testing sets
    print(f"Test set shape: {X_test_reduced.shape} ")

    return X_train_reduced, X_test_reduced, y_train_reduced, y_test_reduced

def handle_class_imbalance(X_train, y_train):
    """
    Handle class imbalance in the training data.
    This method applies SMOTE to the training data to balance the classes.
    Args:
        X_train (DataFrame): Training features.
        y_train (Series): Training target variable.
    Returns:
        tuple: Resampled training features and target variable.
    """
    smote = SMOTE(random_state=42, sampling_strategy='auto', k_neighbors=5)
    X_resampled, y_resampled = smote.fit_resample(X_train, y_train)

    # Print the class distribution before and after SMOTE
    print("Class Imbalance handling SMOTE")
    print("------------------------------")
    print(f"Original dataset shape: {Counter(y_train)}")
    print(f"Resampled dataset shape: {Counter(y_resampled)}")

    return X_resampled, y_resampled

def evaluate_model(model, X_train_resampled, y_train_resampled, X_test, y_test):
    """
    Evaluate the model using various metrics.
    This method trains a RandomForestClassifier and evaluates it on the test set.
    Args:
        X_train_resampled (DataFrame): Resampled training features.
        y_train_resampled (Series): Resampled training target variable.
        X_test (DataFrame): Testing features.
        y_test (Series): Testing target variable.
    Returns:
        :param model:
    """
    # Train the model
    model.fit(X_train_resampled, y_train_resampled)

    # Make prediction
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1]  # Get probabilities for the positive class

    # Evaluate the model
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, zero_division=0)
    recall = recall_score(y_test, y_pred, zero_division=0)
    f1 = f1_score(y_test, y_pred, zero_division=0)
    roc_auc = roc_auc_score(y_test, y_prob)

    conf_matrix = confusion_matrix(y_test, y_pred)

    graph_report = classification_report(y_test, y_pred, output_dict=True)
    class_report = classification_report(y_test, y_pred)

    # Print evaluation metrics
    print("\n")
    print(f"Evaluation Metrics:\n")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1 Score: {f1:.4f}")
    print(f"ROC AUC Score: {roc_auc:.4f}")

    # Plot confusion matrix
    print("\n")
    print("Evaluation:")
    print("-----------------")
    plt.figure(figsize=(8, 4))

    plt.subplot(1, 2, 1)
    sns.heatmap(class_report, annot=True, fmt='.2f', cmap='crest', cbar=False)
    plt.title('Classification Report')
    plt.xlabel('Metrics')
    plt.ylabel('Classes')

    plt.subplot(1, 2, 2)
    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Greens', cbar=False)
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')

    plt.tight_layout()
    plt.show()

    return model

def main():
    """
    Main method to execute the evaluation process.
    This method loads the data, splits it into training and testing sets, and returns the sets.
    Returns:
        tuple: Training and testing DataFrames.
    """
    # Load the data
    RANDOM_STATE = 42

    data = ld.load_final_data().copy(deep=True)

    # Split the data into training and testing sets7
    X_train_reduced, X_test_reduced, y_train_reduced, y_test_reduced = split_data(data)

    # Initialize models
    logistic_regression_model = LogisticRegression(max_iter=1000, random_state=RANDOM_STATE)
    random_forest_model = RandomForestClassifier(random_state=RANDOM_STATE, n_estimators=100, max_depth=10)
    xgboost_model = XGBClassifier(n_estimators=200, random_state=RANDOM_STATE)

    # Handle class imbalance in the training data
    X_lr_train_resampled, y_lr_train_resampled = handle_class_imbalance(X_train_reduced, y_train_reduced)
    evaluate_model(logistic_regression_model, X_lr_train_resampled, y_lr_train_resampled, X_test_reduced, y_test_reduced)

if __name__ == "__main__":
    main()