import sys, os, joblib, json, pickle
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common import utils
from globals import basic_settings
from datetime import datetime
from typing import Any, Dict, Optional

def save_model(model: Any, model_path: str, metadata: Optional[Dict] = None, use_joblib: bool = True):
    """
    Save a model to a file with optional metadata.
    
    Args:
        model (Any): The model to save.
        model_path (str): The path where the model will be saved.
        metadata (Optional[Dict]): Optional metadata to save alongside the model.
        use_joblib (bool): Whether to use joblib for saving the model.
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        # Save the model
        if use_joblib:
            joblib.dump(model, model_path)
        else:
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
                
        # Save metadata if provided
        if metadata:
            metadata_path = model_path.replace('.pkl', '_metadata.json')
            metadata['saved_at'] = datetime.now().isoformat()
            metadata['model_path'] = os.path.basename(model_path)
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)
                
            print(f"Model and metadata saved successfully at {model_path} and {metadata_path}.")
        else:
            print(f"Model saved successfully at {model_path}. No metadata provided.")
    except Exception as e:
        print(f"Error saving model: {e}")
        raise
    
def load_model(model_path: str, use_joblib: bool = True) -> Any:
    """
    Load a saved model.
    
    Args:
        model_path (str): The path from which to load the model.
        use_joblib (bool): Whether to use joblib for loading the model.
        
    Returns:
        The loaded model.
    """
    try:
        if use_joblib:
            model = joblib.load(model_path)
        else:
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
                
        print(f"Model loaded successfully from {model_path}.")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        raise
    
def load_metadata(model_path: str) -> Optional[Dict]:
    """
    Load metadata associated with a saved model.
    
    Args:
        model_path (str): The path from which to load the metadata.
        
    Returns:
        Optional[Dict]: The loaded metadata, or None if no metadata is found.
    """
    try:
        metadata_path = model_path.replace('.pkl', '_metadata.json')
        if os.path.exists(metadata_path):
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            print(f"Metadata loaded successfully from {metadata_path}.")
            return metadata
        else:
            print(f"No metadata found at {metadata_path}.")
            return None
    except Exception as e:
        print(f"Error loading metadata: {e}")
        return None
    
def _save_model_with_metadata(self, grid_search):
    """
        Save model with comprehensive metadata.
    """
    try:
        model_metadata = {
            'model_type': 'PredictiveModel',
            'algorithm': type(self.model).__name__,
            'best_params': grid_search.best_params_,
            'best_cv_score': float(grid_search.best_score_),
            'feature_names': self.feature_names,
            'target_column': self.target_column,
            'n_features': len(self.feature_names),
            'random_state': self.random_state,
            'test_size': self.test_size
        }

        # get model save path from settings
        app_base_path = basic_settings.app_settings.get('app_base_path')
        model_path = os.path.join(app_base_path, 'models', f"{self.model_name}.pkl")

        # Save model with mata-data
        utils.save_model(self.model, model_path, model_metadata)
        self.logger.info(f"Predictive model saved to {model_path}")
    except Exception as e:
        self.logger.error(f"Error saving predictive model: {e}")
            
def _save_model_with_metadata(self, model_type: str, outcome_model: str, treatment_model:str):
    """Save the trained causal model with metadata."""
    try:
        model_metadata = {
            'model_type': 'CausalModel',
            'algorithm': type(self.causal_model).__name__,
            'causal_model_type': model_type,
            'outcome_model': outcome_model,
            'treatment_model': treatment_model,
            'treatment_column': self.treatment_column,
            'outcome_column': self.outcome_column,
            'feature_names': self.feature_names,
            'n_features': len(self.feature_names) if self.feature_names else 0,
            'random_state': self.random_state,
            'test_size': self.test_size
        }

        # get model save path from settings
        app_base_path = basic_settings.app_settings.get('app_base_path')
        model_path = os.path.join(app_base_path, 'models', f"{self.model_name}.pkl")

        # Save model with metadata
        utils.save_model(self.causal_model, model_path, model_metadata)
        self.logger.info(f"Causal model saved to {model_path}")
    except Exception as e:
        self.logger.error(f"Error saving causal model: {e}")
        raise