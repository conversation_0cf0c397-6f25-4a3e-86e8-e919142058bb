import unittest
from src.data import preprocessor as pp, load_data as ld

class test_data_preprocessing(unittest.TestCase):
    def test_rename_columns(self):
        preprocessor = pp.Preprocessor()
        data = preprocessor.rename_columns()
        self.assertIn('patient_id', data.columns)
        self.assertIn('appointment_id', data.columns)
        self.assertIn('scheduled_day', data.columns)
        self.assertIn('appointment_day', data.columns)
        self.assertIn('no_show', data.columns)
        self.assertIn('hypertension', data.columns)
        self.assertIn('handicap', data.columns)

    def test_preprocessor(self):
        preprocessor = pp.Preprocessor()
        data = preprocessor.Processor()

        # Check data types
        self.assertEqual(data['patient_id'].dtype, 'Int64')
        self.assertEqual(data['appointment_id'].dtype, 'Int64')
        self.assertEqual(data['scheduled_day'].dtype, 'datetime64[ns]')
        self.assertEqual(data['appointment_day'].dtype, 'datetime64[ns]')

        # Check categorical columns
        self.assertEqual(data['gender'].dtype, 'category')
        self.assertEqual(data['neighbourhood'].dtype, 'category')
        self.assertEqual(data['handicap'].dtype, 'category')
        self.assertEqual(data['appointment_day_of_week'].dtype, 'category')

        # Check integer columns
        self.assertEqual(data['sms_received'].dtype, 'bool')
        self.assertEqual(data['no_show'].dtype, 'bool')

if __name__ == '__main__':
    unittest.main()