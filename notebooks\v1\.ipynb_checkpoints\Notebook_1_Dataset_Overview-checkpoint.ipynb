{"cells": [{"cell_type": "markdown", "id": "f18ea164ec61c74f", "metadata": {}, "source": ["<h1>Data Overview</h1>"]}, {"cell_type": "markdown", "id": "6424626284217a31", "metadata": {}, "source": ["<h2>1. Import Libraries</h2>"]}, {"cell_type": "code", "id": "e7a8f22ea3e171c2", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:47.366750Z", "start_time": "2025-06-12T21:05:47.359198Z"}}, "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"], "outputs": [], "execution_count": 20}, {"cell_type": "markdown", "id": "b0b30b76aca8219c", "metadata": {}, "source": ["<h2>2. Loading the Dataset and Initial Explore</h2>"]}, {"cell_type": "code", "id": "35e764968da946fe", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:47.793559Z", "start_time": "2025-06-12T21:05:47.462458Z"}}, "source": ["df_raw = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/data/raw/medical_appointment_no_show.csv\")"], "outputs": [], "execution_count": 21}, {"cell_type": "code", "id": "fd35b4f426910314", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:47.854714Z", "start_time": "2025-06-12T21:05:47.826667Z"}}, "source": ["# Display the first 10 rows of the dataset\n", "df_raw.head(10)"], "outputs": [{"data": {"text/plain": ["      PatientId  AppointmentID Gender          ScheduledDay  \\\n", "0  2.987250e+13        5642903      F  2016-04-29T18:38:08Z   \n", "1  5.589978e+14        5642503      M  2016-04-29T16:08:27Z   \n", "2  4.262962e+12        5642549      F  2016-04-29T16:19:04Z   \n", "3  8.679512e+11        5642828      F  2016-04-29T17:29:31Z   \n", "4  8.841186e+12        5642494      F  2016-04-29T16:07:23Z   \n", "5  9.598513e+13        5626772      F  2016-04-27T08:36:51Z   \n", "6  7.336882e+14        5630279      F  2016-04-27T15:05:12Z   \n", "7  3.449833e+12        5630575      F  2016-04-27T15:39:58Z   \n", "8  5.639473e+13        5638447      F  2016-04-29T08:02:16Z   \n", "9  7.812456e+13        5629123      F  2016-04-27T12:48:25Z   \n", "\n", "         AppointmentDay  Age      Neighbourhood  Scholarship  Hipertension  \\\n", "0  2016-04-29T00:00:00Z   62    JARDIM DA PENHA            0             1   \n", "1  2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0             0   \n", "2  2016-04-29T00:00:00Z   62      MATA DA PRAIA            0             0   \n", "3  2016-04-29T00:00:00Z    8  PONTAL DE CAMBURI            0             0   \n", "4  2016-04-29T00:00:00Z   56    JARDIM DA PENHA            0             1   \n", "5  2016-04-29T00:00:00Z   76          REPÚBLICA            0             1   \n", "6  2016-04-29T00:00:00Z   23         GOIABEIRAS            0             0   \n", "7  2016-04-29T00:00:00Z   39         GOIABEIRAS            0             0   \n", "8  2016-04-29T00:00:00Z   21         ANDORINHAS            0             0   \n", "9  2016-04-29T00:00:00Z   19          CONQUISTA            0             0   \n", "\n", "   Diabetes  Alcoholism  Handcap  SMS_received No-show  \n", "0         0           0        0             0      No  \n", "1         0           0        0             0      No  \n", "2         0           0        0             0      No  \n", "3         0           0        0             0      No  \n", "4         1           0        0             0      No  \n", "5         0           0        0             0      No  \n", "6         0           0        0             0     Yes  \n", "7         0           0        0             0     Yes  \n", "8         0           0        0             0      No  \n", "9         0           0        0             0      No  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2.987250e+13</td>\n", "      <td>5642903</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T18:38:08Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.589978e+14</td>\n", "      <td>5642503</td>\n", "      <td>M</td>\n", "      <td>2016-04-29T16:08:27Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.262962e+12</td>\n", "      <td>5642549</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:19:04Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>62</td>\n", "      <td>MATA DA PRAIA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8.679512e+11</td>\n", "      <td>5642828</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T17:29:31Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>8</td>\n", "      <td>PONTAL DE CAMBURI</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8.841186e+12</td>\n", "      <td>5642494</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T16:07:23Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>56</td>\n", "      <td>JARDIM DA PENHA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>9.598513e+13</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T08:36:51Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7.336882e+14</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:05:12Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>3.449833e+12</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T15:39:58Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>5.639473e+13</td>\n", "      <td>5638447</td>\n", "      <td>F</td>\n", "      <td>2016-04-29T08:02:16Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>21</td>\n", "      <td>ANDORINHAS</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>7.812456e+13</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27T12:48:25Z</td>\n", "      <td>2016-04-29T00:00:00Z</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "execution_count": 22}, {"cell_type": "code", "id": "80e15c1715753c39", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:48.003478Z", "start_time": "2025-06-12T21:05:47.997008Z"}}, "source": ["# Display the shape of the dataset\n", "print(f\"Shape of the dataframe -> Rows: \", df_raw.shape[0])\n", "print(f\"Shape of the dataframe -> Columns: \", df_raw.shape[1])"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of the dataframe -> Rows:  110527\n", "Shape of the dataframe -> Columns:  14\n"]}], "execution_count": 23}, {"cell_type": "code", "id": "4f7045cd35a598bb", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:48.182653Z", "start_time": "2025-06-12T21:05:48.134428Z"}}, "source": ["# Data types of the columns\n", "print(f\"Data types of Columns\\n\",df_raw.info())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 110527 entries, 0 to 110526\n", "Data columns (total 14 columns):\n", " #   Column          Non-Null Count   Dtype  \n", "---  ------          --------------   -----  \n", " 0   PatientId       110527 non-null  float64\n", " 1   AppointmentID   110527 non-null  int64  \n", " 2   Gender          110527 non-null  object \n", " 3   ScheduledDay    110527 non-null  object \n", " 4   AppointmentDay  110527 non-null  object \n", " 5   Age             110527 non-null  int64  \n", " 6   Neighbourhood   110527 non-null  object \n", " 7   Scholarship     110527 non-null  int64  \n", " 8   Hipertension    110527 non-null  int64  \n", " 9   Diabetes        110527 non-null  int64  \n", " 10  Alcoholism      110527 non-null  int64  \n", " 11  Handcap         110527 non-null  int64  \n", " 12  SMS_received    110527 non-null  int64  \n", " 13  No-show         110527 non-null  object \n", "dtypes: float64(1), int64(8), object(5)\n", "memory usage: 11.8+ MB\n", "Data types of Columns\n", " None\n"]}], "execution_count": 24}, {"cell_type": "code", "id": "8f7bc9b8c2516e8b", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:48.487363Z", "start_time": "2025-06-12T21:05:48.478780Z"}}, "source": ["print(f\"Column Names:\\n\", df_raw.columns.to_list())"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Column Names:\n", " ['PatientId', 'AppointmentID', 'Gender', 'ScheduledDay', 'AppointmentDay', 'Age', 'Neighbourhood', 'Scholarship', 'Hipertension', 'Diabetes', 'Alcoholism', 'Handcap', 'SMS_received', 'No-show']\n"]}], "execution_count": 25}, {"cell_type": "code", "id": "e290c5487875b40", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:48.735331Z", "start_time": "2025-06-12T21:05:48.662420Z"}}, "source": ["# Check for `null` and `missing` values in the dataset\n", "print(f\"Null values in the dataframe:\\n\", df_raw.isnull().sum())\n", "print(f\"-------------------------------------------------------\")\n", "print(f\"-------------------------------------------------------\")\n", "print(f\"Missing values in the dataframe:\\n\", df_raw.isna().sum())"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Null values in the dataframe:\n", " PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64\n", "-------------------------------------------------------\n", "-------------------------------------------------------\n", "Missing values in the dataframe:\n", " PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64\n"]}], "execution_count": 26}, {"cell_type": "code", "id": "ebe5c59f3183f5ea", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:48.883477Z", "start_time": "2025-06-12T21:05:48.804907Z"}}, "source": ["# Describe the dataset\n", "df_raw.describe()"], "outputs": [{"data": {"text/plain": ["          PatientId  AppointmentID            Age    Scholarship  \\\n", "count  1.105270e+05   1.105270e+05  110527.000000  110527.000000   \n", "mean   1.474963e+14   5.675305e+06      37.088874       0.098266   \n", "std    2.560949e+14   7.129575e+04      23.110205       0.297675   \n", "min    3.921784e+04   5.030230e+06      -1.000000       0.000000   \n", "25%    4.172614e+12   5.640286e+06      18.000000       0.000000   \n", "50%    3.173184e+13   5.680573e+06      37.000000       0.000000   \n", "75%    9.439172e+13   5.725524e+06      55.000000       0.000000   \n", "max    9.999816e+14   5.790484e+06     115.000000       1.000000   \n", "\n", "        Hipertension       Diabetes     Alcoholism        Handcap  \\\n", "count  110527.000000  110527.000000  110527.000000  110527.000000   \n", "mean        0.197246       0.071865       0.030400       0.022248   \n", "std         0.397921       0.258265       0.171686       0.161543   \n", "min         0.000000       0.000000       0.000000       0.000000   \n", "25%         0.000000       0.000000       0.000000       0.000000   \n", "50%         0.000000       0.000000       0.000000       0.000000   \n", "75%         0.000000       0.000000       0.000000       0.000000   \n", "max         1.000000       1.000000       1.000000       4.000000   \n", "\n", "        SMS_received  \n", "count  110527.000000  \n", "mean        0.321026  \n", "std         0.466873  \n", "min         0.000000  \n", "25%         0.000000  \n", "50%         0.000000  \n", "75%         1.000000  \n", "max         1.000000  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Age</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>37.088874</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>0.022248</td>\n", "      <td>0.321026</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>23.110205</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>0.161543</td>\n", "      <td>0.466873</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921784e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>-1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>18.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>37.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>55.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>115.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "execution_count": 27}, {"cell_type": "code", "id": "586aa98642cbf93f", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:49.238676Z", "start_time": "2025-06-12T21:05:48.987542Z"}}, "source": ["# Summary of the dataset\n", "df_raw.describe(include='all')"], "outputs": [{"data": {"text/plain": ["           PatientId  AppointmentID  Gender          ScheduledDay  \\\n", "count   1.105270e+05   1.105270e+05  110527                110527   \n", "unique           NaN            NaN       2                103549   \n", "top              NaN            NaN       F  2016-05-06T07:09:54Z   \n", "freq             NaN            NaN   71840                    24   \n", "mean    1.474963e+14   5.675305e+06     NaN                   NaN   \n", "std     2.560949e+14   7.129575e+04     NaN                   NaN   \n", "min     3.921784e+04   5.030230e+06     NaN                   NaN   \n", "25%     4.172614e+12   5.640286e+06     NaN                   NaN   \n", "50%     3.173184e+13   5.680573e+06     NaN                   NaN   \n", "75%     9.439172e+13   5.725524e+06     NaN                   NaN   \n", "max     9.999816e+14   5.790484e+06     NaN                   NaN   \n", "\n", "              AppointmentDay            Age   Neighbourhood    Scholarship  \\\n", "count                 110527  110527.000000          110527  110527.000000   \n", "unique                    27            NaN              81            NaN   \n", "top     2016-06-06T00:00:00Z            NaN  JARDIM CAMBURI            NaN   \n", "freq                    4692            NaN            7717            NaN   \n", "mean                     NaN      37.088874             NaN       0.098266   \n", "std                      NaN      23.110205             NaN       0.297675   \n", "min                      NaN      -1.000000             NaN       0.000000   \n", "25%                      NaN      18.000000             NaN       0.000000   \n", "50%                      NaN      37.000000             NaN       0.000000   \n", "75%                      NaN      55.000000             NaN       0.000000   \n", "max                      NaN     115.000000             NaN       1.000000   \n", "\n", "         Hipertension       Diabetes     Alcoholism        Handcap  \\\n", "count   110527.000000  110527.000000  110527.000000  110527.000000   \n", "unique            NaN            NaN            NaN            NaN   \n", "top               NaN            NaN            NaN            NaN   \n", "freq              NaN            NaN            NaN            NaN   \n", "mean         0.197246       0.071865       0.030400       0.022248   \n", "std          0.397921       0.258265       0.171686       0.161543   \n", "min          0.000000       0.000000       0.000000       0.000000   \n", "25%          0.000000       0.000000       0.000000       0.000000   \n", "50%          0.000000       0.000000       0.000000       0.000000   \n", "75%          0.000000       0.000000       0.000000       0.000000   \n", "max          1.000000       1.000000       1.000000       4.000000   \n", "\n", "         SMS_received No-show  \n", "count   110527.000000  110527  \n", "unique            NaN       2  \n", "top               NaN      No  \n", "freq              NaN   88208  \n", "mean         0.321026     NaN  \n", "std          0.466873     NaN  \n", "min          0.000000     NaN  \n", "25%          0.000000     NaN  \n", "50%          0.000000     NaN  \n", "75%          1.000000     NaN  \n", "max          1.000000     NaN  "], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PatientId</th>\n", "      <th>AppointmentID</th>\n", "      <th>Gender</th>\n", "      <th>ScheduledDay</th>\n", "      <th>AppointmentDay</th>\n", "      <th>Age</th>\n", "      <th>Neighbourhood</th>\n", "      <th>Scholarship</th>\n", "      <th>Hipertension</th>\n", "      <th>Diabetes</th>\n", "      <th>Alcoholism</th>\n", "      <th>Handcap</th>\n", "      <th>SMS_received</th>\n", "      <th>No-show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.105270e+05</td>\n", "      <td>1.105270e+05</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527.000000</td>\n", "      <td>110527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>103549</td>\n", "      <td>27</td>\n", "      <td>NaN</td>\n", "      <td>81</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>F</td>\n", "      <td>2016-05-06T07:09:54Z</td>\n", "      <td>2016-06-06T00:00:00Z</td>\n", "      <td>NaN</td>\n", "      <td>JARDIM CAMBURI</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>71840</td>\n", "      <td>24</td>\n", "      <td>4692</td>\n", "      <td>NaN</td>\n", "      <td>7717</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>88208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.474963e+14</td>\n", "      <td>5.675305e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>37.088874</td>\n", "      <td>NaN</td>\n", "      <td>0.098266</td>\n", "      <td>0.197246</td>\n", "      <td>0.071865</td>\n", "      <td>0.030400</td>\n", "      <td>0.022248</td>\n", "      <td>0.321026</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.560949e+14</td>\n", "      <td>7.129575e+04</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>23.110205</td>\n", "      <td>NaN</td>\n", "      <td>0.297675</td>\n", "      <td>0.397921</td>\n", "      <td>0.258265</td>\n", "      <td>0.171686</td>\n", "      <td>0.161543</td>\n", "      <td>0.466873</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>3.921784e+04</td>\n", "      <td>5.030230e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>4.172614e+12</td>\n", "      <td>5.640286e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>3.173184e+13</td>\n", "      <td>5.680573e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>37.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.439172e+13</td>\n", "      <td>5.725524e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>55.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.999816e+14</td>\n", "      <td>5.790484e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>115.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "execution_count": 28}, {"cell_type": "code", "id": "6436a46e5a9b3079", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:51.047280Z", "start_time": "2025-06-12T21:05:49.369633Z"}}, "source": ["df_raw.hist(figsize=(10, 10), bins=20, grid=False)"], "outputs": [{"data": {"text/plain": ["array([[<Axes: title={'center': 'PatientId'}>,\n", "        <Axes: title={'center': 'AppointmentID'}>,\n", "        <Axes: title={'center': 'Age'}>],\n", "       [<Axes: title={'center': 'Scholarship'}>,\n", "        <Axes: title={'center': 'Hipertension'}>,\n", "        <Axes: title={'center': 'Diabetes'}>],\n", "       [<Axes: title={'center': 'Alcoholism'}>,\n", "        <Axes: title={'center': 'Handcap'}>,\n", "        <Axes: title={'center': 'SMS_received'}>]], dtype=object)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["<Figure size 1000x1000 with 9 Axes>"], "image/png": "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*************************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 29}, {"cell_type": "code", "id": "116df1214de1420a", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:51.554194Z", "start_time": "2025-06-12T21:05:51.438270Z"}}, "source": ["# Display duplicates in the dataset\n", "print(f\"Duplicate rows in the dataset: {df_raw.duplicated().sum()}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Duplicate rows in the dataset: 0\n"]}], "execution_count": 30}, {"cell_type": "code", "id": "256a5debfc8cc3db", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:51.686642Z", "start_time": "2025-06-12T21:05:51.667521Z"}}, "source": ["# Display the unique patient's in the dataset\n", "print(f\"Unique Patients in the dataset: {df_raw['PatientId'].nunique()}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique Patients in the dataset: 62299\n"]}], "execution_count": 31}, {"cell_type": "code", "id": "1c2f132bdc4342fe", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:51.769507Z", "start_time": "2025-06-12T21:05:51.754171Z"}}, "source": ["# Display the unique appointments in the dataset\n", "print(f\"Unique Appointments in the dataset: {df_raw['AppointmentID'].nunique()}\")"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unique Appointments in the dataset: 110527\n"]}], "execution_count": 32}, {"cell_type": "code", "id": "8655bc80e4636376", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:51.882917Z", "start_time": "2025-06-12T21:05:51.873395Z"}}, "source": ["# Display `Age` column explore\n", "print(\"Value count\", df_raw['Age'].value_counts())"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Value count Age\n", " 0      3539\n", " 1      2273\n", " 52     1746\n", " 49     1652\n", " 53     1651\n", "        ... \n", " 115       5\n", " 100       4\n", " 102       2\n", " 99        1\n", "-1         1\n", "Name: count, Length: 104, dtype: int64\n"]}], "execution_count": 33}, {"cell_type": "code", "id": "a0826a68d72814bd", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:52.022839Z", "start_time": "2025-06-12T21:05:51.965732Z"}}, "source": ["df_raw.groupby(['Age', 'PatientId'])['Age'].count()"], "outputs": [{"data": {"text/plain": ["Age   PatientId   \n", "-1    4.659432e+14    1\n", " 0    2.699191e+08    2\n", "      3.424788e+08    1\n", "      4.279622e+08    1\n", "      1.192462e+09    1\n", "                     ..\n", " 100  5.578313e+13    1\n", " 102  2.342836e+11    1\n", "      9.762948e+14    1\n", " 115  3.196321e+13    4\n", "      7.482346<PERSON>+14    1\n", "Name: Age, Length: 63467, dtype: int64"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "execution_count": 34}, {"cell_type": "markdown", "id": "b6883afc68c0863e", "metadata": {}, "source": ["There are actually just two patients aged 115, which might be plausible as the maximum age of a human is 122. Let's keep it as it is."]}, {"cell_type": "code", "id": "f4fa722f5314bb9b", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:52.136916Z", "start_time": "2025-06-12T21:05:52.124594Z"}}, "source": ["df_raw['Handcap'].value_counts()"], "outputs": [{"data": {"text/plain": ["Handcap\n", "0    108286\n", "1      2042\n", "2       183\n", "3        13\n", "4         3\n", "Name: count, dtype: int64"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "execution_count": 35}, {"cell_type": "code", "id": "88091f0f050d533e", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:52.380230Z", "start_time": "2025-06-12T21:05:52.362196Z"}}, "source": ["df_raw['Neighbourhood'].value_counts()"], "outputs": [{"data": {"text/plain": ["Neighbourhood\n", "JARDIM CAMBURI                 7717\n", "MARIA ORTIZ                    5805\n", "RESISTÊNCIA                    4431\n", "JARDIM DA PENHA                3877\n", "ITARARÉ                        3514\n", "                               ... \n", "ILHA DO BOI                      35\n", "ILHA DO FRADE                    10\n", "AEROPORTO                         8\n", "ILHAS OCEÂNICAS DE TRINDADE       2\n", "PARQUE INDUSTRIAL                 1\n", "Name: count, Length: 81, dtype: int64"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "execution_count": 36}, {"cell_type": "code", "id": "26ab81b866f9a563", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:52.715739Z", "start_time": "2025-06-12T21:05:52.676980Z"}}, "source": ["df_raw.isnull().sum()"], "outputs": [{"data": {"text/plain": ["PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "execution_count": 37}, {"cell_type": "code", "id": "7d927fa8b2008dd9", "metadata": {"ExecuteTime": {"end_time": "2025-06-12T21:05:53.002687Z", "start_time": "2025-06-12T21:05:52.971531Z"}}, "source": ["df_raw.isna().sum()"], "outputs": [{"data": {"text/plain": ["PatientId         0\n", "AppointmentID     0\n", "Gender            0\n", "ScheduledDay      0\n", "AppointmentDay    0\n", "Age               0\n", "Neighbourhood     0\n", "Scholarship       0\n", "Hipertension      0\n", "Diabetes          0\n", "Alcoholism        0\n", "Handcap           0\n", "SMS_received      0\n", "No-show           0\n", "dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "execution_count": 38}, {"cell_type": "markdown", "id": "c282ac43611a535a", "metadata": {}, "source": ["## Characteristics of the Dataset\n", "\n", "Column needs to be transformed to appropriate data types.\n", "- Data Quality: All columns are present and without any missing values.\n", "- The columns -> `Scholarship`,`Hypertension`,`Diabetes`,`Alcoholism`, `Alcoholism` can be identified as `boolean` and should be converted to bool type.\n", "- The `Gender` and `Handcap` columns should be converted to a categorical data type. The Handicap column is categorical; for instance, a value of 4 signifies that a patient has four distinct handicaps.\n", "- The `Scheduled Day` and `Appointment Day` column need to be transformed to datetime type. The <i>ScheduledDay</i> and <i>AppointmentDay</i> has `00:00:00:00` in its TimeStamp, we will ignore it.\n", "- `Neighbourhood` needs further investigation to identify the type of data it contains.\n", "- PatientId of type `float`, but they should be `int64`\n", "- Age has a value of -1 which is not valid. This should be corrected.\n", "- Typo corrections and standardisation:\n", "   - `Hipertension` should be corrected to `hypertension`.\n", "   - `Handcap` should be corrected to `handicap`.\n", "   - `SMS_received` should be corrected to `sms_Received`.\n", "   - `No-show` should be corrected to `no_show`.\n", "\n", "From the above information of the DataFrame and the sample data we can see that there are 14 columns in total.\n", "\n", "- There are 13 independent Variables -> [`PatientId`,`AppointmentID`,`Gender`,`ScheduleDay`,`AppointmentDay`,`Age`,`Neighbourhood`,`Scholarship`,`Hipertension`,`Diabetes`,'Alcoholism`,'Handcap`,`SMSReceived`]\n", "- The Dependent Variable is -> [`NoShow`]\n", "- The independent variables -> [`PatientId`,`AppointmentID`]\n", "- `AppointmentID` does not interesting in the context of our analysis, so let's dropped during data cleaning phase.\n", "- Categorical field `Gener`, `Neighbourhood`, `No-show` should be encoded for modelling.\n", "- There are 81 places, some of them with large number of appointments, it makes sense to categorise it"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}