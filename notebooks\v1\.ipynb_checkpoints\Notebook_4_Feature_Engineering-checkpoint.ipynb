{"cells": [{"cell_type": "markdown", "id": "224aa5636ac7adc4", "metadata": {}, "source": ["# 4. Feature Engineering"]}, {"cell_type": "code", "execution_count": 8, "id": "abcc8757400eb5b8", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:27:33.549556Z", "start_time": "2025-06-14T13:27:30.435998Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ace_tools in c:\\programdata\\anaconda3\\lib\\site-packages (0.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install ace_tools"]}, {"cell_type": "code", "execution_count": 3, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:27:33.619785Z", "start_time": "2025-06-14T13:27:33.613293Z"}}, "outputs": [], "source": ["import pandas as pd\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.preprocessing import LabelEncoder\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 4, "id": "68d93330", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:55.166869Z", "start_time": "2025-06-14T13:23:54.820572Z"}}, "outputs": [], "source": ["original_data = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/data/raw/medical_appointment_no_show.csv\")"]}, {"cell_type": "code", "execution_count": 5, "id": "6fe255cd778f5323", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:55.447390Z", "start_time": "2025-06-14T13:23:55.253526Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 16 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "       handicap  sms_received  no_show  lead_time appointment_day_of_week  \n", "0             0         False    False          2                  Friday  \n", "1             0         False     True          2                  Friday  \n", "2             0         False     True          2                  Friday  \n", "3             0         False    False          2                  Friday  \n", "4             0         False    False          2                  Friday  \n", "...         ...           ...      ...        ...                     ...  \n", "71954         0          True    False         35                 Tuesday  \n", "71955         0          True    False         35                 Tuesday  \n", "71956         0          True    False         41                 Tuesday  \n", "71957         0          True    False         41                 Tuesday  \n", "71958         0          True    False         41                 Tuesday  \n", "\n", "[71959 rows x 16 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = pd.read_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/data/processed/medical_appointment_no_show_processed.csv\")\n", "df_fe"]}, {"cell_type": "code", "execution_count": 170, "id": "3d4d17c5", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:55.618482Z", "start_time": "2025-06-14T13:23:55.584386Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 16 columns):\n", " #   Column                   Non-Null Count  Dtype \n", "---  ------                   --------------  ----- \n", " 0   patient_id               71959 non-null  int64 \n", " 1   appointment_id           71959 non-null  int64 \n", " 2   gender                   71959 non-null  object\n", " 3   scheduled_day            71959 non-null  object\n", " 4   appointment_day          71959 non-null  object\n", " 5   age                      71959 non-null  int64 \n", " 6   neighbourhood            71959 non-null  object\n", " 7   scholarship              71959 non-null  bool  \n", " 8   hypertension             71959 non-null  bool  \n", " 9   diabetes                 71959 non-null  bool  \n", " 10  alcoholism               71959 non-null  bool  \n", " 11  handicap                 71959 non-null  int64 \n", " 12  sms_received             71959 non-null  bool  \n", " 13  no_show                  71959 non-null  bool  \n", " 14  lead_time                71959 non-null  int64 \n", " 15  appointment_day_of_week  71959 non-null  object\n", "dtypes: bool(6), int64(5), object(5)\n", "memory usage: 5.9+ MB\n"]}], "source": ["df_fe.info()"]}, {"cell_type": "code", "execution_count": 171, "id": "8e5c2cdb", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:55.982519Z", "start_time": "2025-06-14T13:23:55.825304Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 16 columns):\n", " #   Column                   Non-Null Count  Dtype         \n", "---  ------                   --------------  -----         \n", " 0   patient_id               71959 non-null  int64         \n", " 1   appointment_id           71959 non-null  int64         \n", " 2   gender                   71959 non-null  category      \n", " 3   scheduled_day            71959 non-null  datetime64[ns]\n", " 4   appointment_day          71959 non-null  datetime64[ns]\n", " 5   age                      71959 non-null  int64         \n", " 6   neighbourhood            71959 non-null  category      \n", " 7   scholarship              71959 non-null  bool          \n", " 8   hypertension             71959 non-null  bool          \n", " 9   diabetes                 71959 non-null  bool          \n", " 10  alcoholism               71959 non-null  bool          \n", " 11  handicap                 71959 non-null  category      \n", " 12  sms_received             71959 non-null  bool          \n", " 13  no_show                  71959 non-null  bool          \n", " 14  lead_time                71959 non-null  int64         \n", " 15  appointment_day_of_week  71959 non-null  category      \n", "dtypes: bool(6), category(4), datetime64[ns](2), int64(4)\n", "memory usage: 4.0 MB\n"]}], "source": ["# Convert `gender`, `neighbourhood`, `handicap`, to categorical type\n", "for col in ['gender', 'neighbourhood', 'handicap', 'appointment_day_of_week']:\n", "    df_fe[col] = df_fe[col].astype('category')\n", "\n", "# Convert `scheduled_day` and `appointment_day` to datetime\n", "for col in ['scheduled_day', 'appointment_day']:\n", "    df_fe[col] = pd.to_datetime(df_fe[col]).dt.date.astype('datetime64[ns]')\n", "    \n", "df_fe.info()"]}, {"cell_type": "code", "execution_count": 172, "id": "1c76fbc271e543ab", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:56.135954Z", "start_time": "2025-06-14T13:23:56.126762Z"}}, "outputs": [{"data": {"text/plain": ["Index(['patient_id', 'appointment_id', 'gender', 'scheduled_day',\n", "       'appointment_day', 'age', 'neighbourhood', 'scholarship',\n", "       'hypertension', 'diabetes', 'alcoholism', 'handicap', 'sms_received',\n", "       'no_show', 'lead_time', 'appointment_day_of_week'],\n", "      dtype='object')"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe.columns"]}, {"cell_type": "markdown", "id": "5182891c", "metadata": {}, "source": ["### Create new features"]}, {"cell_type": "markdown", "id": "0d35da3a", "metadata": {}, "source": ["1. Extract the hour of the day when the appointment was scheduled"]}, {"cell_type": "code", "execution_count": 173, "id": "e81b50c5", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:56.581593Z", "start_time": "2025-06-14T13:23:56.266863Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 17 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "      handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0            0         False    False          2                  Friday   \n", "1            0         False     True          2                  Friday   \n", "2            0         False     True          2                  Friday   \n", "3            0         False    False          2                  Friday   \n", "4            0         False    False          2                  Friday   \n", "...        ...           ...      ...        ...                     ...   \n", "71954        0          True    False         35                 Tuesday   \n", "71955        0          True    False         35                 Tuesday   \n", "71956        0          True    False         41                 Tuesday   \n", "71957        0          True    False         41                 Tuesday   \n", "71958        0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours  \n", "0                   18  \n", "1                   16  \n", "2                   16  \n", "3                   17  \n", "4                   16  \n", "...                ...  \n", "71954               13  \n", "71955               16  \n", "71956                7  \n", "71957                7  \n", "71958                7  \n", "\n", "[71959 rows x 17 columns]"]}, "execution_count": 173, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['scheduled_hours'] = pd.to_datetime(original_data['ScheduledDay']).dt.hour.astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "e800a365", "metadata": {}, "source": ["2. Create Age Groups"]}, {"cell_type": "code", "execution_count": 174, "id": "5aca5616", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:56.817292Z", "start_time": "2025-06-14T13:23:56.778917Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 18 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "      handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0            0         False    False          2                  Friday   \n", "1            0         False     True          2                  Friday   \n", "2            0         False     True          2                  Friday   \n", "3            0         False    False          2                  Friday   \n", "4            0         False    False          2                  Friday   \n", "...        ...           ...      ...        ...                     ...   \n", "71954        0          True    False         35                 Tuesday   \n", "71955        0          True    False         35                 Tuesday   \n", "71956        0          True    False         41                 Tuesday   \n", "71957        0          True    False         41                 Tuesday   \n", "71958        0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  \n", "0                   18       66+  \n", "1                   16     19-35  \n", "2                   16     36-60  \n", "3                   17     19-35  \n", "4                   16     19-35  \n", "...                ...       ...  \n", "71954               13     51-65  \n", "71955               16     51-65  \n", "71956                7     19-35  \n", "71957                7     36-60  \n", "71958                7     51-65  \n", "\n", "[71959 rows x 18 columns]"]}, "execution_count": 174, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['age_group'] = pd.cut(df_fe['age'], bins=[0, 18, 35, 50, 65, 100], labels=['0-18', '19-35', '36-60', '51-65', '66+'])\n", "df_fe"]}, {"cell_type": "markdown", "id": "4d394cde", "metadata": {}, "source": ["3. Create a new column for the total number of chronic conditions"]}, {"cell_type": "code", "execution_count": 175, "id": "6c3d9771", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:57.038588Z", "start_time": "2025-06-14T13:23:57.033054Z"}}, "outputs": [], "source": ["# df_fe['hypertension'] = df_fe['hypertension'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['hypertension'].astype('int64')\n", "\n", "# df_fe['diabetes'] = df_fe['diabetes'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['alcoholism'] = df_fe['alcoholism'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['handicap'] = df_fe['handicap'].replace({0: 'False', 1: 'True'}).infer_objects(copy=False)\n", "# df_fe['diabetes'].astype('int64')\n", "\n", "# df_fe['total_chronic_conditions'] = df_fe[['hypertension', 'diabetes', 'alcoholism', 'handicap']].sum(axis=1)\n", "# df_fe"]}, {"cell_type": "markdown", "id": "649474b9", "metadata": {}, "source": ["4. Create a new column to indicate if the appointment is on a weekend"]}, {"cell_type": "code", "execution_count": 176, "id": "95cf8f8c", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:57.335528Z", "start_time": "2025-06-14T13:23:57.276239Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 19 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "      handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0            0         False    False          2                  Friday   \n", "1            0         False     True          2                  Friday   \n", "2            0         False     True          2                  Friday   \n", "3            0         False    False          2                  Friday   \n", "4            0         False    False          2                  Friday   \n", "...        ...           ...      ...        ...                     ...   \n", "71954        0          True    False         35                 Tuesday   \n", "71955        0          True    False         35                 Tuesday   \n", "71956        0          True    False         41                 Tuesday   \n", "71957        0          True    False         41                 Tuesday   \n", "71958        0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend  \n", "0                   18       66+           0  \n", "1                   16     19-35           0  \n", "2                   16     36-60           0  \n", "3                   17     19-35           0  \n", "4                   16     19-35           0  \n", "...                ...       ...         ...  \n", "71954               13     51-65           0  \n", "71955               16     51-65           0  \n", "71956                7     19-35           0  \n", "71957                7     36-60           0  \n", "71958                7     51-65           0  \n", "\n", "[71959 rows x 19 columns]"]}, "execution_count": 176, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['is_weekend'] = pd.to_datetime(df_fe['appointment_day']).dt.dayofweek.isin([5, 6]).astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "a0309b21", "metadata": {}, "source": ["5. Create a new feature for SMS Reminders"]}, {"cell_type": "code", "execution_count": 177, "id": "9c1844fd", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:57.605440Z", "start_time": "2025-06-14T13:23:57.576108Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 20 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "      handicap  sms_received  no_show  lead_time appointment_day_of_week  \\\n", "0            0         False    False          2                  Friday   \n", "1            0         False     True          2                  Friday   \n", "2            0         False     True          2                  Friday   \n", "3            0         False    False          2                  Friday   \n", "4            0         False    False          2                  Friday   \n", "...        ...           ...      ...        ...                     ...   \n", "71954        0          True    False         35                 Tuesday   \n", "71955        0          True    False         35                 Tuesday   \n", "71956        0          True    False         41                 Tuesday   \n", "71957        0          True    False         41                 Tuesday   \n", "71958        0          True    False         41                 Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend received_sms  \n", "0                   18       66+           0           No  \n", "1                   16     19-35           0           No  \n", "2                   16     36-60           0           No  \n", "3                   17     19-35           0           No  \n", "4                   16     19-35           0           No  \n", "...                ...       ...         ...          ...  \n", "71954               13     51-65           0          Yes  \n", "71955               16     51-65           0          Yes  \n", "71956                7     19-35           0          Yes  \n", "71957                7     36-60           0          Yes  \n", "71958                7     51-65           0          Yes  \n", "\n", "[71959 rows x 20 columns]"]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['received_sms'] = df_fe['sms_received'].map({False: 'No', True: 'Yes'})\n", "df_fe"]}, {"cell_type": "markdown", "id": "a550732d", "metadata": {}, "source": ["6. Create a feature for the number of day until the next appointment."]}, {"cell_type": "code", "execution_count": 178, "id": "92068b1d", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:57.998999Z", "start_time": "2025-06-14T13:23:57.920370Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 21 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  \\\n", "0       76       REPÚBLICA        False          True     False  ...   \n", "1       23      GOIABEIRAS        False         False     False  ...   \n", "2       39      GOIABEIRAS        False         False     False  ...   \n", "3       19       CONQUISTA        False         False     False  ...   \n", "4       30  NOVA PALESTINA        False         False     False  ...   \n", "...    ...             ...          ...           ...       ...  ...   \n", "71954   56     MARIA ORTIZ        False         False     False  ...   \n", "71955   51     MARIA ORTIZ        False         False     False  ...   \n", "71956   21     MARIA ORTIZ        False         False     False  ...   \n", "71957   38     MARIA ORTIZ        False         False     False  ...   \n", "71958   54     MARIA ORTIZ        False         False     False  ...   \n", "\n", "       handicap sms_received  no_show  lead_time  appointment_day_of_week  \\\n", "0             0        False    False          2                   Friday   \n", "1             0        False     True          2                   Friday   \n", "2             0        False     True          2                   Friday   \n", "3             0        False    False          2                   Friday   \n", "4             0        False    False          2                   Friday   \n", "...         ...          ...      ...        ...                      ...   \n", "71954         0         True    False         35                  Tuesday   \n", "71955         0         True    False         35                  Tuesday   \n", "71956         0         True    False         41                  Tuesday   \n", "71957         0         True    False         41                  Tuesday   \n", "71958         0         True    False         41                  Tuesday   \n", "\n", "      scheduled_hours  age_group is_weekend  received_sms  \\\n", "0                  18        66+          0            No   \n", "1                  16      19-35          0            No   \n", "2                  16      36-60          0            No   \n", "3                  17      19-35          0            No   \n", "4                  16      19-35          0            No   \n", "...               ...        ...        ...           ...   \n", "71954              13      51-65          0           Yes   \n", "71955              16      51-65          0           Yes   \n", "71956               7      19-35          0           Yes   \n", "71957               7      36-60          0           Yes   \n", "71958               7      51-65          0           Yes   \n", "\n", "      days_until_next_appointment  \n", "0                              33  \n", "1                               0  \n", "2                              20  \n", "3                               0  \n", "4                               0  \n", "...                           ...  \n", "71954                           0  \n", "71955                           0  \n", "71956                           0  \n", "71957                           0  \n", "71958                           0  \n", "\n", "[71959 rows x 21 columns]"]}, "execution_count": 178, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['days_until_next_appointment'] = (df_fe.groupby('patient_id')['appointment_day'].shift(-1) - df_fe['appointment_day']).dt.days.fillna(0).astype('int64')\n", "df_fe"]}, {"cell_type": "markdown", "id": "cec9c2ef", "metadata": {}, "source": ["7. Create a feature for the number of previous no-show by the patient"]}, {"cell_type": "code", "execution_count": 179, "id": "5f6b5371", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:58.422034Z", "start_time": "2025-06-14T13:23:58.343288Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "      <th>previous_no_show</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 22 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  \\\n", "0       76       REPÚBLICA        False          True     False  ...   \n", "1       23      GOIABEIRAS        False         False     False  ...   \n", "2       39      GOIABEIRAS        False         False     False  ...   \n", "3       19       CONQUISTA        False         False     False  ...   \n", "4       30  NOVA PALESTINA        False         False     False  ...   \n", "...    ...             ...          ...           ...       ...  ...   \n", "71954   56     MARIA ORTIZ        False         False     False  ...   \n", "71955   51     MARIA ORTIZ        False         False     False  ...   \n", "71956   21     MARIA ORTIZ        False         False     False  ...   \n", "71957   38     MARIA ORTIZ        False         False     False  ...   \n", "71958   54     MARIA ORTIZ        False         False     False  ...   \n", "\n", "       sms_received no_show  lead_time  appointment_day_of_week  \\\n", "0             False   False          2                   Friday   \n", "1             False    True          2                   Friday   \n", "2             False    True          2                   Friday   \n", "3             False   False          2                   Friday   \n", "4             False   False          2                   Friday   \n", "...             ...     ...        ...                      ...   \n", "71954          True   False         35                  Tuesday   \n", "71955          True   False         35                  Tuesday   \n", "71956          True   False         41                  Tuesday   \n", "71957          True   False         41                  Tuesday   \n", "71958          True   False         41                  Tuesday   \n", "\n", "       scheduled_hours age_group  is_weekend received_sms  \\\n", "0                   18       66+           0           No   \n", "1                   16     19-35           0           No   \n", "2                   16     36-60           0           No   \n", "3                   17     19-35           0           No   \n", "4                   16     19-35           0           No   \n", "...                ...       ...         ...          ...   \n", "71954               13     51-65           0          Yes   \n", "71955               16     51-65           0          Yes   \n", "71956                7     19-35           0          Yes   \n", "71957                7     36-60           0          Yes   \n", "71958                7     51-65           0          Yes   \n", "\n", "       days_until_next_appointment previous_no_show  \n", "0                               33                0  \n", "1                                0                0  \n", "2                               20                0  \n", "3                                0                0  \n", "4                                0                0  \n", "...                            ...              ...  \n", "71954                            0                1  \n", "71955                            0                1  \n", "71956                            0                0  \n", "71957                            0                0  \n", "71958                            0                0  \n", "\n", "[71959 rows x 22 columns]"]}, "execution_count": 179, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['previous_no_show'] = df_fe.groupby('patient_id')['no_show'].cumsum() - df_fe['no_show']\n", "df_fe"]}, {"cell_type": "markdown", "id": "fc837eca", "metadata": {}, "source": ["8. Create a feature for the total number of appointments per patient"]}, {"cell_type": "code", "execution_count": 180, "id": "2e2a2695", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:58.882330Z", "start_time": "2025-06-14T13:23:58.795409Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>gender</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>...</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>appointment_day_of_week</th>\n", "      <th>scheduled_hours</th>\n", "      <th>age_group</th>\n", "      <th>is_weekend</th>\n", "      <th>received_sms</th>\n", "      <th>days_until_next_appointment</th>\n", "      <th>previous_no_show</th>\n", "      <th>total_appointments</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>18</td>\n", "      <td>66+</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>20</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>17</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>Friday</td>\n", "      <td>16</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>13</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>F</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>Tuesday</td>\n", "      <td>16</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>19-35</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>36-60</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>F</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>Tuesday</td>\n", "      <td>7</td>\n", "      <td>51-65</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 23 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id gender scheduled_day appointment_day  \\\n", "0       95985133231274         5626772      F    2016-04-27      2016-04-29   \n", "1      733688164476661         5630279      F    2016-04-27      2016-04-29   \n", "2        3449833394123         5630575      F    2016-04-27      2016-04-29   \n", "3       78124564369297         5629123      F    2016-04-27      2016-04-29   \n", "4      734536231958495         5630213      F    2016-04-27      2016-04-29   \n", "...                ...             ...    ...           ...             ...   \n", "71954    2572134369293         5651768      F    2016-05-03      2016-06-07   \n", "71955    3596266328735         5650093      F    2016-05-03      2016-06-07   \n", "71956   15576631729893         5630692      F    2016-04-27      2016-06-07   \n", "71957   92134931435557         5630323      F    2016-04-27      2016-06-07   \n", "71958  377511518121127         5629448      F    2016-04-27      2016-06-07   \n", "\n", "       age   neighbourhood  scholarship  hypertension  diabetes  ...  no_show  \\\n", "0       76       REPÚBLICA        False          True     False  ...    False   \n", "1       23      GOIABEIRAS        False         False     False  ...     True   \n", "2       39      GOIABEIRAS        False         False     False  ...     True   \n", "3       19       CONQUISTA        False         False     False  ...    False   \n", "4       30  NOVA PALESTINA        False         False     False  ...    False   \n", "...    ...             ...          ...           ...       ...  ...      ...   \n", "71954   56     MARIA ORTIZ        False         False     False  ...    False   \n", "71955   51     MARIA ORTIZ        False         False     False  ...    False   \n", "71956   21     MARIA ORTIZ        False         False     False  ...    False   \n", "71957   38     MARIA ORTIZ        False         False     False  ...    False   \n", "71958   54     MARIA ORTIZ        False         False     False  ...    False   \n", "\n", "      lead_time  appointment_day_of_week  scheduled_hours  age_group  \\\n", "0             2                   Friday               18        66+   \n", "1             2                   Friday               16      19-35   \n", "2             2                   Friday               16      36-60   \n", "3             2                   Friday               17      19-35   \n", "4             2                   Friday               16      19-35   \n", "...         ...                      ...              ...        ...   \n", "71954        35                  Tuesday               13      51-65   \n", "71955        35                  Tuesday               16      51-65   \n", "71956        41                  Tuesday                7      19-35   \n", "71957        41                  Tuesday                7      36-60   \n", "71958        41                  Tuesday                7      51-65   \n", "\n", "      is_weekend  received_sms days_until_next_appointment  previous_no_show  \\\n", "0              0            No                          33                 0   \n", "1              0            No                           0                 0   \n", "2              0            No                          20                 0   \n", "3              0            No                           0                 0   \n", "4              0            No                           0                 0   \n", "...          ...           ...                         ...               ...   \n", "71954          0           Yes                           0                 1   \n", "71955          0           Yes                           0                 1   \n", "71956          0           Yes                           0                 0   \n", "71957          0           Yes                           0                 0   \n", "71958          0           Yes                           0                 0   \n", "\n", "      total_appointments  \n", "0                      2  \n", "1                      1  \n", "2                      2  \n", "3                      1  \n", "4                      1  \n", "...                  ...  \n", "71954                  2  \n", "71955                  3  \n", "71956                  1  \n", "71957                  2  \n", "71958                  2  \n", "\n", "[71959 rows x 23 columns]"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe['total_appointments'] = df_fe.groupby('patient_id')['appointment_id'].transform('count')\n", "df_fe"]}, {"cell_type": "markdown", "id": "751a59b3", "metadata": {}, "source": ["9. Encode categorical variables as needed for modeling"]}, {"cell_type": "code", "execution_count": 181, "id": "3ce35d4f", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:59.312425Z", "start_time": "2025-06-14T13:23:59.225442Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>patient_id</th>\n", "      <th>appointment_id</th>\n", "      <th>scheduled_day</th>\n", "      <th>appointment_day</th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>...</th>\n", "      <th>age_group_19-35</th>\n", "      <th>age_group_36-60</th>\n", "      <th>age_group_51-65</th>\n", "      <th>age_group_66+</th>\n", "      <th>received_sms_Yes</th>\n", "      <th>appointment_day_of_week_Monday</th>\n", "      <th>appointment_day_of_week_Saturday</th>\n", "      <th>appointment_day_of_week_Thursday</th>\n", "      <th>appointment_day_of_week_Tuesday</th>\n", "      <th>appointment_day_of_week_Wednesday</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>95985133231274</td>\n", "      <td>5626772</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>733688164476661</td>\n", "      <td>5630279</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3449833394123</td>\n", "      <td>5630575</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>78124564369297</td>\n", "      <td>5629123</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>734536231958495</td>\n", "      <td>5630213</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-04-29</td>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>2572134369293</td>\n", "      <td>5651768</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>3596266328735</td>\n", "      <td>5650093</td>\n", "      <td>2016-05-03</td>\n", "      <td>2016-06-07</td>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>15576631729893</td>\n", "      <td>5630692</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>92134931435557</td>\n", "      <td>5630323</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>377511518121127</td>\n", "      <td>5629448</td>\n", "      <td>2016-04-27</td>\n", "      <td>2016-06-07</td>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 30 columns</p>\n", "</div>"], "text/plain": ["            patient_id  appointment_id scheduled_day appointment_day  age  \\\n", "0       95985133231274         5626772    2016-04-27      2016-04-29   76   \n", "1      733688164476661         5630279    2016-04-27      2016-04-29   23   \n", "2        3449833394123         5630575    2016-04-27      2016-04-29   39   \n", "3       78124564369297         5629123    2016-04-27      2016-04-29   19   \n", "4      734536231958495         5630213    2016-04-27      2016-04-29   30   \n", "...                ...             ...           ...             ...  ...   \n", "71954    2572134369293         5651768    2016-05-03      2016-06-07   56   \n", "71955    3596266328735         5650093    2016-05-03      2016-06-07   51   \n", "71956   15576631729893         5630692    2016-04-27      2016-06-07   21   \n", "71957   92134931435557         5630323    2016-04-27      2016-06-07   38   \n", "71958  377511518121127         5629448    2016-04-27      2016-06-07   54   \n", "\n", "        neighbourhood  scholarship  hypertension  diabetes  alcoholism  ...  \\\n", "0           REPÚBLICA        False          True     False       False  ...   \n", "1          GOIABEIRAS        False         False     False       False  ...   \n", "2          GOIABEIRAS        False         False     False       False  ...   \n", "3           CONQUISTA        False         False     False       False  ...   \n", "4      NOVA PALESTINA        False         False     False       False  ...   \n", "...               ...          ...           ...       ...         ...  ...   \n", "71954     MARIA ORTIZ        False         False     False       False  ...   \n", "71955     MARIA ORTIZ        False         False     False       False  ...   \n", "71956     MARIA ORTIZ        False         False     False       False  ...   \n", "71957     MARIA ORTIZ        False         False     False       False  ...   \n", "71958     MARIA ORTIZ        False         False     False       False  ...   \n", "\n", "      age_group_19-35  age_group_36-60  age_group_51-65  age_group_66+  \\\n", "0               False            False            False           True   \n", "1                True            False            False          False   \n", "2               False             True            False          False   \n", "3                True            False            False          False   \n", "4                True            False            False          False   \n", "...               ...              ...              ...            ...   \n", "71954           False            False             True          False   \n", "71955           False            False             True          False   \n", "71956            True            False            False          False   \n", "71957           False             True            False          False   \n", "71958           False            False             True          False   \n", "\n", "       received_sms_Yes  appointment_day_of_week_Monday  \\\n", "0                 False                           False   \n", "1                 False                           False   \n", "2                 False                           False   \n", "3                 False                           False   \n", "4                 False                           False   \n", "...                 ...                             ...   \n", "71954              True                           False   \n", "71955              True                           False   \n", "71956              True                           False   \n", "71957              True                           False   \n", "71958              True                           False   \n", "\n", "       appointment_day_of_week_Saturday  appointment_day_of_week_Thursday  \\\n", "0                                 False                             False   \n", "1                                 False                             False   \n", "2                                 False                             False   \n", "3                                 False                             False   \n", "4                                 False                             False   \n", "...                                 ...                               ...   \n", "71954                             False                             False   \n", "71955                             False                             False   \n", "71956                             False                             False   \n", "71957                             False                             False   \n", "71958                             False                             False   \n", "\n", "       appointment_day_of_week_Tuesday  appointment_day_of_week_Wednesday  \n", "0                                False                              False  \n", "1                                False                              False  \n", "2                                False                              False  \n", "3                                False                              False  \n", "4                                False                              False  \n", "...                                ...                                ...  \n", "71954                             True                              False  \n", "71955                             True                              False  \n", "71956                             True                              False  \n", "71957                             True                              False  \n", "71958                             True                              False  \n", "\n", "[71959 rows x 30 columns]"]}, "execution_count": 181, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = pd.get_dummies(df_fe, columns=['gender','age_group','received_sms','appointment_day_of_week'], drop_first=True)\n", "df_fe"]}, {"cell_type": "markdown", "id": "3bf934cc", "metadata": {}, "source": ["10. Drop columns irrelevant columns"]}, {"cell_type": "code", "execution_count": 182, "id": "a4913f61", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:23:59.717375Z", "start_time": "2025-06-14T13:23:59.665526Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>neighbourhood</th>\n", "      <th>scholarship</th>\n", "      <th>hypertension</th>\n", "      <th>diabetes</th>\n", "      <th>alcoholism</th>\n", "      <th>handicap</th>\n", "      <th>sms_received</th>\n", "      <th>no_show</th>\n", "      <th>lead_time</th>\n", "      <th>...</th>\n", "      <th>age_group_19-35</th>\n", "      <th>age_group_36-60</th>\n", "      <th>age_group_51-65</th>\n", "      <th>age_group_66+</th>\n", "      <th>received_sms_Yes</th>\n", "      <th>appointment_day_of_week_Monday</th>\n", "      <th>appointment_day_of_week_Saturday</th>\n", "      <th>appointment_day_of_week_Thursday</th>\n", "      <th>appointment_day_of_week_Tuesday</th>\n", "      <th>appointment_day_of_week_Wednesday</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>76</td>\n", "      <td>REPÚBLICA</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>23</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39</td>\n", "      <td>GOIABEIRAS</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>CONQUISTA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>30</td>\n", "      <td>NOVA PALESTINA</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71954</th>\n", "      <td>56</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71955</th>\n", "      <td>51</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>35</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71956</th>\n", "      <td>21</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71957</th>\n", "      <td>38</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71958</th>\n", "      <td>54</td>\n", "      <td>MARIA ORTIZ</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>41</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71959 rows × 26 columns</p>\n", "</div>"], "text/plain": ["       age   neighbourhood  scholarship  hypertension  diabetes  alcoholism  \\\n", "0       76       REPÚBLICA        False          True     False       False   \n", "1       23      GOIABEIRAS        False         False     False       False   \n", "2       39      GOIABEIRAS        False         False     False       False   \n", "3       19       CONQUISTA        False         False     False       False   \n", "4       30  NOVA PALESTINA        False         False     False       False   \n", "...    ...             ...          ...           ...       ...         ...   \n", "71954   56     MARIA ORTIZ        False         False     False       False   \n", "71955   51     MARIA ORTIZ        False         False     False       False   \n", "71956   21     MARIA ORTIZ        False         False     False       False   \n", "71957   38     MARIA ORTIZ        False         False     False       False   \n", "71958   54     MARIA ORTIZ        False         False     False       False   \n", "\n", "      handicap  sms_received  no_show  lead_time  ...  age_group_19-35  \\\n", "0            0         False    False          2  ...            False   \n", "1            0         False     True          2  ...             True   \n", "2            0         False     True          2  ...            False   \n", "3            0         False    False          2  ...             True   \n", "4            0         False    False          2  ...             True   \n", "...        ...           ...      ...        ...  ...              ...   \n", "71954        0          True    False         35  ...            False   \n", "71955        0          True    False         35  ...            False   \n", "71956        0          True    False         41  ...             True   \n", "71957        0          True    False         41  ...            False   \n", "71958        0          True    False         41  ...            False   \n", "\n", "       age_group_36-60  age_group_51-65  age_group_66+  received_sms_Yes  \\\n", "0                False            False           True             False   \n", "1                False            False          False             False   \n", "2                 True            False          False             False   \n", "3                False            False          False             False   \n", "4                False            False          False             False   \n", "...                ...              ...            ...               ...   \n", "71954            False             True          False              True   \n", "71955            False             True          False              True   \n", "71956            False            False          False              True   \n", "71957             True            False          False              True   \n", "71958            False             True          False              True   \n", "\n", "       appointment_day_of_week_Monday  appointment_day_of_week_Saturday  \\\n", "0                               False                             False   \n", "1                               False                             False   \n", "2                               False                             False   \n", "3                               False                             False   \n", "4                               False                             False   \n", "...                               ...                               ...   \n", "71954                           False                             False   \n", "71955                           False                             False   \n", "71956                           False                             False   \n", "71957                           False                             False   \n", "71958                           False                             False   \n", "\n", "       appointment_day_of_week_Thursday  appointment_day_of_week_Tuesday  \\\n", "0                                 False                            False   \n", "1                                 False                            False   \n", "2                                 False                            False   \n", "3                                 False                            False   \n", "4                                 False                            False   \n", "...                                 ...                              ...   \n", "71954                             False                             True   \n", "71955                             False                             True   \n", "71956                             False                             True   \n", "71957                             False                             True   \n", "71958                             False                             True   \n", "\n", "       appointment_day_of_week_Wednesday  \n", "0                                  False  \n", "1                                  False  \n", "2                                  False  \n", "3                                  False  \n", "4                                  False  \n", "...                                  ...  \n", "71954                              False  \n", "71955                              False  \n", "71956                              False  \n", "71957                              False  \n", "71958                              False  \n", "\n", "[71959 rows x 26 columns]"]}, "execution_count": 182, "metadata": {}, "output_type": "execute_result"}], "source": ["df_fe = df_fe.drop(columns=['appointment_id', 'patient_id', 'scheduled_day', 'appointment_day'], axis=1, inplace=False)\n", "df_fe"]}, {"cell_type": "code", "execution_count": 183, "id": "532d79cdafff1fd2", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:24:00.105493Z", "start_time": "2025-06-14T13:24:00.082388Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 71959 entries, 0 to 71958\n", "Data columns (total 26 columns):\n", " #   Column                             Non-Null Count  Dtype   \n", "---  ------                             --------------  -----   \n", " 0   age                                71959 non-null  int64   \n", " 1   neighbourhood                      71959 non-null  category\n", " 2   scholarship                        71959 non-null  bool    \n", " 3   hypertension                       71959 non-null  bool    \n", " 4   diabetes                           71959 non-null  bool    \n", " 5   alcoholism                         71959 non-null  bool    \n", " 6   handicap                           71959 non-null  category\n", " 7   sms_received                       71959 non-null  bool    \n", " 8   no_show                            71959 non-null  bool    \n", " 9   lead_time                          71959 non-null  int64   \n", " 10  scheduled_hours                    71959 non-null  int64   \n", " 11  is_weekend                         71959 non-null  int64   \n", " 12  days_until_next_appointment        71959 non-null  int64   \n", " 13  previous_no_show                   71959 non-null  int64   \n", " 14  total_appointments                 71959 non-null  int64   \n", " 15  gender_M                           71959 non-null  bool    \n", " 16  age_group_19-35                    71959 non-null  bool    \n", " 17  age_group_36-60                    71959 non-null  bool    \n", " 18  age_group_51-65                    71959 non-null  bool    \n", " 19  age_group_66+                      71959 non-null  bool    \n", " 20  received_sms_Yes                   71959 non-null  bool    \n", " 21  appointment_day_of_week_Monday     71959 non-null  bool    \n", " 22  appointment_day_of_week_Saturday   71959 non-null  bool    \n", " 23  appointment_day_of_week_Thursday   71959 non-null  bool    \n", " 24  appointment_day_of_week_Tuesday    71959 non-null  bool    \n", " 25  appointment_day_of_week_Wednesday  71959 non-null  bool    \n", "dtypes: bool(17), category(2), int64(7)\n", "memory usage: 5.1 MB\n"]}], "source": ["df_fe.info()"]}, {"cell_type": "code", "execution_count": null, "id": "a11d1a34496a92f7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 184, "id": "3117e076b16be241", "metadata": {"ExecuteTime": {"end_time": "2025-06-14T13:24:01.187423Z", "start_time": "2025-06-14T13:24:00.541883Z"}}, "outputs": [], "source": ["df_fe.to_csv(\"C:/Research/Msc/CMM709/CAUSALITY-EXPLORE/data/processed/medical_appointment_no_show_final.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}