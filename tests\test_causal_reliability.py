"""
Test script to verify the Linear DML reliability improvements.
"""

import sys
import os
import numpy as np
import pandas as pd

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.models.train_causal import CausalModelTrainer

def test_reliability_improvements():
    """Test that the reliability improvements work correctly."""
    
    print("Testing Linear DML Reliability Improvements")
    print("=" * 50)
    
    # Create synthetic test data
    np.random.seed(42)
    n_samples = 1000
    n_features = 10
    
    # Generate features
    X = np.random.randn(n_samples, n_features)
    
    # Generate binary treatment (0 or 1)
    treatment_prob = 1 / (1 + np.exp(-X[:, 0]))  # Logistic function
    T = np.random.binomial(1, treatment_prob)
    
    # Generate outcome with small treatment effect
    true_ate = 0.1  # Small but real effect
    Y = X[:, 0] + X[:, 1] + true_ate * T + np.random.randn(n_samples) * 0.1
    
    # Create DataFrame
    feature_names = [f'feature_{i}' for i in range(n_features)]
    data = pd.DataFrame(X, columns=feature_names)
    data['treatment'] = T
    data['outcome'] = Y
    
    print(f"Created synthetic dataset:")
    print(f"  Samples: {n_samples}")
    print(f"  Features: {n_features}")
    print(f"  True ATE: {true_ate}")
    print(f"  Treatment prevalence: {T.mean():.3f}")
    
    # Test the improved trainer
    trainer = CausalModelTrainer(
        data=data,
        treatment_column='treatment',
        outcome_column='outcome',
        model_name='test_model'
    )
    
    # Test different model configurations
    test_configs = [
        ('linear_dml', 'ridge', 'ridge'),
        ('linear_dml', 'random_forest', 'ridge'),
        ('causal_forest', 'random_forest', 'random_forest')
    ]
    
    results = []
    
    for model_type, outcome_model, treatment_model in test_configs:
        print(f"\nTesting {model_type} with {outcome_model}/{treatment_model}:")
        
        try:
            model = trainer.train(
                model_type=model_type,
                outcome_model=outcome_model,
                treatment_model=treatment_model
            )
            
            summary = trainer.get_model_summary()
            results.append(summary)
            
            print(f"  ✓ Success!")
            print(f"  ATE: {summary['ate']:.4f} (true: {true_ate})")
            print(f"  ATE Std: {summary['ate_std']:.4f}")
            
            if summary.get('reliability'):
                reliability = summary['reliability']
                print(f"  Reliable: {reliability.get('is_reliable', 'Unknown')}")
                if reliability.get('warnings'):
                    print(f"  Warnings: {len(reliability['warnings'])}")
                    for warning in reliability['warnings'][:2]:  # Show first 2 warnings
                        print(f"    - {warning}")
            
        except Exception as e:
            print(f"  ✗ Failed: {e}")
            results.append({'error': str(e)})
    
    print(f"\n{'='*50}")
    print("RELIABILITY TEST SUMMARY")
    print(f"{'='*50}")
    
    successful_models = [r for r in results if 'error' not in r]
    failed_models = [r for r in results if 'error' in r]
    
    print(f"Successful models: {len(successful_models)}/{len(results)}")
    print(f"Failed models: {len(failed_models)}")
    
    if successful_models:
        ates = [r['ate'] for r in successful_models if r['ate'] is not None]
        if ates:
            print(f"ATE range: [{min(ates):.4f}, {max(ates):.4f}]")
            print(f"ATE mean: {np.mean(ates):.4f}")
            print(f"True ATE: {true_ate}")
            print(f"Estimation error: {abs(np.mean(ates) - true_ate):.4f}")
    
    # Test reliability checking directly
    print(f"\nTesting reliability checking...")
    
    # Create problematic treatment effects for testing
    problematic_effects = np.array([1e8, -1e8, np.nan, np.inf])
    trainer.ate = 1e8
    trainer.ate_std = 1e9
    
    reliability = trainer._check_model_reliability(problematic_effects, 
                                                 pd.Series([0, 1, 0, 1]), 
                                                 pd.Series([0, 1, 1, 0]))
    
    print(f"  Reliability check on problematic data:")
    print(f"    Reliable: {reliability['is_reliable']}")
    print(f"    Warnings: {len(reliability['warnings'])}")
    for warning in reliability['warnings']:
        print(f"      - {warning}")
    
    print(f"\n✓ Reliability improvements test completed!")
    return results

if __name__ == "__main__":
    test_reliability_improvements()
